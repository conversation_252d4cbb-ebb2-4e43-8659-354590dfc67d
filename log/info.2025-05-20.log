[INFO] [14:36:05] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [14:36:06] [8998]  --- [o.s.b.a.l.ConditionEvaluationReportLoggingListener] --- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[INFO] [14:40:20] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [14:40:20] [8998]  --- [o.s.b.a.l.ConditionEvaluationReportLoggingListener] --- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[INFO] [14:41:22] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [14:41:22] [8998]  --- [o.s.b.a.l.ConditionEvaluationReportLoggingListener] --- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[INFO] [14:42:50] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [14:49:00] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [14:49:00] [8998]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=be977f0a-ed0d-3b92-9046-7952ef501da6
[INFO] [14:49:51] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [14:49:52] [8998]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=be977f0a-ed0d-3b92-9046-7952ef501da6
[INFO] [14:49:52] [8998]  --- [o.s.b.a.l.ConditionEvaluationReportLoggingListener] --- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[INFO] [14:54:54] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [14:54:55] [8998]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=a7d0c27f-ad26-367f-b918-0f57b7c57566
[INFO] [14:54:55] [8998]  --- [o.s.b.a.l.ConditionEvaluationReportLoggingListener] --- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[INFO] [15:00:33] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [15:00:33] [8998]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=73c82277-b46a-3992-8add-62f1a7cb3868
[INFO] [15:00:34] [8998]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port 8998 (http)
[INFO] [15:00:34] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-8998"]
[INFO] [15:00:34] [8998]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [15:00:34] [8998]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/10.1.24]
[INFO] [15:00:34] [8998]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [15:00:34] [8998]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 582 ms
[INFO] [15:00:34] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-8998"]
[INFO] [15:00:35] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Pausing ProtocolHandler ["http-nio-8998"]
[INFO] [15:00:35] [8998]  --- [o.a.catalina.core.StandardService] --- Stopping service [Tomcat]
[INFO] [15:00:35] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Stopping ProtocolHandler ["http-nio-8998"]
[INFO] [15:00:35] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Destroying ProtocolHandler ["http-nio-8998"]
[INFO] [15:00:35] [8998]  --- [o.s.b.a.l.ConditionEvaluationReportLogger] --- 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
[INFO] [15:03:19] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [15:03:19] [8998]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=73c82277-b46a-3992-8add-62f1a7cb3868
[INFO] [15:03:19] [8998]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port 8998 (http)
[INFO] [15:03:19] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-8998"]
[INFO] [15:03:19] [8998]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [15:03:19] [8998]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/10.1.24]
[INFO] [15:03:20] [8998]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [15:03:20] [8998]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 613 ms
[INFO] [15:03:20] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-8998"]
[INFO] [15:03:21] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Pausing ProtocolHandler ["http-nio-8998"]
[INFO] [15:03:21] [8998]  --- [o.a.catalina.core.StandardService] --- Stopping service [Tomcat]
[INFO] [15:03:21] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Stopping ProtocolHandler ["http-nio-8998"]
[INFO] [15:03:21] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Destroying ProtocolHandler ["http-nio-8998"]
[INFO] [15:03:21] [8998]  --- [o.s.b.a.l.ConditionEvaluationReportLogger] --- 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
[INFO] [15:03:35] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [15:03:35] [8998]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=73c82277-b46a-3992-8add-62f1a7cb3868
[INFO] [15:03:35] [8998]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port 8998 (http)
[INFO] [15:03:35] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-8998"]
[INFO] [15:03:35] [8998]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [15:03:35] [8998]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/10.1.24]
[INFO] [15:03:35] [8998]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [15:03:35] [8998]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 592 ms
[INFO] [15:03:36] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,prod} inited
[INFO] [15:03:36] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,test} inited
[INFO] [15:03:36] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [prod] success
[INFO] [15:03:36] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [test] success
[INFO] [15:03:37] [8998]  --- [o.s.b.a.w.s.WelcomePageHandlerMapping] --- Adding welcome page template: index
[INFO] [15:03:38] [8998]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [15:03:38] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-8998"]
[INFO] [15:03:38] [8998]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port 8998 (http) with context path '/'
[INFO] [15:03:39] [8998]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [15:03:39] [8998]  --- [com.mira.CopyUserApplication] --- Started CopyUserApplication in 6.579 seconds (process running for 7.266)
[INFO] [15:03:55] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [15:03:55] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [15:03:55] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [15:03:55] [8998]  --- [c.b.d.d.d.DefaultDataSourceDestroyer] --- dynamic-datasource close the datasource named [prod] success,
[INFO] [15:03:55] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [15:03:55] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [15:03:55] [8998]  --- [c.b.d.d.d.DefaultDataSourceDestroyer] --- dynamic-datasource close the datasource named [test] success,
[INFO] [15:03:55] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [15:04:32] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [15:04:32] [8998]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=73c82277-b46a-3992-8add-62f1a7cb3868
[INFO] [15:04:32] [8998]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port 8998 (http)
[INFO] [15:04:32] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-8998"]
[INFO] [15:04:32] [8998]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [15:04:32] [8998]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/10.1.24]
[INFO] [15:04:32] [8998]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [15:04:32] [8998]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 582 ms
[INFO] [15:04:32] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,prod} inited
[INFO] [15:04:32] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,test} inited
[INFO] [15:04:32] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [prod] success
[INFO] [15:04:32] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [test] success
[INFO] [15:04:33] [8998]  --- [o.s.b.a.w.s.WelcomePageHandlerMapping] --- Adding welcome page template: index
[INFO] [15:04:34] [8998]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [15:04:34] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-8998"]
[INFO] [15:04:34] [8998]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port 8998 (http) with context path '/'
[INFO] [15:04:35] [8998]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [15:04:35] [8998]  --- [com.mira.CopyUserApplication] --- Started CopyUserApplication in 6.16 seconds (process running for 6.832)
[INFO] [15:04:42] [8998]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [15:04:42] [8998]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [15:04:42] [8998]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 1 ms
[INFO] [15:04:48] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [15:04:48] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [15:04:48] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [15:04:48] [8998]  --- [c.b.d.d.d.DefaultDataSourceDestroyer] --- dynamic-datasource close the datasource named [prod] success,
[INFO] [15:04:48] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [15:04:48] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [15:04:48] [8998]  --- [c.b.d.d.d.DefaultDataSourceDestroyer] --- dynamic-datasource close the datasource named [test] success,
[INFO] [15:04:48] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
