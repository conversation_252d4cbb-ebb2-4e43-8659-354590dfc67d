[INFO] [13:40:48] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [13:40:49] [8998]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=56af9234-ef7f-33e3-a76e-d5250923bb5c
[INFO] [13:40:49] [8998]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- <PERSON><PERSON> initialized with port 8998 (http)
[INFO] [13:40:49] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-8998"]
[INFO] [13:40:49] [8998]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [13:40:49] [8998]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/10.1.24]
[INFO] [13:40:49] [8998]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [13:40:49] [8998]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 767 ms
[INFO] [13:40:49] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,prod} inited
[INFO] [13:40:49] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,test} inited
[INFO] [13:40:49] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [prod] success
[INFO] [13:40:49] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [test] success
[INFO] [13:40:49] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [test]
[INFO] [13:40:50] [8998]  --- [o.s.b.a.w.s.WelcomePageHandlerMapping] --- Adding welcome page template: index
[INFO] [13:40:51] [8998]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [13:40:51] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-8998"]
[INFO] [13:40:51] [8998]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port 8998 (http) with context path '/'
[INFO] [13:40:52] [8998]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [13:40:52] [8998]  --- [com.mira.CopyUserApplication] --- Started CopyUserApplication in 6.697 seconds (process running for 7.76)
[INFO] [13:40:56] [8998]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [13:40:56] [8998]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [13:40:56] [8998]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [13:41:51] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [13:41:51] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [13:41:51] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [13:41:51] [8998]  --- [c.b.d.d.d.DefaultDataSourceDestroyer] --- dynamic-datasource close the datasource named [prod] success,
[INFO] [13:41:51] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [13:41:51] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [13:41:51] [8998]  --- [c.b.d.d.d.DefaultDataSourceDestroyer] --- dynamic-datasource close the datasource named [test] success,
[INFO] [13:41:51] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [13:47:16] [8998]  --- [com.mira.CopyUserApplication] --- No active profile set, falling back to 1 default profile: "default"
[INFO] [13:47:16] [8998]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=56af9234-ef7f-33e3-a76e-d5250923bb5c
[INFO] [13:47:17] [8998]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port 8998 (http)
[INFO] [13:47:17] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-8998"]
[INFO] [13:47:17] [8998]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [13:47:17] [8998]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/10.1.24]
[INFO] [13:47:17] [8998]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [13:47:17] [8998]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 596 ms
[INFO] [13:47:17] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,prod} inited
[INFO] [13:47:17] [8998]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,test} inited
[INFO] [13:47:17] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [prod] success
[INFO] [13:47:17] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [test] success
[INFO] [13:47:17] [8998]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [test]
[INFO] [13:47:17] [8998]  --- [o.s.b.a.w.s.WelcomePageHandlerMapping] --- Adding welcome page template: index
[INFO] [13:47:19] [8998]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [13:47:19] [8998]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-8998"]
[INFO] [13:47:19] [8998]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port 8998 (http) with context path '/'
[INFO] [13:47:20] [8998]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [13:47:20] [8998]  --- [com.mira.CopyUserApplication] --- Started CopyUserApplication in 6.157 seconds (process running for 6.916)
[INFO] [13:47:29] [8998]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [13:47:29] [8998]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [13:47:29] [8998]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [13:48:20] [8998]  --- [c.mira.service.CopyUserServiceImpl] --- user: 494997 start copy
[INFO] [13:48:22] [8998]  --- [c.mira.service.CopyUserServiceImpl] --- copy app_user success
[INFO] [13:48:23] [8998]  --- [c.mira.service.CopyUserServiceImpl] --- copy app_user_info success
[INFO] [13:48:23] [8998]  --- [c.mira.service.CopyUserServiceImpl] --- copy app_data_manual success
[INFO] [13:48:24] [8998]  --- [c.mira.service.CopyUserServiceImpl] --- copy app_data_temperature success
