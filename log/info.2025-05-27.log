[INFO] [10:52:10] [8200]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:52:10] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:52:10] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:52:14] [8200]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-iam-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-iam.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-iam,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource.yaml,DEFAULT_GROUP'}]
[INFO] [10:52:14] [8200]  --- [com.mira.iam.biz.IAMApplication] --- The following 1 profile is active: "dev"
[INFO] [10:52:14] [8200]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.iam.biz]
[INFO] [10:52:15] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:52:15] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:52:15] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
[INFO] [10:52:15] [8200]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=8977f737-32df-339f-b5c4-beeabc49a443
[INFO] [10:52:15] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:52:15] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:52:15] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$678/0x00000008007b7840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:52:15] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:52:15] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:52:15] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$f2ecabf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:52:15] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:52:15] [8200]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 8200 (http)
[INFO] [10:52:15] [8200]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-8200"]
[INFO] [10:52:15] [8200]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:52:15] [8200]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:52:15] [8200]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:52:15] [8200]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1702 ms
[INFO] [10:52:16] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:52:16] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:52:16] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:52:16] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:52:16] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:52:17] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:52:18] [8200]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 21 endpoint(s) beneath base path '/actuator'
[INFO] [10:52:18] [8200]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@39f826ae, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3c50ba07, org.springframework.security.web.context.SecurityContextPersistenceFilter@5816fd13, org.springframework.security.web.header.HeaderWriterFilter@53a52b3f, org.springframework.security.web.authentication.logout.LogoutFilter@5b3063b7, com.mira.iam.biz.filter.OAuthClientTokenEndpointFilter@16c2c396, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@65e503a7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@64a552, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@651a3e01, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@78cb1a92, org.springframework.security.web.session.SessionManagementFilter@72bec302, org.springframework.security.web.access.ExceptionTranslationFilter@cbbc0ac, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@42017162]
[INFO] [10:52:18] [8200]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4ff2ac2d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@41862a32, org.springframework.security.web.context.SecurityContextPersistenceFilter@8d0d52a, org.springframework.security.web.header.HeaderWriterFilter@430e9d37, org.springframework.security.web.authentication.logout.LogoutFilter@7288afd8, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@4fdf83bb, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@70347322, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@2a654167, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@68183f19, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@440dc4f7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15e8e884, org.springframework.security.web.session.SessionManagementFilter@11bb0655, org.springframework.security.web.access.ExceptionTranslationFilter@503e1cd9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@13c02cec]
[INFO] [10:52:20] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:52:20] [8200]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-8200"]
[INFO] [10:52:20] [8200]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 8200 (http) with context path ''
[INFO] [10:52:20] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:52:20] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:52:22] [8200]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-iam *************:8200 register finished
[INFO] [10:52:23] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:52:23] [8200]  --- [com.mira.iam.biz.IAMApplication] --- Started IAMApplication in 14.3 seconds (JVM running for 15.137)
[INFO] [10:52:23] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=datasource.yaml, group=DEFAULT_GROUP
[INFO] [10:52:23] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam-dev.yaml, group=DEFAULT_GROUP
[INFO] [10:52:23] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam.yaml, group=DEFAULT_GROUP
[INFO] [10:52:23] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam, group=DEFAULT_GROUP
[INFO] [10:52:23] [8200]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [10:52:23] [8200]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [10:52:23] [8200]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 3 ms
[INFO] [10:53:54] [8200]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:53:54] [8200]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:53:56] [8200]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:53:56] [8200]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:53:56] [8200]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:53:56] [8200]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:53:56] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:53:56] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:53:56] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:53:56] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:53:56] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:53:56] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [10:55:44] [8200]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:55:45] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:55:45] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:55:48] [8200]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-iam-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-iam.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-iam,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource.yaml,DEFAULT_GROUP'}]
[INFO] [10:55:48] [8200]  --- [com.mira.iam.biz.IAMApplication] --- The following 1 profile is active: "dev"
[INFO] [10:55:49] [8200]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.iam.biz]
[INFO] [10:55:49] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:55:49] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:55:49] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
[INFO] [10:55:49] [8200]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=8977f737-32df-339f-b5c4-beeabc49a443
[INFO] [10:55:50] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:55:50] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:55:50] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$678/0x00000008007b7440] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:55:50] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:55:50] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:55:50] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$b9190de3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:55:50] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:55:50] [8200]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 8200 (http)
[INFO] [10:55:50] [8200]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-8200"]
[INFO] [10:55:50] [8200]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:55:50] [8200]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:55:50] [8200]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:55:50] [8200]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1585 ms
[INFO] [10:55:50] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:55:50] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:55:50] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:55:50] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:55:50] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:55:52] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:55:52] [8200]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 21 endpoint(s) beneath base path '/actuator'
[INFO] [10:55:53] [8200]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@1858f93e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2e61695b, org.springframework.security.web.context.SecurityContextPersistenceFilter@36f329e7, org.springframework.security.web.header.HeaderWriterFilter@6870f52a, org.springframework.security.web.authentication.logout.LogoutFilter@c7a7d3, com.mira.iam.biz.filter.OAuthClientTokenEndpointFilter@7e654c, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@ffa60c9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@68603829, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4567dcbc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@720f0fa2, org.springframework.security.web.session.SessionManagementFilter@224bff6, org.springframework.security.web.access.ExceptionTranslationFilter@734990c1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4f042c70]
[INFO] [10:55:53] [8200]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@50b4e828, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@63c57bde, org.springframework.security.web.context.SecurityContextPersistenceFilter@7923cb8d, org.springframework.security.web.header.HeaderWriterFilter@f7b6b9e, org.springframework.security.web.authentication.logout.LogoutFilter@70347322, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@4b68d2a6, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@6aa7ea23, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@56c75b4f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@68183f19, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3ceb8e1f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1cd25514, org.springframework.security.web.session.SessionManagementFilter@775429c8, org.springframework.security.web.access.ExceptionTranslationFilter@3fe5c9f1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5e0e97b6]
[INFO] [10:55:54] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:55:55] [8200]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-8200"]
[INFO] [10:55:55] [8200]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 8200 (http) with context path ''
[INFO] [10:55:55] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:55:55] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:55:56] [8200]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-iam *************:8200 register finished
[INFO] [10:55:57] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:55:57] [8200]  --- [com.mira.iam.biz.IAMApplication] --- Started IAMApplication in 14.207 seconds (JVM running for 14.97)
[INFO] [10:55:57] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=datasource.yaml, group=DEFAULT_GROUP
[INFO] [10:55:57] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam-dev.yaml, group=DEFAULT_GROUP
[INFO] [10:55:57] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam.yaml, group=DEFAULT_GROUP
[INFO] [10:55:57] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam, group=DEFAULT_GROUP
[INFO] [10:55:57] [8200]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [10:55:57] [8200]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [10:55:57] [8200]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 3 ms
[INFO] [10:59:04] [8200]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:59:04] [8200]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:59:06] [8200]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:59:06] [8200]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:59:06] [8200]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:59:06] [8200]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:59:06] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:59:06] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:59:06] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:59:06] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:59:06] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:59:06] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [11:09:36] [8200]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [11:09:36] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [11:09:36] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [11:09:40] [8200]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-iam-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-iam.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-iam,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource.yaml,DEFAULT_GROUP'}]
[INFO] [11:09:40] [8200]  --- [com.mira.iam.biz.IAMApplication] --- The following 1 profile is active: "dev"
[INFO] [11:09:41] [8200]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.iam.biz]
[INFO] [11:09:41] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [11:09:41] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [11:09:41] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
[INFO] [11:09:41] [8200]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=8977f737-32df-339f-b5c4-beeabc49a443
[INFO] [11:09:41] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:09:41] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:09:41] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$678/0x00000008007b7840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:09:41] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:09:41] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:09:41] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$fa64ca08] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:09:41] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:09:42] [8200]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 8200 (http)
[INFO] [11:09:42] [8200]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-8200"]
[INFO] [11:09:42] [8200]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [11:09:42] [8200]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [11:09:42] [8200]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [11:09:42] [8200]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1606 ms
[INFO] [11:09:42] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [11:09:42] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [11:09:42] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [11:09:42] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [11:09:42] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [11:09:43] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [11:09:44] [8200]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 21 endpoint(s) beneath base path '/actuator'
[INFO] [11:09:45] [8200]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@4f3eddc0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@729f8c5d, org.springframework.security.web.context.SecurityContextPersistenceFilter@61f26cba, org.springframework.security.web.header.HeaderWriterFilter@6224872f, org.springframework.security.web.authentication.logout.LogoutFilter@42c8f458, com.mira.iam.biz.filter.OAuthClientTokenEndpointFilter@1e33203f, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@7f5179be, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2e5affb3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@70921720, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@58a01e47, org.springframework.security.web.session.SessionManagementFilter@30ff3726, org.springframework.security.web.access.ExceptionTranslationFilter@18161415, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@36c6d53b]
[INFO] [11:09:45] [8200]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@66049424, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@8eb1297, org.springframework.security.web.context.SecurityContextPersistenceFilter@52b7d2d3, org.springframework.security.web.header.HeaderWriterFilter@90efbba, org.springframework.security.web.authentication.logout.LogoutFilter@19e86461, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@c0d114b, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@17d90f81, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@766f2d65, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@249a7ccc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5ab780ce, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3f2cc8ab, org.springframework.security.web.session.SessionManagementFilter@1f9a472e, org.springframework.security.web.access.ExceptionTranslationFilter@44264fb6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6d293b41]
[INFO] [11:09:46] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [11:09:47] [8200]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-8200"]
[INFO] [11:09:47] [8200]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 8200 (http) with context path ''
[INFO] [11:09:47] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [11:09:47] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [11:09:48] [8200]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-iam *************:8200 register finished
[INFO] [11:09:49] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [11:09:49] [8200]  --- [com.mira.iam.biz.IAMApplication] --- Started IAMApplication in 14.555 seconds (JVM running for 15.296)
[INFO] [11:09:49] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=datasource.yaml, group=DEFAULT_GROUP
[INFO] [11:09:49] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam-dev.yaml, group=DEFAULT_GROUP
[INFO] [11:09:49] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam.yaml, group=DEFAULT_GROUP
[INFO] [11:09:49] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam, group=DEFAULT_GROUP
[INFO] [11:09:50] [8200]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [11:09:50] [8200]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [11:09:50] [8200]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 3 ms
[INFO] [11:10:16] [8200]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [11:10:16] [8200]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [11:10:18] [8200]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [11:10:18] [8200]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [11:10:19] [8200]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [11:10:19] [8200]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [11:10:19] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [11:10:19] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [11:10:19] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [11:10:19] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [11:10:19] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [11:10:19] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
