[INFO] [10:47:40] [8000]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:47:40] [8000]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:47:40] [8000]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:47:45] [8000]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-gateway-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-gateway.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-gateway,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-api_black_white.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sys_dict.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-common.yaml,DEFAULT_GROUP'}]
[INFO] [10:47:45] [8000]  --- [com.mira.gateway.GatewayApplication] --- The following 1 profile is active: "dev"
[INFO] [10:47:45] [8000]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=b34328ab-320e-314d-94f3-531989e61c04
[INFO] [10:47:45] [8000]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:47:45] [8000]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:47:45] [8000]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:47:47] [8000]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:47:48] [8000]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [After]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Before]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Between]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Cookie]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Header]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Host]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Method]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Path]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Query]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [ReadBody]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [RemoteAddr]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Weight]
[INFO] [10:47:48] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [CloudFoundryRouteService]
[INFO] [10:47:48] [8000]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [10:47:50] [8000]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:47:50] [8000]  --- [o.s.b.w.e.netty.NettyWebServer] --- Netty started on port 8000
[INFO] [10:47:51] [8000]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:47:52] [8000]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:47:52] [8000]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:47:52] [8000]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:47:54] [8000]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-gateway *************:8000 register finished
[INFO] [10:47:54] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-gateway","groupName":"DEFAULT_GROUP","clusters":"","hosts":[{"ip":"*************","port":8000,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@mira-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}],"pluginEvent":false}, refresh cache start ...
[INFO] [10:47:54] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [10:47:54] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-gateway","groupName":"DEFAULT_GROUP","clusters":"","hosts":[{"ip":"*************","port":8000,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@mira-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}],"pluginEvent":false}, refresh cache start ...
[INFO] [10:47:54] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [10:47:54] [8000]  --- [c.a.c.n.d.GatewayLocatorHeartBeatPublisher] --- Start nacos gateway locator heartBeat task scheduler.
[INFO] [10:47:54] [8000]  --- [com.mira.gateway.GatewayApplication] --- Started GatewayApplication in 15.928 seconds (JVM running for 16.825)
[INFO] [10:47:55] [8000]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-gateway.yaml, group=DEFAULT_GROUP
[INFO] [10:47:55] [8000]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=common.yaml, group=DEFAULT_GROUP
[INFO] [10:47:55] [8000]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-gateway, group=DEFAULT_GROUP
[INFO] [10:47:55] [8000]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=sys_dict.yaml, group=DEFAULT_GROUP
[INFO] [10:47:55] [8000]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=api_black_white.yaml, group=DEFAULT_GROUP
[INFO] [10:47:55] [8000]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-gateway-dev.yaml, group=DEFAULT_GROUP
