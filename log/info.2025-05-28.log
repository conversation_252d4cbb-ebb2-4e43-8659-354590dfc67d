[INFO] [18:39:59] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [18:39:59] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [18:39:59] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [18:40:02] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [18:40:02] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [18:40:03] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [18:40:03] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [18:40:03] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [18:40:03] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
[INFO] [18:40:03] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=8c6d75bc-6eb8-3ce4-b72b-d2373dd080eb
[INFO] [18:40:03] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:40:03] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:40:03] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$690/0x000000080079c040] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:40:03] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:40:03] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:40:03] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$6a2fafea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:40:03] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:40:04] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [18:40:04] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [18:40:04] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [18:40:04] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [18:40:04] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [18:40:04] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1530 ms
[INFO] [18:40:04] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [18:40:04] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [18:40:04] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [18:40:04] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [18:40:04] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [18:40:06] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@1c298769, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4cb1af3f, org.springframework.security.web.context.SecurityContextPersistenceFilter@7b3b763d, org.springframework.security.web.header.HeaderWriterFilter@3199c2c1, org.springframework.security.web.authentication.logout.LogoutFilter@36821a1b, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@7acb8e6b, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@********, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@7902b006, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@59be715a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7c7335dc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@32bff23d, org.springframework.security.web.session.SessionManagementFilter@17f8c75e, org.springframework.security.web.access.ExceptionTranslationFilter@7798de95, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@14b53f58]
[INFO] [18:40:06] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@57082d16 with [org.springframework.security.web.session.DisableEncodeUrlFilter@ae2227, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3a1eb893, org.springframework.security.web.context.SecurityContextPersistenceFilter@5c807e6b, org.springframework.security.web.header.HeaderWriterFilter@34bcdc9f, org.springframework.security.web.authentication.logout.LogoutFilter@796affb8, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@34a8a5ad, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@5891bd6d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@49bcd90d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1bb6bc81, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@35daccb8, org.springframework.security.web.session.SessionManagementFilter@76296c86, org.springframework.security.web.access.ExceptionTranslationFilter@1428d63, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@46092840]
[INFO] [18:40:06] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@e1f17e6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@249f95a2, org.springframework.security.web.context.SecurityContextPersistenceFilter@dc9033f, org.springframework.security.web.header.HeaderWriterFilter@5c0bacaa, org.springframework.security.web.authentication.logout.LogoutFilter@1e7b277a, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@5d81b90a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2c5dfc9b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2db27b46, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5f36ba0f, org.springframework.security.web.session.SessionManagementFilter@7cb15360, org.springframework.security.web.access.ExceptionTranslationFilter@7aeb7bd6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@446d7669]
[INFO] [18:40:07] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [18:40:07] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [18:40:08] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [18:40:08] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [18:40:08] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [18:40:09] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api 192.168.0.102:9100 register finished
[INFO] [18:40:09] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [18:40:09] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 10.318 seconds (JVM running for 11.172)
[INFO] [18:40:09] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [18:40:09] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [18:40:09] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [18:40:10] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [18:40:10] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [18:40:10] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 3 ms
[INFO] [18:40:58] [9100] 6836e83aa109cf99040c51231a8be651 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/login
[INFO] [18:41:52] [9100] 6836e86fc663e320076049f606bbfd8b --- [o.s.s.o.p.e.AuthorizationEndpoint] --- Handling OAuth2 error: error="invalid_grant", error_description="Invalid redirect: https://localhost:420/trackers/mira does not match one of the registered values."
[INFO] [18:41:52] [9100] 6836e86fc663e320076049f606bbfd8b --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/error
Params: response_type=code&redirect_uri=https://localhost:420/trackers/mira&client_id=biocanic_dF9XvesiwgfLf8GvjelyA1d&scope=clinic%20doctor%20patient
[INFO] [18:43:04] [9100] 6836e8b7f2b90552fc0d13858ec36976 --- [o.s.s.o.p.e.AuthorizationEndpoint] --- Handling OAuth2 error: error="invalid_grant", error_description="Invalid redirect: https://localhost:420/trackers/mira does not match one of the registered values."
[INFO] [18:43:04] [9100] 6836e8b7f2b90552fc0d13858ec36976 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/error
Params: response_type=code&redirect_uri=https://localhost:420/trackers/mira&client_id=biocanic_dF9XvesiwgfLf8GvjelyA1d&scope=clinic%20doctor%20patient
[INFO] [18:43:36] [9100] 6836e8d817c6c5a0c7ab1be45a249e3c --- [o.s.s.o.p.e.AuthorizationEndpoint] --- Handling OAuth2 error: error="invalid_grant", error_description="Invalid redirect: https://localhost:420/trackers/mira does not match one of the registered values."
[INFO] [18:43:45] [9100] 6836e8d817c6c5a0c7ab1be45a249e3c --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/error
Params: response_type=code&redirect_uri=https://localhost:420/trackers/mira&client_id=biocanic_dF9XvesiwgfLf8GvjelyA1d&scope=clinic%20doctor%20patient
[INFO] [18:44:10] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [18:44:10] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [18:44:10] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [18:44:10] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [18:44:10] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [18:44:10] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [18:44:10] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [18:44:10] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [18:44:10] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [18:44:10] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [18:44:10] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [18:44:10] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [18:49:13] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [18:49:13] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [18:49:13] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [18:49:16] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [18:49:16] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [18:49:17] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [18:49:17] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [18:49:17] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [18:49:17] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[INFO] [18:49:17] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=8c6d75bc-6eb8-3ce4-b72b-d2373dd080eb
[INFO] [18:49:17] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:49:17] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:49:17] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$690/0x000000080079c040] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:49:17] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:49:17] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:49:17] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$56b21eba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:49:17] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:49:17] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [18:49:17] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [18:49:17] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [18:49:17] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [18:49:18] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [18:49:18] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1546 ms
[INFO] [18:49:18] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [18:49:18] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [18:49:18] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [18:49:18] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [18:49:18] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [18:49:19] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@39da0e47, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@55b56db3, org.springframework.security.web.context.SecurityContextPersistenceFilter@3b68a50c, org.springframework.security.web.header.HeaderWriterFilter@7edab945, org.springframework.security.web.authentication.logout.LogoutFilter@1bb6bc81, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@4e387da1, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@1c697ca0, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@6c96e1d5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5f51f320, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@294a150c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2af5eab6, org.springframework.security.web.session.SessionManagementFilter@5bf9bb2, org.springframework.security.web.access.ExceptionTranslationFilter@79dcd7f1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5572be5a]
[INFO] [18:49:20] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@ace45e9 with [org.springframework.security.web.session.DisableEncodeUrlFilter@5c943847, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@352b5f3c, org.springframework.security.web.context.SecurityContextPersistenceFilter@6fb87b73, org.springframework.security.web.header.HeaderWriterFilter@5b8e2ea7, org.springframework.security.web.authentication.logout.LogoutFilter@785aeba9, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@fa3db48, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@5b6b5888, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5f2788f2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@75f67ea7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@24305eca, org.springframework.security.web.session.SessionManagementFilter@6b994b71, org.springframework.security.web.access.ExceptionTranslationFilter@2762e9a7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@32647dde]
[INFO] [18:49:20] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@544ee437, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@ffd26d1, org.springframework.security.web.context.SecurityContextPersistenceFilter@76b49d0, org.springframework.security.web.header.HeaderWriterFilter@7d26b390, org.springframework.security.web.authentication.logout.LogoutFilter@77f43f3e, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@379ddab2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@223c42b0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6acedf54, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7dfc0fda, org.springframework.security.web.session.SessionManagementFilter@6ff5eeed, org.springframework.security.web.access.ExceptionTranslationFilter@4f3d49e4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5e4a9c16]
[INFO] [18:49:21] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [18:49:21] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [18:49:21] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [18:49:21] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [18:49:21] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [18:49:22] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api 192.168.0.102:9100 register finished
[INFO] [18:49:22] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [18:49:22] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 9.718 seconds (JVM running for 10.473)
[INFO] [18:49:22] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [18:49:22] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [18:49:22] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [18:49:23] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [18:49:23] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [18:49:23] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [18:49:35] [9100] 6836ea3e17fb7d83c1c07cb83785e9c7 --- [o.s.s.o.p.e.AuthorizationEndpoint] --- Handling OAuth2 error: error="invalid_grant", error_description="Invalid redirect: https://localhost:420/trackers/mira does not match one of the registered values."
[INFO] [18:49:35] [9100] 6836ea3e17fb7d83c1c07cb83785e9c7 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/error
Params: response_type=code&redirect_uri=https://localhost:420/trackers/mira&client_id=biocanic_dF9XvesiwgfLf8GvjelyA1d&scope=clinic%20doctor%20patient
[INFO] [18:51:38] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [18:51:38] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [18:51:38] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [18:51:38] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [18:51:38] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [18:51:38] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [18:51:39] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [18:51:39] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [18:51:39] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [18:51:39] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [18:51:39] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [18:51:39] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [18:51:43] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [18:51:43] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [18:51:43] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [18:51:46] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [18:51:46] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [18:51:47] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [18:51:47] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [18:51:47] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [18:51:47] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[INFO] [18:51:47] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=8c6d75bc-6eb8-3ce4-b72b-d2373dd080eb
[INFO] [18:51:48] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:51:48] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:51:48] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$690/0x00000008007a7840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:51:48] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:51:48] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:51:48] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$8d566d1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:51:48] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [18:51:48] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [18:51:48] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [18:51:48] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [18:51:48] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [18:51:48] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [18:51:48] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1477 ms
[INFO] [18:51:48] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [18:51:48] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [18:51:48] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [18:51:48] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [18:51:48] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [18:51:51] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@255845b1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@59e02113, org.springframework.security.web.context.SecurityContextPersistenceFilter@455082d2, org.springframework.security.web.header.HeaderWriterFilter@221c097f, org.springframework.security.web.authentication.logout.LogoutFilter@53785d1a, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@372dc92e, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@59a79420, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@45ec6bb3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@55145dc5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@15045e40, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4a77d2a5, org.springframework.security.web.session.SessionManagementFilter@49bcd90d, org.springframework.security.web.access.ExceptionTranslationFilter@60610a2b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@75f67ea7]
[INFO] [18:51:51] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@4b954cbb with [org.springframework.security.web.session.DisableEncodeUrlFilter@bec2d81, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5f04449a, org.springframework.security.web.context.SecurityContextPersistenceFilter@76b49d0, org.springframework.security.web.header.HeaderWriterFilter@7d26b390, org.springframework.security.web.authentication.logout.LogoutFilter@77f43f3e, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@141cee26, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@354d5692, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@223c42b0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6acedf54, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5634c26c, org.springframework.security.web.session.SessionManagementFilter@6ff5eeed, org.springframework.security.web.access.ExceptionTranslationFilter@4f3d49e4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4c18b432]
[INFO] [18:51:51] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@28446b06, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2cf7860e, org.springframework.security.web.context.SecurityContextPersistenceFilter@1acd660d, org.springframework.security.web.header.HeaderWriterFilter@5aaecd25, org.springframework.security.web.authentication.logout.LogoutFilter@22aaa811, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@1df6f57d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@22f3b213, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e18848d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@439b893a, org.springframework.security.web.session.SessionManagementFilter@239fdf8f, org.springframework.security.web.access.ExceptionTranslationFilter@6f8af186, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@59da32ad]
[INFO] [18:51:52] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [18:51:52] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [18:51:52] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [18:51:52] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [18:51:52] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [18:51:53] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api 192.168.0.102:9100 register finished
[INFO] [18:51:54] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [18:51:54] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 11.12 seconds (JVM running for 11.887)
[INFO] [18:51:54] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [18:51:54] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [18:51:54] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [18:51:54] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [18:51:54] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [18:51:54] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [18:53:53] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [18:53:53] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [18:53:53] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [18:53:53] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [18:53:53] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [18:53:53] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [18:53:54] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [18:53:54] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [18:53:54] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [18:53:54] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [18:53:54] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [18:53:54] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
