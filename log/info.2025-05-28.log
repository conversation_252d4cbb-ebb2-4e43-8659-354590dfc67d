[INFO] [11:13:59] [8200]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [11:13:59] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [11:13:59] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [11:14:03] [8200]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-iam-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-iam.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-iam,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource.yaml,DEFAULT_GROUP'}]
[INFO] [11:14:03] [8200]  --- [com.mira.iam.biz.IAMApplication] --- The following 1 profile is active: "dev"
[INFO] [11:14:04] [8200]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.iam.biz]
[INFO] [11:14:04] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [11:14:04] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [11:14:04] [8200]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
[INFO] [11:14:05] [8200]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=8977f737-32df-339f-b5c4-beeabc49a443
[INFO] [11:14:05] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:14:05] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:14:05] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$676/0x00000008007aa440] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:14:05] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:14:05] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:14:05] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$b2ef8f8d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:14:05] [8200]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:14:05] [8200]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 8200 (http)
[INFO] [11:14:05] [8200]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-8200"]
[INFO] [11:14:05] [8200]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [11:14:05] [8200]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [11:14:05] [8200]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [11:14:05] [8200]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 2023 ms
[INFO] [11:14:06] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [11:14:06] [8200]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [11:14:06] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [11:14:06] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [11:14:06] [8200]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [11:14:07] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [11:14:09] [8200]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 21 endpoint(s) beneath base path '/actuator'
[INFO] [11:14:09] [8200]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@69765d94, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@62f25e32, org.springframework.security.web.context.SecurityContextPersistenceFilter@6413fef3, org.springframework.security.web.header.HeaderWriterFilter@60e5d4fb, org.springframework.security.web.authentication.logout.LogoutFilter@301af154, com.mira.iam.biz.filter.OAuthClientTokenEndpointFilter@44a1e5d2, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@5ab780ce, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1334a246, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@33bd86fe, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@48adb967, org.springframework.security.web.session.SessionManagementFilter@2c427287, org.springframework.security.web.access.ExceptionTranslationFilter@321b2469, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3005133e]
[INFO] [11:14:09] [8200]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6ddf3c5e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@68fac558, org.springframework.security.web.context.SecurityContextPersistenceFilter@3f2cc8ab, org.springframework.security.web.header.HeaderWriterFilter@8eb1297, org.springframework.security.web.authentication.logout.LogoutFilter@58968733, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@4f042c70, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@22ef1086, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@1822f675, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7dcbcede, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2de4045a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@34883fa2, org.springframework.security.web.session.SessionManagementFilter@766f2d65, org.springframework.security.web.access.ExceptionTranslationFilter@66049424, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5f4ebecd]
[INFO] [11:14:11] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [11:14:11] [8200]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-8200"]
[INFO] [11:14:11] [8200]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 8200 (http) with context path ''
[INFO] [11:14:11] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [11:14:11] [8200]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [11:14:13] [8200]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-iam *************:8200 register finished
[INFO] [11:14:14] [8200]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [11:14:14] [8200]  --- [com.mira.iam.biz.IAMApplication] --- Started IAMApplication in 16.405 seconds (JVM running for 17.517)
[INFO] [11:14:14] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=datasource.yaml, group=DEFAULT_GROUP
[INFO] [11:14:14] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam-dev.yaml, group=DEFAULT_GROUP
[INFO] [11:14:14] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam.yaml, group=DEFAULT_GROUP
[INFO] [11:14:14] [8200]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-iam, group=DEFAULT_GROUP
[INFO] [11:14:14] [8200]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [11:14:14] [8200]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [11:14:14] [8200]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 3 ms
