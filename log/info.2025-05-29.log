[INFO] [10:23:07] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:23:07] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:23:07] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:23:11] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [10:23:11] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [10:23:12] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [10:23:12] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:23:12] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:23:12] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[INFO] [10:23:12] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=8c6d75bc-6eb8-3ce4-b72b-d2373dd080eb
[INFO] [10:23:12] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:23:12] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:23:12] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007af840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:23:12] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:23:12] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:23:12] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$1d1b437b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:23:12] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:23:13] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [10:23:13] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [10:23:13] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:23:13] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:23:13] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:23:13] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1553 ms
[INFO] [10:23:13] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:23:13] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:23:13] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:23:13] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:23:13] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:23:15] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:23:16] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@5435582e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@56daf3b1, org.springframework.security.web.context.SecurityContextPersistenceFilter@358caf3a, org.springframework.security.web.header.HeaderWriterFilter@********, org.springframework.security.web.authentication.logout.LogoutFilter@470a2845, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@15363eba, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@7ba0c0e5, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@6be93728, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5a32ab89, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7b0a864e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1ee2e363, org.springframework.security.web.session.SessionManagementFilter@36821a1b, org.springframework.security.web.access.ExceptionTranslationFilter@26753687, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@dcf495c]
[INFO] [10:23:16] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@2e5d40a4 with [org.springframework.security.web.session.DisableEncodeUrlFilter@56aacc7b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@112d94d, org.springframework.security.web.context.SecurityContextPersistenceFilter@2394fad2, org.springframework.security.web.header.HeaderWriterFilter@24c04958, org.springframework.security.web.authentication.logout.LogoutFilter@46092840, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@5f324d9f, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@a885aa1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@53785d1a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@75d454a4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6e832c9, org.springframework.security.web.session.SessionManagementFilter@649a76c1, org.springframework.security.web.access.ExceptionTranslationFilter@70201785, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@434896b0]
[INFO] [10:23:16] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@19bdfa3e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@78e3bebd, org.springframework.security.web.context.SecurityContextPersistenceFilter@7fd65f9a, org.springframework.security.web.header.HeaderWriterFilter@1ca574db, org.springframework.security.web.authentication.logout.LogoutFilter@3bc2146c, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@717c00f9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@f48a79a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@38fc1904, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4f0b02a3, org.springframework.security.web.session.SessionManagementFilter@5323b690, org.springframework.security.web.access.ExceptionTranslationFilter@5c658163, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@8ff4795]
[INFO] [10:23:18] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:23:18] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [10:23:18] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [10:23:18] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [10:23:18] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:23:18] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:23:20] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [10:23:21] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:23:22] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [10:23:22] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 15.979 seconds (JVM running for 16.911)
[INFO] [10:23:22] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [10:23:22] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [10:23:22] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [10:23:22] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [10:23:22] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [10:23:22] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [10:23:40] [9100] 6837c52b34134d637905013dd67a8f32 --- [o.s.s.o.p.e.AuthorizationEndpoint] --- Handling OAuth2 error: error="unsupported_response_type", error_description="Unsupported response types: [aaa]"
[INFO] [10:25:31] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:25:31] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:25:33] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:25:33] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:25:34] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:25:34] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:25:34] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:25:34] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:25:34] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:25:34] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:25:34] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:25:34] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [10:25:39] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:25:39] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:25:39] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:25:43] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [10:25:43] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [10:25:44] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [10:25:44] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:25:44] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:25:44] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[INFO] [10:25:44] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=8c6d75bc-6eb8-3ce4-b72b-d2373dd080eb
[INFO] [10:25:44] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:25:44] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:25:44] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007af840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:25:44] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:25:44] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:25:44] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$5277eeb3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:25:44] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:25:44] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [10:25:44] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [10:25:44] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:25:44] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:25:45] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:25:45] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1480 ms
[INFO] [10:25:45] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:25:45] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:25:45] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:25:45] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:25:45] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:25:47] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:25:47] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@27c2f134, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@64bed8b2, org.springframework.security.web.context.SecurityContextPersistenceFilter@32bff23d, org.springframework.security.web.header.HeaderWriterFilter@5ee63d9, org.springframework.security.web.authentication.logout.LogoutFilter@55145dc5, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@15045e40, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@2555b92, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@55b56db3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2fef413c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6042b613, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@793f2b41, org.springframework.security.web.session.SessionManagementFilter@502326b3, org.springframework.security.web.access.ExceptionTranslationFilter@4c97e3d5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@299003eb]
[INFO] [10:25:48] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@76b49d0 with [org.springframework.security.web.session.DisableEncodeUrlFilter@223c42b0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6acedf54, org.springframework.security.web.context.SecurityContextPersistenceFilter@b0e903a, org.springframework.security.web.header.HeaderWriterFilter@61ce2d47, org.springframework.security.web.authentication.logout.LogoutFilter@2c7e2c5, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@4a77d2a5, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@62df1f0e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2ca2fcb5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3c964873, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2a0cc666, org.springframework.security.web.session.SessionManagementFilter@184b3575, org.springframework.security.web.access.ExceptionTranslationFilter@5abfb698, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6ff5eeed]
[INFO] [10:25:48] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2700609f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@141cee26, org.springframework.security.web.context.SecurityContextPersistenceFilter@67a6f2c4, org.springframework.security.web.header.HeaderWriterFilter@6f05d5f2, org.springframework.security.web.authentication.logout.LogoutFilter@17c95612, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@1ca2d595, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6bac199f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2a47fb5d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@ecd3a9b, org.springframework.security.web.session.SessionManagementFilter@3acae925, org.springframework.security.web.access.ExceptionTranslationFilter@688c914e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4a282c78]
[INFO] [10:25:50] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:25:50] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [10:25:50] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [10:25:50] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [10:25:50] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:25:50] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:25:52] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:25:52] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:25:52] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:25:52] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:25:52] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:25:52] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:25:52] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [10:25:54] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Pausing ProtocolHandler ["http-nio-9100"]
[INFO] [10:25:54] [9100]  --- [o.a.catalina.core.StandardService] --- Stopping service [Tomcat]
[INFO] [10:25:54] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Stopping ProtocolHandler ["http-nio-9100"]
[INFO] [10:25:54] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Destroying ProtocolHandler ["http-nio-9100"]
[INFO] [10:25:54] [9100]  --- [o.s.b.a.l.ConditionEvaluationReportLoggingListener] --- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[INFO] [10:26:11] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:26:11] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:26:11] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:26:15] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [10:26:15] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [10:26:15] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [10:26:15] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:26:15] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:26:15] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[INFO] [10:26:16] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=8c6d75bc-6eb8-3ce4-b72b-d2373dd080eb
[INFO] [10:26:16] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:26:16] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:26:16] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007af840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:26:16] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:26:16] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:26:16] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$7e115e75] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:26:16] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:26:16] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [10:26:16] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [10:26:16] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:26:16] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:26:16] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:26:16] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1460 ms
[INFO] [10:26:16] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:26:17] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:26:17] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:26:17] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:26:17] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:26:18] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:26:19] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@7ba0c0e5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1ee2e363, org.springframework.security.web.context.SecurityContextPersistenceFilter@72288b38, org.springframework.security.web.header.HeaderWriterFilter@5f7e2ca8, org.springframework.security.web.authentication.logout.LogoutFilter@21e72fb2, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@2258ca14, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@60e83cb9, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@1a177f2c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2693b289, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@106c4a72, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@612e1c6e, org.springframework.security.web.session.SessionManagementFilter@5a2e4e77, org.springframework.security.web.access.ExceptionTranslationFilter@307cf289, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@420dee82]
[INFO] [10:26:19] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@5f2788f2 with [org.springframework.security.web.session.DisableEncodeUrlFilter@75f67ea7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@549561ab, org.springframework.security.web.context.SecurityContextPersistenceFilter@372dc92e, org.springframework.security.web.header.HeaderWriterFilter@5bee07c3, org.springframework.security.web.authentication.logout.LogoutFilter@243b8682, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@53bc8c7e, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@3d3a3738, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3063493e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5f36ba0f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@785aeba9, org.springframework.security.web.session.SessionManagementFilter@f2bec99, org.springframework.security.web.access.ExceptionTranslationFilter@5c0bacaa, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@36a65069]
[INFO] [10:26:19] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@24f6f6bf, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@255845b1, org.springframework.security.web.context.SecurityContextPersistenceFilter@2ca2fcb5, org.springframework.security.web.header.HeaderWriterFilter@184b3575, org.springframework.security.web.authentication.logout.LogoutFilter@39bbe17c, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@3760f3e8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3c964873, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7db72209, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@59e02113, org.springframework.security.web.session.SessionManagementFilter@b0e903a, org.springframework.security.web.access.ExceptionTranslationFilter@61ce2d47, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@420849f6]
[INFO] [10:26:21] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:26:21] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [10:26:21] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [10:26:21] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [10:26:21] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:26:21] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:26:24] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [10:26:25] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:26:25] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [10:26:25] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 15.733 seconds (JVM running for 16.614)
[INFO] [10:26:25] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [10:26:25] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [10:26:25] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [10:26:25] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [10:26:25] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [10:26:25] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [10:26:32] [9100] 6837c5d710a5ba83389b6acf28462df3 --- [o.s.s.o.p.e.AuthorizationEndpoint] --- Handling OAuth2 error: error="unsupported_response_type", error_description="Unsupported response types: [aaa]"
[INFO] [10:28:12] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:28:12] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:28:14] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:28:14] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:28:14] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:28:14] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:28:14] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:28:14] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:28:14] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:28:14] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:28:14] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:28:14] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [10:35:18] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:35:18] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:35:18] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:35:22] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [10:35:22] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [10:35:22] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [10:35:23] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:35:23] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:35:23] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[INFO] [10:35:23] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=39678291-946f-3119-a427-c24ad57874d4
[INFO] [10:35:23] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:35:23] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:35:23] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007af840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:35:23] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:35:23] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:35:23] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$1f2c763a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:35:23] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:35:23] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [10:35:23] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [10:35:23] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:35:23] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:35:23] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:35:23] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1642 ms
[INFO] [10:35:24] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:35:24] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:35:24] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:35:24] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:35:24] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:35:26] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:35:26] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@700b9a54, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c658163, org.springframework.security.web.context.SecurityContextPersistenceFilter@74691a1f, org.springframework.security.web.header.HeaderWriterFilter@20395afe, org.springframework.security.web.authentication.logout.LogoutFilter@4cb1af3f, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@20de9df, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@1ca574db, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@243b8682, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@8872669, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3dfef314, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5323b690, org.springframework.security.web.session.SessionManagementFilter@3c92f2f9, org.springframework.security.web.access.ExceptionTranslationFilter@5e774546, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2aa143ba]
[INFO] [10:35:26] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@5410bbdf with [org.springframework.security.web.session.DisableEncodeUrlFilter@323dd422, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2f229f60, org.springframework.security.web.context.SecurityContextPersistenceFilter@2762e9a7, org.springframework.security.web.header.HeaderWriterFilter@5bad04d1, org.springframework.security.web.authentication.logout.LogoutFilter@39bbe17c, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@48c42253, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@24305eca, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@75f67ea7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@22fb60f3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3cdf8aa5, org.springframework.security.web.session.SessionManagementFilter@397fced4, org.springframework.security.web.access.ExceptionTranslationFilter@27c2f134, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5e4a9c16]
[INFO] [10:35:26] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6cf58989, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3a1dc432, org.springframework.security.web.context.SecurityContextPersistenceFilter@21ad0060, org.springframework.security.web.header.HeaderWriterFilter@ecd3a9b, org.springframework.security.web.authentication.logout.LogoutFilter@15a8b228, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@5a69f1ef, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5233b7ad, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@354d5692, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4c18b432, org.springframework.security.web.session.SessionManagementFilter@58d9cd6, org.springframework.security.web.access.ExceptionTranslationFilter@141cee26, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2c9573f1]
[INFO] [10:35:28] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:35:29] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [10:35:29] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [10:35:29] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [10:35:29] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:35:29] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:35:30] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [10:35:31] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:35:32] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [10:35:32] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 15.026 seconds (JVM running for 15.994)
[INFO] [10:35:32] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [10:35:32] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [10:35:32] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [10:35:32] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [10:35:32] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [10:35:32] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 3 ms
[INFO] [10:37:55] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:37:55] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:37:57] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:37:57] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:37:58] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:37:58] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:37:58] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:37:58] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:37:58] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:37:58] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:37:58] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:37:58] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [10:38:02] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:38:02] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:38:02] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:38:06] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [10:38:06] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [10:38:06] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [10:38:07] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:38:07] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:38:07] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[INFO] [10:38:07] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=39678291-946f-3119-a427-c24ad57874d4
[INFO] [10:38:07] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:38:07] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:38:07] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007b7840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:38:07] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:38:07] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:38:07] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$6a2fafea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:38:07] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:38:07] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [10:38:07] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [10:38:07] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:38:07] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:38:07] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:38:07] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1502 ms
[INFO] [10:38:08] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:38:08] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:38:08] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:38:08] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:38:08] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:38:10] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:38:10] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@2fef413c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4a0b1701, org.springframework.security.web.context.SecurityContextPersistenceFilter@6859f102, org.springframework.security.web.header.HeaderWriterFilter@5f0bcadb, org.springframework.security.web.authentication.logout.LogoutFilter@72288b38, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@36b5dbdf, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@7b9661d4, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@14f1db20, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6a8a54f5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@26753687, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@20de9df, org.springframework.security.web.session.SessionManagementFilter@461f295c, org.springframework.security.web.access.ExceptionTranslationFilter@3199c2c1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@72a4f3f]
[INFO] [10:38:10] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@7aeb7bd6 with [org.springframework.security.web.session.DisableEncodeUrlFilter@5c0bacaa, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7cb15360, org.springframework.security.web.context.SecurityContextPersistenceFilter@1a177f2c, org.springframework.security.web.header.HeaderWriterFilter@6be93728, org.springframework.security.web.authentication.logout.LogoutFilter@4e387da1, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@66373f77, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@5d9b79a2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@c82f6bd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2e70d072, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@dc9033f, org.springframework.security.web.session.SessionManagementFilter@455082d2, org.springframework.security.web.access.ExceptionTranslationFilter@784dd9da, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3c79be0f]
[INFO] [10:38:10] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@732f7660, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7d525b45, org.springframework.security.web.context.SecurityContextPersistenceFilter@5f324d9f, org.springframework.security.web.header.HeaderWriterFilter@48ac4201, org.springframework.security.web.authentication.logout.LogoutFilter@372dc92e, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@a885aa1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b7b5e7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2aa143ba, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3a0afe5b, org.springframework.security.web.session.SessionManagementFilter@f2bec99, org.springframework.security.web.access.ExceptionTranslationFilter@635791b7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@76ad0058]
[INFO] [10:38:12] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:38:13] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [10:38:13] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [10:38:13] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [10:38:13] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:38:13] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:38:14] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [10:38:15] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:38:16] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [10:38:16] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 14.784 seconds (JVM running for 15.596)
[INFO] [10:38:16] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [10:38:16] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [10:38:16] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [10:38:16] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [10:38:16] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [10:38:16] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 1 ms
[INFO] [10:39:45] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:39:45] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:39:47] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:39:47] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:39:48] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:39:48] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:39:48] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:39:48] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:39:48] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:39:48] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:39:48] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:39:48] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [10:39:53] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:39:53] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:39:53] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:39:57] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [10:39:57] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [10:39:57] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [10:39:58] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:39:58] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:39:58] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
[INFO] [10:39:58] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=4df39b44-f63b-392c-8ff6-de31ee677106
[INFO] [10:39:58] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:39:58] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:39:58] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007ad040] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:39:58] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:39:58] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:39:58] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$30310d7c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:39:58] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:39:58] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [10:39:58] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [10:39:58] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:39:58] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:39:58] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:39:58] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1527 ms
[INFO] [10:39:59] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:39:59] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:39:59] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:39:59] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:39:59] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:40:00] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:40:01] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@2555b92, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@793f2b41, org.springframework.security.web.context.SecurityContextPersistenceFilter@23f4aaeb, org.springframework.security.web.header.HeaderWriterFilter@27c68ccb, org.springframework.security.web.authentication.logout.LogoutFilter@2e6066e1, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@2574123a, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@49442e03, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@2af5eab6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7b9661d4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@757501da, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5bad04d1, org.springframework.security.web.session.SessionManagementFilter@14f1db20, org.springframework.security.web.access.ExceptionTranslationFilter@5d6e77a4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@59d99269]
[INFO] [10:40:01] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@6acedf54 with [org.springframework.security.web.session.DisableEncodeUrlFilter@2a0cc666, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@77f43f3e, org.springframework.security.web.context.SecurityContextPersistenceFilter@3c964873, org.springframework.security.web.header.HeaderWriterFilter@b0e903a, org.springframework.security.web.authentication.logout.LogoutFilter@3760f3e8, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@75552781, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@1b25ebea, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7db72209, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2c7e2c5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@379ddab2, org.springframework.security.web.session.SessionManagementFilter@2ca2fcb5, org.springframework.security.web.access.ExceptionTranslationFilter@184b3575, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@223c42b0]
[INFO] [10:40:01] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@ecd3a9b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@58d9cd6, org.springframework.security.web.context.SecurityContextPersistenceFilter@2a47fb5d, org.springframework.security.web.header.HeaderWriterFilter@67a6f2c4, org.springframework.security.web.authentication.logout.LogoutFilter@92a584f, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@36e23af, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@47a9bcf1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@17c95612, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21ad0060, org.springframework.security.web.session.SessionManagementFilter@6bac199f, org.springframework.security.web.access.ExceptionTranslationFilter@3acae925, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@141cee26]
[INFO] [10:40:03] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:40:04] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [10:40:04] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [10:40:04] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [10:40:04] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:40:04] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:40:05] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [10:40:06] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:40:06] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [10:40:06] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 15.266 seconds (JVM running for 16.071)
[INFO] [10:40:07] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [10:40:07] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [10:40:07] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [10:40:07] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [10:40:07] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [10:40:07] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [10:40:13] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:40:13] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:40:15] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:40:15] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:40:15] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:40:15] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:40:16] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:40:16] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:40:16] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:40:16] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:40:16] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:40:16] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [10:40:32] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:40:32] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:40:32] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:40:36] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [10:40:36] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [10:40:37] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [10:40:37] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:40:37] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:40:37] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
[INFO] [10:40:37] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=5460a669-b95f-39f5-8843-56489160415c
[INFO] [10:40:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:40:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:40:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007af840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:40:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:40:38] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:40:38] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c637b2dc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:40:38] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:40:38] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [10:40:38] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [10:40:38] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:40:38] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:40:38] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:40:38] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1524 ms
[INFO] [10:40:38] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:40:38] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:40:38] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:40:38] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:40:38] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:40:40] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:40:41] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@62df1f0e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3e6a55fb, org.springframework.security.web.context.SecurityContextPersistenceFilter@331886ac, org.springframework.security.web.header.HeaderWriterFilter@1ee2e363, org.springframework.security.web.authentication.logout.LogoutFilter@b7b5e7, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@e1f17e6, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@1b25ebea, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@72a1ccad, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5d9b79a2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@42cf08a1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7aead7d9, org.springframework.security.web.session.SessionManagementFilter@2574123a, org.springframework.security.web.access.ExceptionTranslationFilter@6f77917c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@61ce2d47]
[INFO] [10:40:41] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@4b954cbb with [org.springframework.security.web.session.DisableEncodeUrlFilter@bec2d81, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5f04449a, org.springframework.security.web.context.SecurityContextPersistenceFilter@311d8595, org.springframework.security.web.header.HeaderWriterFilter@2b35976a, org.springframework.security.web.authentication.logout.LogoutFilter@67a6f2c4, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@141cee26, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@354d5692, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@688c914e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6f05d5f2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5634c26c, org.springframework.security.web.session.SessionManagementFilter@67c64b9c, org.springframework.security.web.access.ExceptionTranslationFilter@379ddab2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4c18b432]
[INFO] [10:40:41] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@28446b06, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2cf7860e, org.springframework.security.web.context.SecurityContextPersistenceFilter@1acd660d, org.springframework.security.web.header.HeaderWriterFilter@5aaecd25, org.springframework.security.web.authentication.logout.LogoutFilter@22aaa811, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@1df6f57d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@22f3b213, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e18848d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@439b893a, org.springframework.security.web.session.SessionManagementFilter@239fdf8f, org.springframework.security.web.access.ExceptionTranslationFilter@6f8af186, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@59da32ad]
[INFO] [10:40:43] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:40:43] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [10:40:43] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [10:40:43] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [10:40:43] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:40:43] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:40:45] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [10:40:46] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:40:46] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [10:40:46] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 15.085 seconds (JVM running for 15.931)
[INFO] [10:40:46] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [10:40:46] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [10:40:46] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [10:40:46] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [10:40:46] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [10:40:46] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 3 ms
[INFO] [10:42:10] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:42:10] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:42:12] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:42:12] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:42:13] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:42:13] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:42:13] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:42:13] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:42:13] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:42:13] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:42:13] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:42:13] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [10:42:18] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:42:19] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:42:19] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:42:22] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [10:42:22] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [10:42:23] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [10:42:23] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:42:23] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:42:23] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
[INFO] [10:42:23] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=5460a669-b95f-39f5-8843-56489160415c
[INFO] [10:42:24] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:42:24] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:42:24] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007af840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:42:24] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:42:24] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:42:24] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$19d3c480] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:42:24] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:42:24] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [10:42:24] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [10:42:24] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:42:24] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:42:24] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:42:24] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1515 ms
[INFO] [10:42:24] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:42:24] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:42:24] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:42:24] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:42:24] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:42:26] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:42:27] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@4ba88ab5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d2ec8b2, org.springframework.security.web.context.SecurityContextPersistenceFilter@17f8c75e, org.springframework.security.web.header.HeaderWriterFilter@573f95b2, org.springframework.security.web.authentication.logout.LogoutFilter@4872d04d, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@5fb8ad3d, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@43905ade, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@672c6b93, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7ae736a8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@424769f2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@502326b3, org.springframework.security.web.session.SessionManagementFilter@134b9d9c, org.springframework.security.web.access.ExceptionTranslationFilter@2e78213c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@74691a1f]
[INFO] [10:42:27] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@24c04958 with [org.springframework.security.web.session.DisableEncodeUrlFilter@649a76c1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2394fad2, org.springframework.security.web.context.SecurityContextPersistenceFilter@2b1aa390, org.springframework.security.web.header.HeaderWriterFilter@4d162d67, org.springframework.security.web.authentication.logout.LogoutFilter@1428d63, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@56daf3b1, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@1cf45f46, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5e1d6ace, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@221c097f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@53785d1a, org.springframework.security.web.session.SessionManagementFilter@757501da, org.springframework.security.web.access.ExceptionTranslationFilter@6042b613, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@70201785]
[INFO] [10:42:27] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@a885aa1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2a9464d9, org.springframework.security.web.context.SecurityContextPersistenceFilter@52e296c0, org.springframework.security.web.header.HeaderWriterFilter@35daccb8, org.springframework.security.web.authentication.logout.LogoutFilter@7aeb7bd6, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@5c0bacaa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1d77762f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7e2bd635, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21cb907d, org.springframework.security.web.session.SessionManagementFilter@23ac3788, org.springframework.security.web.access.ExceptionTranslationFilter@3a1eb893, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@372dc92e]
[INFO] [10:42:29] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:42:29] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [10:42:29] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [10:42:29] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [10:42:29] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:42:29] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:42:31] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [10:42:32] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:42:32] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [10:42:32] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 14.949 seconds (JVM running for 16.209)
[INFO] [10:42:32] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [10:42:32] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [10:42:32] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [10:42:32] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [10:42:32] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [10:42:32] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 3 ms
[INFO] [10:42:35] [9100] 6837c999e1c9e11dfaa7d5f64863cc34 --- [o.s.s.o.p.e.AuthorizationEndpoint] --- Handling OAuth2 error: error="unsupported_response_type", error_description="Unsupported response types: [aaa]"
[INFO] [10:42:46] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:42:46] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:42:48] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:42:48] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:42:49] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:42:49] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:42:49] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:42:49] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:42:49] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:42:49] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:42:49] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:42:49] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [10:45:41] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [10:45:41] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:45:41] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:45:45] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [10:45:45] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [10:45:45] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [10:45:46] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [10:45:46] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [10:45:46] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
[INFO] [10:45:46] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=5460a669-b95f-39f5-8843-56489160415c
[INFO] [10:45:46] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:45:46] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:45:46] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$693/0x00000008007a8c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:45:46] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:45:46] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:45:46] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$3e6a5d24] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:45:46] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [10:45:46] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [10:45:46] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [10:45:46] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [10:45:46] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [10:45:46] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [10:45:46] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1329 ms
[INFO] [10:45:47] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [10:45:47] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [10:45:47] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [10:45:47] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [10:45:47] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [10:45:48] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:45:49] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@188caeaf, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@67de5328, org.springframework.security.web.context.SecurityContextPersistenceFilter@231d5119, org.springframework.security.web.header.HeaderWriterFilter@7ddd5715, org.springframework.security.web.authentication.logout.LogoutFilter@4086e003, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@5ad67d82, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@4b05a9b1, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@201c1d99, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@40ef1198, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7dc16d33, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@296b758, org.springframework.security.web.session.SessionManagementFilter@27bfa57f, org.springframework.security.web.access.ExceptionTranslationFilter@5565d718, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@69fc5803]
[INFO] [10:45:49] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@70376c6 with [org.springframework.security.web.session.DisableEncodeUrlFilter@2e89310c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@55c75b72, org.springframework.security.web.context.SecurityContextPersistenceFilter@6230a15a, org.springframework.security.web.header.HeaderWriterFilter@10bf2185, org.springframework.security.web.authentication.logout.LogoutFilter@6b0f50d8, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@3bc82aa3, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@3b910a4b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7225f871, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1bff4cb9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@567412db, org.springframework.security.web.session.SessionManagementFilter@14c06f50, org.springframework.security.web.access.ExceptionTranslationFilter@15c0a8ad, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@582b6362]
[INFO] [10:45:49] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@49825659, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2ab0ca04, org.springframework.security.web.context.SecurityContextPersistenceFilter@676beb9f, org.springframework.security.web.header.HeaderWriterFilter@60eacbbf, org.springframework.security.web.authentication.logout.LogoutFilter@35cc5b75, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@466a5b80, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3a892b9f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c88a842, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@53c68ea5, org.springframework.security.web.session.SessionManagementFilter@36e6e59f, org.springframework.security.web.access.ExceptionTranslationFilter@61a7930e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@65c97eeb]
[INFO] [10:45:51] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:45:51] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [10:45:51] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [10:45:51] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [10:45:51] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [10:45:51] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [10:45:53] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [10:45:54] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [10:45:54] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [10:45:54] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 14.498 seconds (JVM running for 14.984)
[INFO] [10:45:54] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [10:45:54] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [10:45:54] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [10:45:55] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [10:45:55] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [10:45:55] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [10:45:58] [9100] 6837ca6611d2448a9f2b485515ebf216 --- [o.s.s.o.p.e.AuthorizationEndpoint] --- Handling OAuth2 error: error="unsupported_response_type", error_description="Unsupported response types: [aaa]"
[INFO] [10:46:05] [9100] 6837ca6d89eec36c5c2422dbe4e417cc --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/login
[INFO] [10:46:59] [9100] 6837caa3333232af815a1b8442d7ffbe --- [c.m.o.interceptor.LogInterceptor] --- URL: /v1/cycle
[INFO] [10:47:04] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:47:04] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:47:06] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:47:06] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:47:06] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:47:06] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [10:47:06] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [10:47:06] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [10:47:06] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [10:47:06] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [10:47:06] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [10:47:06] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [11:23:32] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [11:23:32] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [11:23:32] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [11:23:36] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [11:23:36] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [11:23:36] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [11:23:37] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [11:23:37] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [11:23:37] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[INFO] [11:23:37] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=6a2e2ce8-a96e-3f24-a850-1e02c060d73c
[INFO] [11:23:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:23:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:23:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007af840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:23:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:23:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:23:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$1ff8bc17] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:23:37] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [11:23:37] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [11:23:37] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [11:23:37] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [11:23:37] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [11:23:37] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [11:23:37] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1560 ms
[INFO] [11:23:38] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [11:23:38] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [11:23:38] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [11:23:38] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [11:23:38] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [11:23:40] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [11:23:40] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@649a76c1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2394fad2, org.springframework.security.web.context.SecurityContextPersistenceFilter@4df5b562, org.springframework.security.web.header.HeaderWriterFilter@63f9c2cd, org.springframework.security.web.authentication.logout.LogoutFilter@6f67291f, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@46f43f50, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@53785d1a, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@834944, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2182ebc7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7cb4fc46, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@75d454a4, org.springframework.security.web.session.SessionManagementFilter@402d443d, org.springframework.security.web.access.ExceptionTranslationFilter@8dcc25, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@55145dc5]
[INFO] [11:23:40] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@58e9375b with [org.springframework.security.web.session.DisableEncodeUrlFilter@d7d984c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@53bc8c7e, org.springframework.security.web.context.SecurityContextPersistenceFilter@7abe3d53, org.springframework.security.web.header.HeaderWriterFilter@b7b5e7, org.springframework.security.web.authentication.logout.LogoutFilter@21cb907d, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@112d94d, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@124ba047, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@372dc92e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@a885aa1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@8ff4795, org.springframework.security.web.session.SessionManagementFilter@2aa143ba, org.springframework.security.web.access.ExceptionTranslationFilter@5f324d9f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6c96e1d5]
[INFO] [11:23:40] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2026af0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@31c9bb2f, org.springframework.security.web.context.SecurityContextPersistenceFilter@1c697ca0, org.springframework.security.web.header.HeaderWriterFilter@39da0e47, org.springframework.security.web.authentication.logout.LogoutFilter@6a49b3c7, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@6f6c4462, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2af5eab6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@43347199, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@71e839ee, org.springframework.security.web.session.SessionManagementFilter@55b56db3, org.springframework.security.web.access.ExceptionTranslationFilter@2e530f34, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@397fced4]
[INFO] [11:23:42] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [11:23:43] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [11:23:43] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [11:23:43] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [11:23:43] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [11:23:43] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [11:23:44] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [11:23:45] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [11:23:45] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [11:23:45] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 14.452 seconds (JVM running for 15.42)
[INFO] [11:23:45] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [11:23:45] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [11:23:45] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [11:23:45] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [11:23:45] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [11:23:45] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [11:24:00] [9100] 6837d350d0435d5d36f57876178e4440 --- [c.m.o.interceptor.LogInterceptor] --- URL: /v1/cycle
[INFO] [11:25:19] [9100] 6837d39e6ea28f47351e2554cbcf1401 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/redirect_uri/biocanic_dF9XvesiwgfLf8GvjelyA1d
[INFO] [11:25:28] [9100] 6837d3a847523d96d22621b6697b9bd1 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/redirect_uri/biocanic_dF9XvesiwgfLf8GvjelyA1d
[INFO] [11:28:40] [9100] 6837d468e02ebf62df0069c2208c7af0 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/login
[INFO] [12:40:42] [9100]  --- [i.l.core.protocol.ConnectionWatchdog] --- Reconnecting, last destination was /************:6379
[INFO] [12:40:42] [9100]  --- [i.l.c.protocol.ReconnectionHandler] --- Reconnected to ************:6379
[INFO] [13:00:24] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [13:00:24] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [13:12:48] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [13:12:48] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [13:12:48] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [13:12:52] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [13:12:52] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [13:12:52] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [13:12:52] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [13:12:52] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [13:12:52] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
[INFO] [13:12:53] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=3061f585-c28c-3e56-a4b3-4d786392ee00
[INFO] [13:12:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:12:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:12:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007af840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:12:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:12:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:12:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$cb939a91] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:12:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:12:53] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [13:12:53] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [13:12:53] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [13:12:53] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [13:12:53] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [13:12:53] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1573 ms
[INFO] [13:12:54] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [13:12:54] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [13:12:54] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [13:12:54] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [13:12:54] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [13:12:56] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [13:12:56] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@294a150c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2fef413c, org.springframework.security.web.context.SecurityContextPersistenceFilter@29c471f6, org.springframework.security.web.header.HeaderWriterFilter@6a17ab87, org.springframework.security.web.authentication.logout.LogoutFilter@2fc72df3, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@7c5ae5c3, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@4a0b1701, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@5f51f320, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@40a1916d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6f36c42, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7b9661d4, org.springframework.security.web.session.SessionManagementFilter@34ceefb5, org.springframework.security.web.access.ExceptionTranslationFilter@134b9d9c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@43edd4e0]
[INFO] [13:12:57] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@3c79be0f with [org.springframework.security.web.session.DisableEncodeUrlFilter@7aeb7bd6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c0bacaa, org.springframework.security.web.context.SecurityContextPersistenceFilter@455082d2, org.springframework.security.web.header.HeaderWriterFilter@784dd9da, org.springframework.security.web.authentication.logout.LogoutFilter@6f77917c, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@331886ac, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@47c8c176, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1a177f2c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c82f6bd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7cb15360, org.springframework.security.web.session.SessionManagementFilter@6be93728, org.springframework.security.web.access.ExceptionTranslationFilter@56313529, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7e2bd635]
[INFO] [13:12:57] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@76ad0058, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@732f7660, org.springframework.security.web.context.SecurityContextPersistenceFilter@f2bec99, org.springframework.security.web.header.HeaderWriterFilter@635791b7, org.springframework.security.web.authentication.logout.LogoutFilter@7abe3d53, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@372dc92e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5f324d9f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@b7b5e7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7d525b45, org.springframework.security.web.session.SessionManagementFilter@48ac4201, org.springframework.security.web.access.ExceptionTranslationFilter@5d81b90a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7e772d11]
[INFO] [13:12:59] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [13:12:59] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [13:12:59] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [13:12:59] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [13:12:59] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [13:12:59] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [13:13:01] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [13:13:02] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [13:13:02] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [13:13:02] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 15.035 seconds (JVM running for 16.703)
[INFO] [13:13:02] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [13:13:02] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [13:13:02] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [13:13:03] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [13:13:03] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [13:13:03] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 3 ms
[INFO] [13:14:06] [9100] 6837ed1e3d0b84dbc9af2549947623f4 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/token
[INFO] [13:14:45] [9100] 6837ed451f5b66e5fbe69252d33a88c6 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/token
[INFO] [13:15:03] [9100] 6837ed5675d83297f300c8b4062bbe16 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/token
[INFO] [13:15:24] [9100] 6837ed6c069b70c070b40cf6e86e2450 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/login
[INFO] [13:16:20] [9100] 6837eda4913becdb2267fed7ad414743 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/token
[INFO] [13:16:31] [9100] 6837edaf5582ac0b1970547d9cf3fc8f --- [c.m.o.interceptor.LogInterceptor] --- URL: /v1/cycle
[INFO] [13:23:04] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [13:23:04] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [13:23:06] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [13:23:06] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [13:23:06] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [13:23:06] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [13:23:06] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [13:23:06] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [13:23:06] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [13:23:06] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [13:23:06] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [13:23:06] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [13:29:03] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [13:29:03] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [13:29:03] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [13:29:07] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [13:29:07] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [13:29:07] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [13:29:07] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [13:29:07] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [13:29:08] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
[INFO] [13:29:08] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=83383b68-d427-3db2-890c-58dd561da5bf
[INFO] [13:29:08] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:29:08] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:29:08] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007b7840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:29:08] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:29:08] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:29:08] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$bbb2710a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:29:08] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [13:29:08] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [13:29:08] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [13:29:08] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [13:29:08] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [13:29:08] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [13:29:08] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1544 ms
[INFO] [13:29:09] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [13:29:09] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [13:29:09] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [13:29:09] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [13:29:09] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [13:29:10] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [13:29:11] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@48ac4201, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@f2bec99, org.springframework.security.web.context.SecurityContextPersistenceFilter@2258ca14, org.springframework.security.web.header.HeaderWriterFilter@470a2845, org.springframework.security.web.authentication.logout.LogoutFilter@545bc8e1, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@43edd4e0, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@5f324d9f, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@3c79be0f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6a7eef27, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1243ad9a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@b7b5e7, org.springframework.security.web.session.SessionManagementFilter@64b6d6dd, org.springframework.security.web.access.ExceptionTranslationFilter@6c633590, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@47c8c176]
[INFO] [13:29:11] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@793f2b41 with [org.springframework.security.web.session.DisableEncodeUrlFilter@49442e03, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5bad04d1, org.springframework.security.web.context.SecurityContextPersistenceFilter@56aacc7b, org.springframework.security.web.header.HeaderWriterFilter@434896b0, org.springframework.security.web.authentication.logout.LogoutFilter@423ddfe1, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@38bb297, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@8ff4795, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@112d94d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6e832c9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@730c4dfd, org.springframework.security.web.session.SessionManagementFilter@2e5d40a4, org.springframework.security.web.access.ExceptionTranslationFilter@2caf6813, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2555b92]
[INFO] [13:29:11] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3760f3e8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@73032867, org.springframework.security.web.context.SecurityContextPersistenceFilter@2762e9a7, org.springframework.security.web.header.HeaderWriterFilter@71e839ee, org.springframework.security.web.authentication.logout.LogoutFilter@5f2788f2, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@75f67ea7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b8e2ea7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b994b71, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4e6f3d08, org.springframework.security.web.session.SessionManagementFilter@36a65069, org.springframework.security.web.access.ExceptionTranslationFilter@31c9bb2f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@39bbe17c]
[INFO] [13:29:13] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [13:29:13] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [13:29:14] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [13:29:14] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [13:29:14] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [13:29:14] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [13:29:15] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [13:29:16] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [13:29:16] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [13:29:16] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 14.412 seconds (JVM running for 15.348)
[INFO] [13:29:16] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [13:29:16] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [13:29:16] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [13:29:17] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [13:29:17] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [13:29:17] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [13:29:58] [9100] 6837f0d54873892a84a4b928d1837c69 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/token
[INFO] [13:33:56] [9100] 6837f1c330ac12242fa37fc8aa339e30 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/token
[INFO] [13:37:19] [9100] 6837f28fbeee597f85634c435cb2609f --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/login
[INFO] [13:37:46] [9100] 6837f2aa3a7481cafc143e2892e0e454 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/token
[INFO] [13:38:59] [9100] 6837f2f311dc440196f696fd37fb7de5 --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/token
[INFO] [14:13:40] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [14:13:40] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [14:13:42] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [14:13:42] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [14:13:42] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [14:13:42] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [14:13:42] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [14:13:42] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [14:13:42] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [14:13:42] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [14:13:42] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [14:13:42] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [14:13:48] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [14:13:48] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [14:13:48] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [14:13:51] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [14:13:51] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [14:13:52] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [14:13:52] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [14:13:52] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [14:13:52] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[INFO] [14:13:52] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=83383b68-d427-3db2-890c-58dd561da5bf
[INFO] [14:13:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:13:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:13:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007af840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:13:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:13:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:13:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$7e600523] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:13:53] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:13:53] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [14:13:53] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [14:13:53] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [14:13:53] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [14:13:53] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [14:13:53] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1558 ms
[INFO] [14:13:53] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [14:13:53] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [14:13:53] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [14:13:53] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [14:13:53] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [14:13:55] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [14:13:56] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@1e7b277a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5d81b90a, org.springframework.security.web.context.SecurityContextPersistenceFilter@930b6f9, org.springframework.security.web.header.HeaderWriterFilter@193b6f5b, org.springframework.security.web.authentication.logout.LogoutFilter@3d26c82d, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@740610a2, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@635791b7, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@52e296c0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@13ac7281, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@447cdbaa, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@48ac4201, org.springframework.security.web.session.SessionManagementFilter@219a9b16, org.springframework.security.web.access.ExceptionTranslationFilter@4df5b562, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@834944]
[INFO] [14:13:56] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@27c2f134 with [org.springframework.security.web.session.DisableEncodeUrlFilter@64bed8b2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2555b92, org.springframework.security.web.context.SecurityContextPersistenceFilter@2caf6813, org.springframework.security.web.header.HeaderWriterFilter@3a0afe5b, org.springframework.security.web.authentication.logout.LogoutFilter@112d94d, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@1afea182, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@58e9375b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@434896b0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2e5d40a4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@793f2b41, org.springframework.security.web.session.SessionManagementFilter@243b8682, org.springframework.security.web.access.ExceptionTranslationFilter@7d525b45, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@a9e31e8]
[INFO] [14:13:56] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7db72209, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2c7e2c5, org.springframework.security.web.context.SecurityContextPersistenceFilter@31c9bb2f, org.springframework.security.web.header.HeaderWriterFilter@397fced4, org.springframework.security.web.authentication.logout.LogoutFilter@5b8e2ea7, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@6b994b71, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@71e839ee, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@36a65069, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@39bbe17c, org.springframework.security.web.session.SessionManagementFilter@2026af0, org.springframework.security.web.access.ExceptionTranslationFilter@280d1d8d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3c964873]
[INFO] [14:13:58] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [14:13:58] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [14:13:58] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [14:13:58] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [14:13:58] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [14:13:58] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [14:14:00] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [14:14:01] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [14:14:01] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [14:14:01] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 14.45 seconds (JVM running for 16.048)
[INFO] [14:14:01] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [14:14:01] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [14:14:01] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [14:14:01] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [14:14:01] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [14:14:01] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [14:17:05] [9100] 6837fbe05236ae3a0de8115300a51d6c --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/token
[INFO] [14:18:02] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [14:18:02] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [14:18:04] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [14:18:04] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [14:18:04] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [14:18:04] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [14:18:05] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [14:18:05] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [14:18:05] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [14:18:05] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [14:18:05] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [14:18:05] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
[INFO] [14:18:09] [9100]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [14:18:09] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [14:18:09] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [14:18:13] [9100]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-open-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-open-api,DEFAULT_GROUP'}]
[INFO] [14:18:13] [9100]  --- [com.mira.openapi.OpenApiApplication] --- The following 1 profile is active: "dev"
[INFO] [14:18:13] [9100]  --- [c.g.l.r.s.b.c.AutoConfiguredRetrofitScannerRegistrar] --- Scan the @RetrofitClient annotated interface using the auto-configuration package. packages=[com.mira.openapi]
[INFO] [14:18:13] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Multiple Spring Data modules found, entering strict repository configuration mode!
[INFO] [14:18:13] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO] [14:18:13] [9100]  --- [o.s.d.r.c.RepositoryConfigurationDelegate] --- Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[INFO] [14:18:14] [9100]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=83383b68-d427-3db2-890c-58dd561da5bf
[INFO] [14:18:14] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:18:14] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:18:14] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$692/0x00000008007af840] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:18:14] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:18:14] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:18:14] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$fa3cc21a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:18:14] [9100]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [14:18:14] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat initialized with port(s): 9100 (http)
[INFO] [14:18:14] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Initializing ProtocolHandler ["http-nio-9100"]
[INFO] [14:18:14] [9100]  --- [o.a.catalina.core.StandardService] --- Starting service [Tomcat]
[INFO] [14:18:14] [9100]  --- [o.a.catalina.core.StandardEngine] --- Starting Servlet engine: [Apache Tomcat/9.0.63]
[INFO] [14:18:14] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring embedded WebApplicationContext
[INFO] [14:18:14] [9100]  --- [o.s.b.w.s.c.ServletWebServerApplicationContext] --- Root WebApplicationContext: initialization completed in 1614 ms
[INFO] [14:18:15] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1,master} inited
[INFO] [14:18:15] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2,slave} inited
[INFO] [14:18:15] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [slave] success
[INFO] [14:18:15] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource - add a datasource named [master] success
[INFO] [14:18:15] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
[INFO] [14:18:17] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [14:18:17] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@331886ac, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@66373f77, org.springframework.security.web.context.SecurityContextPersistenceFilter@63f9c2cd, org.springframework.security.web.header.HeaderWriterFilter@2aee0173, org.springframework.security.web.authentication.logout.LogoutFilter@13ac7281, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@447cdbaa, com.mira.openapi.filter.OAuthClientTokenEndpointFilter@834944, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@612e1c6e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@402d443d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c5dc3db, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6f014f27, org.springframework.security.web.session.SessionManagementFilter@66c58c03, org.springframework.security.web.access.ExceptionTranslationFilter@34adce96, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@49bcd90d]
[INFO] [14:18:17] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration$NotOAuthRequestMatcher@3bc2146c with [org.springframework.security.web.session.DisableEncodeUrlFilter@717c00f9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@299003eb, org.springframework.security.web.context.SecurityContextPersistenceFilter@7cb15360, org.springframework.security.web.header.HeaderWriterFilter@7aeb7bd6, org.springframework.security.web.authentication.logout.LogoutFilter@5bee07c3, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@611d867, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@732f7660, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@dc9033f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2c5dfc9b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@72139933, org.springframework.security.web.session.SessionManagementFilter@5c0bacaa, org.springframework.security.web.access.ExceptionTranslationFilter@3c79be0f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7625bfbd]
[INFO] [14:18:17] [9100]  --- [o.s.s.web.DefaultSecurityFilterChain] --- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@75c2a35, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@a9e31e8, org.springframework.security.web.context.SecurityContextPersistenceFilter@6c96e1d5, org.springframework.security.web.header.HeaderWriterFilter@52a1d6f, org.springframework.security.web.authentication.logout.LogoutFilter@8ff4795, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@19bdfa3e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@58e9375b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@d7d984c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@27c2f134, org.springframework.security.web.session.SessionManagementFilter@38bb297, org.springframework.security.web.access.ExceptionTranslationFilter@4bcff08c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@20ac726c]
[INFO] [14:18:20] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [14:18:20] [9100]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [14:18:20] [9100]  --- [o.a.coyote.http11.Http11NioProtocol] --- Starting ProtocolHandler ["http-nio-9100"]
[INFO] [14:18:20] [9100]  --- [o.s.b.w.e.tomcat.TomcatWebServer] --- Tomcat started on port(s): 9100 (http) with context path ''
[INFO] [14:18:20] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [14:18:20] [9100]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [14:18:21] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-open-api *************:9100 register finished
[INFO] [14:18:22] [9100]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [14:18:22] [9100]  --- [o.s.s.a.ScheduledAnnotationBeanPostProcessor] --- No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[INFO] [14:18:22] [9100]  --- [com.mira.openapi.OpenApiApplication] --- Started OpenApiApplication in 14.684 seconds (JVM running for 15.548)
[INFO] [14:18:22] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api.yaml, group=DEFAULT_GROUP
[INFO] [14:18:22] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api, group=DEFAULT_GROUP
[INFO] [14:18:22] [9100]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-open-api-dev.yaml, group=DEFAULT_GROUP
[INFO] [14:18:23] [9100]  --- [o.a.c.c.C.[Tomcat].[localhost].[/]] --- Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO] [14:18:23] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Initializing Servlet 'dispatcherServlet'
[INFO] [14:18:23] [9100]  --- [o.s.web.servlet.DispatcherServlet] --- Completed initialization in 2 ms
[INFO] [14:18:29] [9100] 6837fc35bd89d5035f01dbb9063c451c --- [c.m.o.interceptor.LogInterceptor] --- URL: /oauth/token
[INFO] [14:20:34] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [14:20:34] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [14:20:36] [9100]  --- [o.s.b.w.e.tomcat.GracefulShutdown] --- Graceful shutdown complete
[INFO] [14:20:36] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [14:20:36] [9100]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [14:20:36] [9100]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
[INFO] [14:20:36] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource start closing ....
[INFO] [14:20:36] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closing ...
[INFO] [14:20:36] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-2} closed
[INFO] [14:20:36] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closing ...
[INFO] [14:20:36] [9100]  --- [c.alibaba.druid.pool.DruidDataSource] --- {dataSource-1} closed
[INFO] [14:20:36] [9100]  --- [c.b.d.d.DynamicRoutingDataSource] --- dynamic-datasource all closed success,bye
