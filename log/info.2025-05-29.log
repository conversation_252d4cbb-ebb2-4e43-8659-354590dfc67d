[INFO] [09:50:35] [8000]  --- [o.h.validator.internal.util.Version] --- HV000001: Hibernate Validator 6.2.3.Final
[INFO] [09:50:36] [8000]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [09:50:36] [8000]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [09:50:40] [8000]  --- [o.s.c.b.c.PropertySourceBootstrapConfiguration] --- Located property source: [BootstrapPropertySource {name='bootstrapProperties-mira-gateway-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-gateway.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-mira-gateway,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-api_black_white.yaml,DEFAULT_GROUP'}]
[INFO] [09:50:40] [8000]  --- [com.mira.gateway.GatewayApplication] --- The following 1 profile is active: "dev"
[INFO] [09:50:41] [8000]  --- [o.s.cloud.context.scope.GenericScope] --- BeanFactory id=17373c96-a10c-3278-bcf7-ea17cb52dcc9
[INFO] [09:50:41] [8000]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [09:50:41] [8000]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [09:50:41] [8000]  --- [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] --- Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO] [09:50:45] [8000]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [09:50:46] [8000]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [After]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Before]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Between]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Cookie]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Header]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Host]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Method]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Path]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Query]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [ReadBody]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [RemoteAddr]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [Weight]
[INFO] [09:50:46] [8000]  --- [o.s.c.g.r.RouteDefinitionRouteLocator] --- Loaded RoutePredicateFactory [CloudFoundryRouteService]
[INFO] [09:50:46] [8000]  --- [o.s.b.a.e.web.EndpointLinksResolver] --- Exposing 22 endpoint(s) beneath base path '/actuator'
[INFO] [09:50:48] [8000]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [09:50:49] [8000]  --- [o.s.b.w.e.netty.NettyWebServer] --- Netty started on port 8000
[INFO] [09:50:50] [8000]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [09:50:51] [8000]  --- [o.s.cloud.commons.util.InetUtils] --- Cannot determine local hostname
[INFO] [09:50:51] [8000]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO] [09:50:51] [8000]  --- [c.a.n.p.a.s.c.ClientAuthPluginManager] --- [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO] [09:50:52] [8000]  --- [c.a.c.n.r.NacosServiceRegistry] --- nacos registry, DEFAULT_GROUP mira-gateway *************:8000 register finished
[INFO] [09:50:53] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-gateway","groupName":"DEFAULT_GROUP","clusters":"","hosts":[{"ip":"*************","port":8000,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@mira-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}],"pluginEvent":false}, refresh cache start ...
[INFO] [09:50:53] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [09:50:53] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-script","groupName":"DEFAULT_GROUP","clusters":"","hosts":[{"ip":"*************","port":8079,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@mira-script","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2409:8a28:e83:6bd4:7804:e35a:401e:2]"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"ip":"**************","port":8079,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@mira-script","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}],"pluginEvent":false}, refresh cache start ...
[INFO] [09:50:53] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [09:50:54] [8000]  --- [c.a.c.n.d.GatewayLocatorHeartBeatPublisher] --- Start nacos gateway locator heartBeat task scheduler.
[INFO] [09:50:54] [8000]  --- [com.mira.gateway.GatewayApplication] --- Started GatewayApplication in 19.578 seconds (JVM running for 21.165)
[INFO] [09:50:54] [8000]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-gateway.yaml, group=DEFAULT_GROUP
[INFO] [09:50:54] [8000]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-gateway, group=DEFAULT_GROUP
[INFO] [09:50:54] [8000]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=api_black_white.yaml, group=DEFAULT_GROUP
[INFO] [09:50:54] [8000]  --- [c.a.c.n.r.NacosContextRefresher] --- [Nacos Config] Listening config: dataId=mira-gateway-dev.yaml, group=DEFAULT_GROUP
[INFO] [09:51:24] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-iam","groupName":"DEFAULT_GROUP","clusters":"","hosts":[{"ip":"*************","port":8200,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@mira-iam","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}],"pluginEvent":false}, refresh cache start ...
[INFO] [09:51:24] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [09:51:48] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-bluetooth","groupName":"DEFAULT_GROUP","clusters":"","hosts":[{"ip":"*************","port":8084,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@mira-bluetooth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}],"pluginEvent":false}, refresh cache start ...
[INFO] [09:51:48] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [09:51:48] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-sso","groupName":"DEFAULT_GROUP","clusters":"","hosts":[{"ip":"*************","port":8090,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@mira-sso","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}],"pluginEvent":false}, refresh cache start ...
[INFO] [09:51:48] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [09:51:48] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-user","groupName":"DEFAULT_GROUP","clusters":"","hosts":[{"ip":"*************","port":8081,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@mira-user","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}],"pluginEvent":false}, refresh cache start ...
[INFO] [09:51:48] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [09:53:25] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v5/survey/getId, user type: 0, duration: 749ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:53:27] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/home-banner/list, user type: 0, duration: 2605ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:53:28] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/sys/error-log, user type: 0, duration: 2243ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:53:29] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v5/home/<USER>
[INFO] [09:53:29] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/sys/error-log, user type: 0, duration: 2208ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:53:29] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v5/home/<USER>
[INFO] [09:53:58] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/comparison/chat, user type: 0, duration: 161ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:53:58] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/h5/common-config/, user type: 0, duration: 348ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:53:58] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/calendar/sex-config, user type: 0, duration: 523ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:53:59] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/calendar/sex-config, user type: 0, duration: 1001ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:53:59] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/custom-log/config/info, user type: 0, duration: 1023ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:54:00] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v5/calendar/data, user type: 0, duration: 1759ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:54:00] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v5/calendar/day-log, user type: 0, duration: 2319ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:54:49] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/cycle-analysis/view, user type: 0, duration: 409ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:54:58] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/chart/analysis-line, user type: 0, duration: 1679ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:55:00] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/chart/tips, user type: 0, duration: 546ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:55:01] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v5/chart/analysis-line, user type: 0, duration: 1136ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:55:01] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v4/sys/error-log, user type: 0, duration: 1149ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [09:55:02] [8000]  --- [c.m.g.filter.GatewayGlobalFilter] --- path: /app/v5/chart/aggregation, user type: 0, duration: 1779ms, cient ip: 127.0.0.1, status: success, message: request success
[INFO] [10:04:01] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-sso","groupName":"DEFAULT_GROUP","clusters":"","hosts":[],"pluginEvent":false}, refresh cache start ...
[INFO] [10:04:01] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [10:04:01] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-bluetooth","groupName":"DEFAULT_GROUP","clusters":"","hosts":[],"pluginEvent":false}, refresh cache start ...
[INFO] [10:04:01] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [10:04:01] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway receive refresh event: {"serviceName":"mira-user","groupName":"DEFAULT_GROUP","clusters":"","hosts":[],"pluginEvent":false}, refresh cache start ...
[INFO] [10:04:01] [8000]  --- [c.m.g.c.NacosInstancesChangeEventListener] --- spring gateway refresh finish.
[INFO] [10:21:55] [8000]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Flushing remaining spans on shutdown
[INFO] [10:21:55] [8000]  --- [o.s.b.w.e.netty.GracefulShutdown] --- Commencing graceful shutdown. Waiting for active requests to complete
[INFO] [10:21:55] [8000]  --- [o.s.b.w.e.netty.GracefulShutdown] --- Graceful shutdown complete
[INFO] [10:21:55] [8000]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registering from Nacos Server now...
[INFO] [10:21:55] [8000]  --- [c.a.c.n.r.NacosServiceRegistry] --- De-registration finished.
[INFO] [10:21:55] [8000]  --- [com.mira.core.util.ThreadPoolUtil] --- ThreadPoolUtil destroy ...
