[WARN] [10:52:14] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam.yaml] & group[DEFAULT_GROUP]
[WARN] [10:52:14] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:52:14] [8200]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.iam.biz]' package. Please check your configuration.
[WARN] [10:52:16] [8200]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:52:18] [8200]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:52:20] [8200]  --- [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] --- Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[WARN] [10:52:20] [8200]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:52:23] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [10:52:23] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [10:52:23] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [10:52:23] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [10:53:54] [8200]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:53:54] [8200]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:53:54] [8200]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:53:54] [8200]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [10:55:48] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam.yaml] & group[DEFAULT_GROUP]
[WARN] [10:55:48] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:55:49] [8200]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.iam.biz]' package. Please check your configuration.
[WARN] [10:55:50] [8200]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:55:53] [8200]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:55:54] [8200]  --- [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] --- Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[WARN] [10:55:55] [8200]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:55:57] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [10:55:57] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [10:55:57] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [10:55:57] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [10:59:04] [8200]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:59:04] [8200]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:59:04] [8200]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:59:04] [8200]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [11:09:40] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam.yaml] & group[DEFAULT_GROUP]
[WARN] [11:09:40] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [11:09:41] [8200]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.iam.biz]' package. Please check your configuration.
[WARN] [11:09:42] [8200]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [11:09:44] [8200]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [11:09:47] [8200]  --- [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] --- Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[WARN] [11:09:47] [8200]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [11:09:49] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [11:09:49] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [11:09:49] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [11:09:49] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [11:10:16] [8200]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [11:10:16] [8200]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [11:10:16] [8200]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [11:10:16] [8200]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [17:24:56] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam.yaml] & group[DEFAULT_GROUP]
[WARN] [17:24:56] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [17:24:57] [8200]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.iam.biz]' package. Please check your configuration.
[WARN] [17:24:58] [8200]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [17:25:01] [8200]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [17:25:03] [8200]  --- [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] --- Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[WARN] [17:25:03] [8200]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [17:25:05] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [17:25:05] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [17:25:05] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [17:25:05] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [17:29:03] [8200]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [17:29:03] [8200]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [17:29:03] [8200]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [17:29:03] [8200]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [17:29:12] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam.yaml] & group[DEFAULT_GROUP]
[WARN] [17:29:12] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [17:29:13] [8200]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.iam.biz]' package. Please check your configuration.
[WARN] [17:29:14] [8200]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [17:29:17] [8200]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [17:29:19] [8200]  --- [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] --- Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[WARN] [17:29:19] [8200]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [17:29:22] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [17:29:22] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [17:29:22] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [17:29:22] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [17:30:51] [8200]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [17:30:51] [8200]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [18:34:26] [8200]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [18:34:26] [8200]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [18:34:26] [8200]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [18:34:26] [8200]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
