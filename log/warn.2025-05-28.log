[WARN] [10:54:25] [8000]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-gateway.yaml] & group[DEFAULT_GROUP]
[WARN] [10:54:26] [8000]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-gateway-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:54:56] [8000]  --- [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] --- Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[WARN] [10:54:57] [8000]  --- [o.s.c.s.a.z.Zip<PERSON>AutoConfiguration] --- Check result of the [WebClientSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: localhost/0:0:0:0:0:0:0:1:9411; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: localhost/0:0:0:0:0:0:0:1:9411}]
[WARN] [10:56:37] [8000]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:56:37] [8000]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:56:37] [8000]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:56:37] [8000]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [11:13:54] [8000]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-gateway.yaml] & group[DEFAULT_GROUP]
[WARN] [11:13:54] [8000]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-gateway-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [11:14:02] [8000]  --- [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] --- Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[WARN] [11:14:02] [8000]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [WebClientSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: localhost/127.0.0.1:9411; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: localhost/127.0.0.1:9411}]
[WARN] [11:18:58] [8000]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [11:18:58] [8000]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [11:18:58] [8000]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [11:18:58] [8000]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
