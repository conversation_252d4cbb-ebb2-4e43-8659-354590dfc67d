[WARN] [11:14:03] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam.yaml] & group[DEFAULT_GROUP]
[WARN] [11:14:03] [8200]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-iam-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [11:14:04] [8200]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.iam.biz]' package. Please check your configuration.
[WARN] [11:14:06] [8200]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [11:14:09] [8200]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [11:14:11] [8200]  --- [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] --- Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[WARN] [11:14:11] [8200]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [11:14:14] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [11:14:14] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [11:14:14] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
[WARN] [11:14:14] [8200]  --- [s.d.s.r.o.OperationImplicitParameterReader] --- Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
