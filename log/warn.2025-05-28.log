[WARN] [16:13:01] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:13:01] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:13:02] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:13:03] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [16:13:06] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:13:08] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:13:10] [9100]  --- [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] --- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is java.lang.reflect.UndeclaredThrowableException
[WARN] [16:13:12] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Timed out waiting for in-flight spans to send
[WARN] [16:13:13] [9100]  --- [o.apache.tomcat.util.net.NioEndpoint] --- Failed to unlock acceptor for [http-nio-9100] because the local address was not available
[WARN] [16:13:13] [9100]  --- [o.a.c.loader.WebappClassLoaderBase] --- The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.18/java.net.Inet6AddressImpl.getHostByAddr(Native Method)
 java.base@11.0.18/java.net.InetAddress$PlatformNameService.getHostByAddr(InetAddress.java:935)
 java.base@11.0.18/java.net.InetAddress.getHostFromNameService(InetAddress.java:659)
 java.base@11.0.18/java.net.InetAddress.getHostName(InetAddress.java:602)
 java.base@11.0.18/java.net.InetAddress.getHostName(InetAddress.java:574)
 app//org.springframework.cloud.commons.util.InetUtils$$Lambda$192/0x0000000800245040.call(Unknown Source)
 java.base@11.0.18/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
 java.base@11.0.18/java.util.concurrent.FutureTask.run(FutureTask.java)
 java.base@11.0.18/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
 java.base@11.0.18/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.18/java.lang.Thread.run(Thread.java:829)
[WARN] [16:13:13] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:13:13] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:13:13] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [16:13:13] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:13:45] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:13:46] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:13:46] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:13:48] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [16:13:50] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:13:52] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:13:56] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [16:13:56] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [16:19:10] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:19:10] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:19:10] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [16:19:10] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:33:20] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:33:21] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:33:21] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:33:22] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [16:33:25] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:33:27] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:33:31] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [16:33:31] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [16:34:02] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:34:02] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:34:02] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [16:34:02] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:34:11] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:34:12] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:34:12] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:34:15] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://*************:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://*************:9411/api/v2/spans": Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out}]
[WARN] [16:34:16] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:34:18] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:34:19] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [16:34:19] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://*************:9411/api/v2/spans": Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out)
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://*************:9411/api/v2/spans": Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/java.net.SocketInputStream.socketRead0(Native Method)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/java.io.BufferedInputStream.fill(BufferedInputStream.java:252)
	at java.base/java.io.BufferedInputStream.read1(BufferedInputStream.java:292)
	at java.base/java.io.BufferedInputStream.read(BufferedInputStream.java:351)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:787)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:722)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1620)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1525)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:527)
	at org.springframework.http.client.SimpleClientHttpResponse.getRawStatusCode(SimpleClientHttpResponse.java:55)
	at org.springframework.web.client.DefaultResponseErrorHandler.hasError(DefaultResponseErrorHandler.java:61)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:807)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:777)
	... 11 common frames omitted
[WARN] [16:36:50] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:36:50] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:36:50] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [16:36:50] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:39:41] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:39:42] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:39:42] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:39:43] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [16:39:46] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:39:49] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:39:51] [9100]  --- [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] --- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is java.lang.reflect.UndeclaredThrowableException
[WARN] [16:39:53] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Timed out waiting for in-flight spans to send
[WARN] [16:39:54] [9100]  --- [o.apache.tomcat.util.net.NioEndpoint] --- Failed to unlock acceptor for [http-nio-9100] because the local address was not available
[WARN] [16:39:54] [9100]  --- [o.a.c.loader.WebappClassLoaderBase] --- The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.18/java.net.Inet6AddressImpl.getHostByAddr(Native Method)
 java.base@11.0.18/java.net.InetAddress$PlatformNameService.getHostByAddr(InetAddress.java:935)
 java.base@11.0.18/java.net.InetAddress.getHostFromNameService(InetAddress.java:659)
 java.base@11.0.18/java.net.InetAddress.getHostName(InetAddress.java:602)
 java.base@11.0.18/java.net.InetAddress.getHostName(InetAddress.java:574)
 app//org.springframework.cloud.commons.util.InetUtils$$Lambda$177/0x000000080021c040.call(Unknown Source)
 java.base@11.0.18/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
 java.base@11.0.18/java.util.concurrent.FutureTask.run(FutureTask.java)
 java.base@11.0.18/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
 java.base@11.0.18/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.18/java.lang.Thread.run(Thread.java:829)
[WARN] [16:39:54] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:39:54] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [16:39:54] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:39:54] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:40:03] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:40:03] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:40:04] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:40:05] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [16:40:07] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:40:10] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:41:33] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [16:41:33] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 11 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [16:51:08] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:51:08] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:51:08] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [16:51:08] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:51:20] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:51:20] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:51:21] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:51:22] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [16:51:25] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:51:27] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:52:16] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:52:16] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:52:16] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [16:52:16] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:52:27] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:52:27] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:52:28] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:52:29] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [16:52:32] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:52:34] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:52:58] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [16:52:58] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 15 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [16:54:37] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:54:37] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:54:37] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [16:54:37] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:54:47] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:54:47] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:54:48] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:54:49] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [16:54:51] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:54:54] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:54:56] [9100]  --- [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] --- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is java.lang.reflect.UndeclaredThrowableException
[WARN] [16:54:57] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Timed out waiting for in-flight spans to send
[WARN] [16:54:58] [9100]  --- [o.apache.tomcat.util.net.NioEndpoint] --- Failed to unlock acceptor for [http-nio-9100] because the local address was not available
[WARN] [16:54:58] [9100]  --- [o.a.c.loader.WebappClassLoaderBase] --- The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.18/java.net.Inet6AddressImpl.getHostByAddr(Native Method)
 java.base@11.0.18/java.net.InetAddress$PlatformNameService.getHostByAddr(InetAddress.java:935)
 java.base@11.0.18/java.net.InetAddress.getHostFromNameService(InetAddress.java:659)
 java.base@11.0.18/java.net.InetAddress.getHostName(InetAddress.java:602)
 java.base@11.0.18/java.net.InetAddress.getHostName(InetAddress.java:574)
 app//org.springframework.cloud.commons.util.InetUtils$$Lambda$177/0x000000080021c040.call(Unknown Source)
 java.base@11.0.18/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
 java.base@11.0.18/java.util.concurrent.FutureTask.run(FutureTask.java)
 java.base@11.0.18/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
 java.base@11.0.18/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.18/java.lang.Thread.run(Thread.java:829)
[WARN] [16:54:58] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:54:58] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [16:54:58] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:54:58] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:55:07] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:55:07] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:55:08] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:55:09] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [16:55:12] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:55:14] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:55:16] [9100]  --- [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] --- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is java.lang.reflect.UndeclaredThrowableException
[WARN] [16:55:18] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Timed out waiting for in-flight spans to send
[WARN] [16:55:19] [9100]  --- [o.apache.tomcat.util.net.NioEndpoint] --- Failed to unlock acceptor for [http-nio-9100] because the local address was not available
[WARN] [16:55:19] [9100]  --- [o.a.c.loader.WebappClassLoaderBase] --- The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.18/java.net.Inet6AddressImpl.getHostByAddr(Native Method)
 java.base@11.0.18/java.net.InetAddress$PlatformNameService.getHostByAddr(InetAddress.java:935)
 java.base@11.0.18/java.net.InetAddress.getHostFromNameService(InetAddress.java:659)
 java.base@11.0.18/java.net.InetAddress.getHostName(InetAddress.java:602)
 java.base@11.0.18/java.net.InetAddress.getHostName(InetAddress.java:574)
 app//org.springframework.cloud.commons.util.InetUtils$$Lambda$177/0x000000080021c040.call(Unknown Source)
 java.base@11.0.18/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
 java.base@11.0.18/java.util.concurrent.FutureTask.run(FutureTask.java)
 java.base@11.0.18/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
 java.base@11.0.18/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.18/java.lang.Thread.run(Thread.java:829)
[WARN] [16:55:19] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:55:19] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:55:19] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [16:55:19] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:56:12] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [16:56:13] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [16:56:13] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [16:56:15] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [16:56:17] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [16:56:20] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [16:56:56] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [16:56:56] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 27 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [16:57:08] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [16:57:08] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [16:57:08] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [16:57:08] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [17:02:32] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [17:02:32] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [17:02:33] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [17:02:34] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [17:02:37] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [17:02:39] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [17:02:43] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [17:02:43] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [17:04:49] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [17:04:49] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [17:04:49] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [17:04:49] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
