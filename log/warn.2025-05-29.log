[WARN] [10:23:11] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [10:23:11] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:23:12] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [10:23:13] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:23:16] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:23:18] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [10:23:18] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [10:23:18] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:25:31] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:25:31] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:25:31] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:25:31] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [10:25:43] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [10:25:43] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:25:44] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [10:25:45] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:25:47] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:25:50] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:25:52] [9100]  --- [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] --- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is java.lang.reflect.UndeclaredThrowableException
[WARN] [10:25:53] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Timed out waiting for in-flight spans to send
[WARN] [10:25:54] [9100]  --- [o.apache.tomcat.util.net.NioEndpoint] --- Failed to unlock acceptor for [http-nio-9100] because the local address was not available
[WARN] [10:25:54] [9100]  --- [o.a.c.loader.WebappClassLoaderBase] --- The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.18/java.net.Inet6AddressImpl.getHostByAddr(Native Method)
 java.base@11.0.18/java.net.InetAddress$PlatformNameService.getHostByAddr(InetAddress.java:935)
 java.base@11.0.18/java.net.InetAddress.getHostFromNameService(InetAddress.java:659)
 java.base@11.0.18/java.net.InetAddress.getHostName(InetAddress.java:602)
 java.base@11.0.18/java.net.InetAddress.getHostName(InetAddress.java:574)
 app//org.springframework.cloud.commons.util.InetUtils$$Lambda$177/0x000000080021c040.call(Unknown Source)
 java.base@11.0.18/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
 java.base@11.0.18/java.util.concurrent.FutureTask.run(FutureTask.java)
 java.base@11.0.18/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
 java.base@11.0.18/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.18/java.lang.Thread.run(Thread.java:829)
[WARN] [10:25:54] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:25:54] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:25:54] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:26:14] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [10:26:15] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:26:15] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [10:26:16] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:26:19] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:26:21] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:26:26] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [10:26:26] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 2 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [10:26:33] [9100] 6837c5d710a5ba83389b6acf28462df3 --- [o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver] --- Failure in @ExceptionHandler org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint#handleOAuth2Exception(OAuth2Exception, ServletWebRequest)
java.lang.ClassCastException: class java.lang.String cannot be cast to class org.springframework.security.oauth2.common.exceptions.OAuth2Exception (java.lang.String is in module java.base of loader 'bootstrap'; org.springframework.security.oauth2.common.exceptions.OAuth2Exception is in unnamed module of loader 'app')
	at org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint.handleException(AuthorizationEndpoint.java:602)
	at org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint.handleOAuth2Exception(AuthorizationEndpoint.java:575)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1327)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1138)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:645)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.mira.openapi.filter.RequestWrapperFilter.doFilter(RequestWrapperFilter.java:20)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:142)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:103)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
[WARN] [10:28:12] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:28:12] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:28:12] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:28:12] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [10:35:21] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [10:35:22] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:35:22] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [10:35:24] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:35:26] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:35:29] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:35:36] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [10:35:36] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [10:37:55] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:37:55] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:37:55] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:37:55] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [10:38:06] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [10:38:06] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:38:06] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [10:38:08] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:38:10] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:38:13] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:38:17] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [10:38:17] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [10:39:45] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:39:45] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:39:45] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:39:45] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [10:39:56] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [10:39:57] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:39:57] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [10:39:58] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:40:01] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:40:04] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:40:13] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:40:13] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:40:13] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:40:13] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [10:40:36] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [10:40:36] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:40:37] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [10:40:38] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:40:40] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:40:43] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [10:40:43] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [10:40:43] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:42:10] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:42:10] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:42:10] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:42:10] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [10:42:22] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [10:42:22] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:42:23] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [10:42:24] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:42:27] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:42:29] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:42:36] [9100] 6837c999e1c9e11dfaa7d5f64863cc34 --- [o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver] --- Failure in @ExceptionHandler org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint#handleOAuth2Exception(OAuth2Exception, ServletWebRequest)
java.lang.ClassCastException: class java.lang.String cannot be cast to class org.springframework.security.oauth2.common.exceptions.OAuth2Exception (java.lang.String is in module java.base of loader 'bootstrap'; org.springframework.security.oauth2.common.exceptions.OAuth2Exception is in unnamed module of loader 'app')
	at org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint.handleException(AuthorizationEndpoint.java:602)
	at org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint.handleOAuth2Exception(AuthorizationEndpoint.java:575)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1327)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1138)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:645)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.mira.openapi.filter.RequestWrapperFilter.doFilter(RequestWrapperFilter.java:20)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:142)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:103)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
[WARN] [10:42:46] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:42:46] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:42:46] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [10:42:46] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:45:45] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [10:45:45] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [10:45:45] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [10:45:46] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [10:45:49] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [10:45:51] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [10:45:51] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [10:45:51] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [10:45:59] [9100] 6837ca6611d2448a9f2b485515ebf216 --- [o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver] --- Failure in @ExceptionHandler org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint#handleOAuth2Exception(OAuth2Exception, ServletWebRequest)
java.lang.ClassCastException: class java.lang.String cannot be cast to class org.springframework.security.oauth2.common.exceptions.OAuth2Exception (java.lang.String is in module java.base of loader 'bootstrap'; org.springframework.security.oauth2.common.exceptions.OAuth2Exception is in unnamed module of loader 'app')
	at org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint.handleException(AuthorizationEndpoint.java:602)
	at org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint.handleOAuth2Exception(AuthorizationEndpoint.java:575)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1327)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1138)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:645)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.mira.openapi.filter.RequestWrapperFilter.doFilter(RequestWrapperFilter.java:20)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:142)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:103)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
[WARN] [10:47:04] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [10:47:04] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [10:47:04] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [10:47:04] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [11:23:36] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [11:23:36] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [11:23:36] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [11:23:38] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [11:23:40] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [11:23:43] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [11:23:46] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [11:23:46] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [13:00:24] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [13:00:24] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [13:00:24] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [13:00:24] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [13:12:51] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [13:12:52] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [13:12:52] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [13:12:53] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [13:12:56] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [13:12:59] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [13:15:06] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [13:15:06] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 4 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [13:23:04] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [13:23:04] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [13:23:04] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [13:23:04] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [13:29:06] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [13:29:07] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [13:29:07] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [13:29:08] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [13:29:11] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [13:29:13] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [13:29:13] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [13:29:13] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [14:13:40] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [14:13:40] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [14:13:40] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [14:13:40] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [14:13:51] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [14:13:51] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [14:13:52] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [14:13:53] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [14:13:56] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [14:13:58] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [14:18:02] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [14:18:02] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [14:18:02] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [14:18:02] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [14:18:12] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [14:18:13] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [14:18:13] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [14:18:14] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [14:18:17] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [14:18:19] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [14:18:19] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 2 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [14:18:20] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
[WARN] [14:20:34] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Start destroying common HttpClient
[WARN] [14:20:34] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Start destroying Publisher
[WARN] [14:20:34] [9100]  --- [c.a.nacos.common.notify.NotifyCenter] --- [NotifyCenter] Destruction of the end
[WARN] [14:20:34] [9100]  --- [c.a.n.c.http.HttpClientBeanHolder] --- [HttpClientBeanHolder] Destruction of the end
[WARN] [14:20:44] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api.yaml] & group[DEFAULT_GROUP]
[WARN] [14:20:44] [9100]  --- [c.a.c.n.c.NacosPropertySourceBuilder] --- Ignore the empty nacos configuration and get it based on dataId[mira-open-api-dev.yaml] & group[DEFAULT_GROUP]
[WARN] [14:20:45] [9100]  --- [c.g.l.r.s.b.c.ClassPathRetrofitClientScanner] --- No RetrofitClient was found in '[com.mira.openapi]' package. Please check your configuration.
[WARN] [14:20:46] [9100]  --- [o.s.c.s.a.z.ZipkinAutoConfiguration] --- Check result of the [RestTemplateSender{http://localhost:9411/api/v2/spans}] contains an error [CheckResult{ok=false, error=org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)}]
[WARN] [14:20:48] [9100]  --- [o.s.s.o.p.t.s.JwtAccessTokenConverter] --- Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
[WARN] [14:20:51] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Spans were dropped due to exceptions. All subsequent errors will be logged at FINE level.
[WARN] [14:20:51] [9100]  --- [z.r.AsyncReporter$BoundedAsyncReporter] --- Dropped 1 spans due to ResourceAccessException(I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused))
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:9411/api/v2/spans": Connection refused (Connection refused); nested exception is java.net.ConnectException: Connection refused (Connection refused)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)
	at org.springframework.cloud.sleuth.zipkin2.ZipkinRestTemplateWrapper.doExecute(ZipkinRestTemplateWrapper.java:67)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:660)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.post(RestTemplateSender.java:51)
	at org.springframework.cloud.sleuth.zipkin2.RestTemplateSender.lambda$new$0(RestTemplateSender.java:44)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender.post(HttpSender.java:137)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:150)
	at org.springframework.cloud.sleuth.zipkin2.HttpSender$HttpPostCall.doExecute(HttpSender.java:140)
	at zipkin2.Call$Base.execute(Call.java:391)
	at zipkin2.reporter.AsyncReporter$BoundedAsyncReporter.flush(AsyncReporter.java:299)
	at zipkin2.reporter.AsyncReporter$Flusher.run(AsyncReporter.java:378)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.Socket.connect(Socket.java:615)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:177)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:507)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:602)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:275)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:374)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:395)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1258)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1192)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1086)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1020)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)
	... 11 common frames omitted
[WARN] [14:20:51] [9100]  --- [o.s.c.s.o.SpringCloudSecurityAutoConfiguration] --- All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release.
