package com.mira.api.bluetooth.dto.cycle;

import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.OvulationTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("周期数据")
public class CycleDataDTO {
    @ApiModelProperty("主键")
    private Integer cycle_index;

    @ApiModelProperty("周期长度")
    private Integer len_cycle;

    @ApiModelProperty("周期开始日")
    private String date_period_start;

    @ApiModelProperty("经期结束日（经期不包含这天")
    private String date_period_end;

    @ApiModelProperty("易孕期开始日 (由 fertility soccer 》=6 计算)")
    private String date_FW_start;

    @ApiModelProperty("易孕期结束日 （易孕期不包含这一天）")
    private String date_FW_end;

    @ApiModelProperty("排卵日 （预测 or LH 峰值日")
    private String date_ovulation;

    @ApiModelProperty("预留，可为 null （实际测量的最高值时间）")
    private String date_LH_surge;

    @ApiModelProperty("float, 实际峰值日 中 LH的对应最大值，可为 null")
    private Float value_LH_surge;

    /**
     * @see OvulationTypeEnum
     */
    @ApiModelProperty("排卵日类型")
    private Integer ovulation_type;

    @ApiModelProperty("编辑经期新增的字段")
    private List<String> date_PDG_rise;

    @ApiModelProperty("float, LH 阈值,可为 null")
    private Float threshold_LH;

    @ApiModelProperty("float, E3G 阈值,可为 null")
    private Float threshold_E3G;

    /**
     * @see CycleStatusEnum
     */
    private Integer cycle_status;

    @ApiModelProperty("怀孕模式下，怀孕周期会分为3段，这个字段表示每段的长度")
    private List<Integer> len_phase;

    @ApiModelProperty("实周期和预测周期为“cd1”类似,怀孕周期为“Week 1,Day1”类似")
    private List<String> cycle_cd_index;

    @ApiModelProperty("怀孕风险区域对象")
    private PregnantRiskDTO pregnant_risk;

    @ApiModelProperty("易孕指数，len_cycle个元素 float ⼩数点后⼀位,每个元素可为null")
    private List<Float> fertility_score_list;

    @ApiModelProperty("测试日")
    private TestingProductDayDTO testing_day_list;
}
