package com.mira.api.bluetooth.dto.example;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("算法返回的example测试数据")
public class AlgorithmExampleDTO {
    @ApiModelProperty("lh example数据")
    @JsonProperty("LH")
    private List<TestDataDTO> LH;

    @ApiModelProperty("e3g example数据")
    @JsonProperty("E3G")
    private List<TestDataDTO> E3G;

    @ApiModelProperty("hcg example数据")
    @JsonProperty("HCG")
    private List<TestDataDTO> HCG;

    @ApiModelProperty("pdg example数据")
    @JsonProperty("PDG")
    private List<TestDataDTO> PDG;

    @ApiModelProperty("bbt example数据")
    @JsonProperty("BBT")
    private List<TestDataDTO> BBT;
}
