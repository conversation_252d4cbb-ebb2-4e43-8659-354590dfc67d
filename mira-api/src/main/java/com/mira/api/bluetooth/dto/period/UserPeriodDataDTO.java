package com.mira.api.bluetooth.dto.period;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("周期信息")
public class UserPeriodDataDTO {
    @ApiModelProperty("周期开始日")
    private String date_period_start;

    @ApiModelProperty("经期结束⽇（经期不包含这天）")
    private String date_period_end;

    @ApiModelProperty("0:忽略len_cycle数值 ; 1:使用len_cycle的数值 ; 2:在len_cycle的基础上修正")
    private Integer flag;

    @ApiModelProperty("当前周期的周期长度")
    private Integer len_cycle;

    @ApiModelProperty("手动添加的排卵日")
    private String manual_ovulation;
}
