package com.mira.api.bluetooth.enums;

import lombok.Getter;

/**
 * 周期标记枚举
 *
 * <AUTHOR>
 */
@Getter
public enum LastCycleFlagEnum {
    ZERO(0, "忽略len_cycle数值"),
    ONE(1, "使用len_cycle的数值"),
    TWO(2, "在len_cycle的基础上修正"),
    THREE(3, "无经期")
    ;

    private final Integer flag;
    private final String desc;

    LastCycleFlagEnum(Integer flag, String desc) {
        this.flag = flag;
        this.desc = desc;
    }
}
