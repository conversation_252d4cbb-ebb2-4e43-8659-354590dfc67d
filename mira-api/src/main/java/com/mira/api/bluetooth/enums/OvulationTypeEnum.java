package com.mira.api.bluetooth.enums;

import lombok.Getter;

/**
 * 排卵日类型枚举（算法）
 *
 * <AUTHOR>
 */
@Getter
public enum OvulationTypeEnum {
    PREDICTED(1, "预测排卵"),
    DETECTED(2, "检测到排卵（lh peak后1天或后2天）"),
    PREDICTED_CONFIRMED(13, "未测到排卵，预测排卵日之后测到多次 pdg raise"),
    DETECTED_CONFIRMED(23, "检测到排卵 ，并且测到多次 pdg rise"),
    CUSTOM(99, "用户自定义输入")
    ;

    private final Integer code;
    private final String desc;

    OvulationTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OvulationTypeEnum get(Integer code) {
        for (OvulationTypeEnum ovulationTypeEnum : values()) {
            if (ovulationTypeEnum.getCode().equals(code)) {
                return ovulationTypeEnum;
            }
        }
        return null;
    }
}
