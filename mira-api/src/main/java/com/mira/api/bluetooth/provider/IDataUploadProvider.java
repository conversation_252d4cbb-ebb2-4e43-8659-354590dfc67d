package com.mira.api.bluetooth.provider;

import com.mira.api.bluetooth.consts.DataUploadApiConst;
import com.mira.api.bluetooth.dto.backend.AppDataUploadDTO;
import com.mira.api.bluetooth.dto.backend.ChangeWandBatchDTO;
import com.mira.api.bluetooth.dto.backend.DataUploadPageRequestDTO;
import com.mira.api.bluetooth.dto.backend.DataUploadResponseDTO;
import com.mira.core.response.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>
 * 上传的hormone测试数据
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-08-08
 **/
@FeignClient(value = "mira-bluetooth", contextId = "data-upload")
public interface IDataUploadProvider {
    /**
     * 测试数据分页列表（给后台管理系统提供）
     *
     * @param dataUploadPageRequestDTO
     * @return DataUploadResponseDTO
     */
    @PostMapping(DataUploadApiConst.DATA_UPLOAD_PAGE)
    CommonResult<DataUploadResponseDTO> dataUploadPage(@RequestBody DataUploadPageRequestDTO dataUploadPageRequestDTO);

    /**
     * 根据id获取测试数据，包含是否删除的状态
     *
     * @param id
     * @return
     */
    @GetMapping(DataUploadApiConst.GETBYID_WITH_DELETED)
    CommonResult<AppDataUploadDTO> getByIdWithDeleted(@RequestParam("id") Long id);

    /**
     * 修改测试数据的删除状态
     *
     * @param id
     * @param userId
     * @param deleted
     * @param sysNote
     * @return
     */
    @PostMapping(DataUploadApiConst.CHANGE_DELETE_STATUS)
    CommonResult<Void> changeDeleteStatus(@RequestParam("id") Long id,
                                          @RequestParam("userId") Long userId,
                                          @RequestParam("deleted") Integer deleted,
                                          @RequestParam("sysNote") String sysNote);

    /**
     * 修改测试数据的完成时间
     *
     * @param id
     * @param dataTimeZone
     * @param newCompleteTime
     * @param sysNote
     * @return
     */
    @PostMapping(DataUploadApiConst.CHANGE_COMPLETE_TIME)
    CommonResult<Void> changeCompleteTime(@RequestParam("id") Long id,
                                          @RequestParam("dataTimeZone") String dataTimeZone,
                                          @RequestParam("newCompleteTime") String newCompleteTime,
                                          @RequestParam("sysNote") String sysNote);

    @PostMapping(DataUploadApiConst.CHANGE_WAND_BATCH)
    CommonResult<Void> changeWandBatch(@RequestBody ChangeWandBatchDTO changeWandBatchDTO);

    /**
     * 统计用户的max测试数据的数量
     *
     * @param userId
     * @return
     */
    @PostMapping(DataUploadApiConst.COUNT_MAX_WANDS)
    CommonResult<Integer> countMaxWands(@RequestParam("userId") Long userId);

    /**
     * 修改测试数据的有效性(改为有效)
     *
     * @param id
     * @return
     */
    @PostMapping(DataUploadApiConst.UPDATE_TEST_DATA_VALID)
    CommonResult<Void> updateTestDataValid(@RequestParam("id") Long id,
                                           @RequestParam("userId") Long userId,
                                           @RequestParam("username") String username);
}
