package com.mira.api.bluetooth.util;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestingProductDayDTO;
import com.mira.api.bluetooth.dto.wand.*;
import com.mira.api.bluetooth.enums.DeviceErrorCodeEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.BiomarkerNameEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.LocalDateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * test remind util
 *
 * <AUTHOR>
 */
public class TestRemindUtil {
    public static List<TestRemindDTO> getTestRemind(DayTestProductsDTO dayTestProductsDTO) {
        String date = dayTestProductsDTO.getDate();
        List<CycleDataDTO> cycleDataDTOS = dayTestProductsDTO.getCycleDataDTOS();
        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = dayTestProductsDTO.getWandTestBiomarkerDTOS();
        return getTestingProducts(date, cycleDataDTOS, wandTestBiomarkerDTOS);
    }

    public static List<TestDiaryRemindDTO> getTestRemind(List<DayTestProductsDTO> dayTestProductsDTOS) {
        List<TestDiaryRemindDTO> testDiaryRemindDTOS = new ArrayList<>();

        for (DayTestProductsDTO dayTestProductsDTO : dayTestProductsDTOS) {
            TestDiaryRemindDTO testDiaryRemindDTO = new TestDiaryRemindDTO();
            testDiaryRemindDTO.setDate(dayTestProductsDTO.getDate());
            testDiaryRemindDTO.setTestRemindDTOS(getTestRemind(dayTestProductsDTO));
            testDiaryRemindDTOS.add(testDiaryRemindDTO);
        }

        return testDiaryRemindDTOS;
    }

    /**
     * 获取当天需要测试的产品列表
     */
    public static List<TestRemindDTO> getTestingProducts(String dateStr,
                                                         List<CycleDataDTO> cycleDataDTOS,
                                                         List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS) {
        List<TestRemindDTO> testingDTOS = new ArrayList<>();
        List<String> testingProducts = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
            if (LocalDateUtil.minusToDay(dateStr, datePeriodStart) < 0
                    || LocalDateUtil.minusToDay(dateStr, dateCycleEnd) >= 0) {
                continue;
            }
            TestingProductDayDTO testingDayList = cycleDataDTO.getTesting_day_list();
            if (testingDayList == null) {
                continue;
            }
            List<String> product03TestingDayList = testingDayList.getProduct03();
            List<String> product02TestingDayList = testingDayList.getProduct02();
            List<String> product09TestingDayList = testingDayList.getProduct09();
            List<String> product12TestingDayList = testingDayList.getProduct12();
            List<String> product14TestingDayList = testingDayList.getProduct14();
            List<String> product16TestingDayList = testingDayList.getProduct16();
            List<String> product18TestingDayList = testingDayList.getProduct18();
            if (product03TestingDayList != null && product03TestingDayList.contains(dateStr)) {
                testingProducts.add("0".concat(WandTypeEnum.E3G_LH.getString()));
            }
            if (product02TestingDayList != null && product02TestingDayList.contains(dateStr)) {
                testingProducts.add("0".concat(WandTypeEnum.HCG.getString()));
            }
            if (product09TestingDayList != null && product09TestingDayList.contains(dateStr)) {
                testingProducts.add("0".concat(WandTypeEnum.PDG.getString()));
            }
            if (product12TestingDayList != null && product12TestingDayList.contains(dateStr)) {
                testingProducts.add(WandTypeEnum.LH_E3G_PDG.getString());
            }
            if (product14TestingDayList != null && product14TestingDayList.contains(dateStr)) {
                testingProducts.add(WandTypeEnum.HCG_QUALITATIVE.getString());
            }
            if (product16TestingDayList != null && product16TestingDayList.contains(dateStr)) {
                testingProducts.add(WandTypeEnum.FSH.getString());
            }
            if (product18TestingDayList != null && product18TestingDayList.contains(dateStr)) {
                testingProducts.add(WandTypeEnum.MAX2.getString());
            }
        }

        Set<String> biomarkers = wandTestBiomarkerDTOS
                .stream()
                .filter(wandTestBiomarkerDTO -> StringUtils.isBlank(wandTestBiomarkerDTO.getEcode())
                        || (wandTestBiomarkerDTO.getEcode().startsWith("B") && !DeviceErrorCodeEnum.B01.getValue().equals(wandTestBiomarkerDTO.getEcode())))
                .map(WandTestBiomarkerDTO::getWandType)
                .collect(Collectors.toSet());

        for (String testingProduct : testingProducts) {
            if ("0".concat(WandTypeEnum.E3G_LH.getString()).equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.E3G.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.E3G.getName());
                    testingDTOS.add(testingDTO);
                }
                if (!biomarkers.contains(BiomarkerNameEnum.LH.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.LH.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if ("0".concat(WandTypeEnum.HCG.getString()).equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.HCG.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.HCG.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if ("0".concat(WandTypeEnum.PDG.getString()).equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.PDG.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.PDG.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if (WandTypeEnum.LH_E3G_PDG.getString().equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.E3G.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.E3G.getName());
                    testingDTOS.add(testingDTO);
                }
                if (!biomarkers.contains(BiomarkerNameEnum.LH.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.LH.getName());
                    testingDTOS.add(testingDTO);
                }
                if (!biomarkers.contains(BiomarkerNameEnum.PDG.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.PDG.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if (WandTypeEnum.MAX2.getString().equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.E3G.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.E3G.getName());
                    testingDTOS.add(testingDTO);
                }
                if (!biomarkers.contains(BiomarkerNameEnum.LH.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.LH.getName());
                    testingDTOS.add(testingDTO);
                }
                if (!biomarkers.contains(BiomarkerNameEnum.FSH.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.FSH.getName());
                    testingDTOS.add(testingDTO);
                }
                if (!biomarkers.contains(BiomarkerNameEnum.PDG.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.PDG.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if (WandTypeEnum.HCG_QUALITATIVE.getString().equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.HCG2.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.HCG2.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if (WandTypeEnum.FSH.getString().equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.FSH.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.FSH.getName());
                    testingDTOS.add(testingDTO);
                }
            }
        }

        return testingDTOS;
    }

    /**
     * 获取今天及之后需要测试的产品列表 (monopause模式下只考虑 12和 16?)
     * 用于首页test plan
     */
    public static List<TestingWandDTO> getTestingWandDTOs(String today,
                                                          List<CycleDataDTO> cycleDataDTOS) {
        List<TestingWandDTO> testingDTOS = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
            //如果今天在周期结束日之后，就忽略该周期
            if (LocalDateUtil.minusToDay(today, dateCycleEnd) >= 0) {
                continue;
            }
            TestingProductDayDTO testingDayList = cycleDataDTO.getTesting_day_list();
            if (testingDayList == null) {
                continue;
            }
            List<String> product03TestingDayList = testingDayList.getProduct03();
            List<String> product09TestingDayList = testingDayList.getProduct09();
            List<String> product12TestingDayList = testingDayList.getProduct12();
            List<String> product16TestingDayList = testingDayList.getProduct16();
            List<String> product18TestingDayList = testingDayList.getProduct18();

            buildTestingWandDTOs(product03TestingDayList, testingDTOS, WandTypeEnum.E3G_LH);
            buildTestingWandDTOs(product09TestingDayList, testingDTOS, WandTypeEnum.PDG);
            buildTestingWandDTOs(product12TestingDayList, testingDTOS, WandTypeEnum.LH_E3G_PDG);
            buildTestingWandDTOs(product16TestingDayList, testingDTOS, WandTypeEnum.FSH);
            buildTestingWandDTOs(product18TestingDayList, testingDTOS, WandTypeEnum.MAX2);
        }
        return testingDTOS.stream().sorted(Comparator.comparing(TestingWandDTO::getDate)).collect(Collectors.toList());
    }

    /**
     * 获取某一天及之后需要测试的产品列表 (monopause模式下只考虑 12和 16?),以及这个月内的已测试历史
     */
    public static List<TestingWandDTO> getTestingWandDTOs(String today, String thisMonth,
                                                          List<CycleDataDTO> cycleDataDTOS,
                                                          List<HormoneDTO> hormoneDatas) {
        List<TestingWandDTO> testingDTOS = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
            //如果今天在周期结束日之后，就忽略该周期 --> 还要看这个月有没有测试历史
            if (LocalDateUtil.minusToDay(today, dateCycleEnd) >= 0) {
                continue;
            }
            TestingProductDayDTO testingDayList = cycleDataDTO.getTesting_day_list();
            if (testingDayList == null) {
                continue;
            }
            List<String> product03TestingDayList = testingDayList.getProduct03();
            List<String> product09TestingDayList = testingDayList.getProduct09();
            List<String> product12TestingDayList = testingDayList.getProduct12();
            List<String> product16TestingDayList = testingDayList.getProduct16();
            List<String> product18TestingDayList = testingDayList.getProduct18();

            buildTestingWandDTOs(product03TestingDayList, testingDTOS, WandTypeEnum.E3G_LH);
            buildTestingWandDTOs(product09TestingDayList, testingDTOS, WandTypeEnum.PDG);
            buildTestingWandDTOs(product12TestingDayList, testingDTOS, WandTypeEnum.LH_E3G_PDG);
            buildTestingWandDTOs(product16TestingDayList, testingDTOS, WandTypeEnum.FSH);
            buildTestingWandDTOs(product18TestingDayList, testingDTOS, WandTypeEnum.MAX2);
        }
        //这里要过滤掉今天之前的预测的测试日
        testingDTOS.removeIf(testingWandDTO -> (LocalDateUtil.minusToDay(testingWandDTO.getDate(), today) < 0));
        //需要展示测试历史
        hormoneDatas.stream()
                    .filter(hormoneDTO -> hormoneDTO.getFlag() == 1)
                    .filter(hormoneDTO ->
                            LocalDateUtil.after(hormoneDTO.getTest_time(), thisMonth + "-01 00:00:01",
                                    DatePatternConst.DATE_TIME_PATTERN)
                    )
                    .forEach(hormoneDTO -> {
                        String testDate = hormoneDTO.getTest_time().substring(0, 10);
                        Integer wandType = hormoneDTO.getTest_results().getWand_type();
                        buildTestingWandDTOs(testDate, testingDTOS, WandTypeEnum.get(wandType));
                    });
        return testingDTOS.stream().sorted(Comparator.comparing(TestingWandDTO::getDate)).collect(Collectors.toList());
    }


    private static void buildTestingWandDTOs(List<String> productTestingDayList,
                                             List<TestingWandDTO> testingDTOS, WandTypeEnum wandTypeEnum) {
        if (productTestingDayList.isEmpty()) {
            return;
        }
        for (String date : productTestingDayList) {
            buildTestingWandDTOs(date, testingDTOS, wandTypeEnum);
        }
    }

    private static void buildTestingWandDTOs(String date,
                                             List<TestingWandDTO> testingDTOS, WandTypeEnum wandTypeEnum) {
        Predicate<TestingWandDTO> predicate = testingWandDTO -> date.equals(testingWandDTO.getDate());
        TestingWandDTO testingWandDTO;
        String productCode = wandTypeEnum.getString();
        switch (wandTypeEnum) {
            case E3G_LH:
                productCode = "0".concat(WandTypeEnum.E3G_LH.getString());
                break;
            case PDG:
                productCode = "0".concat(WandTypeEnum.PDG.getString());
                break;
        }
        if (testingDTOS.stream().anyMatch(predicate)) {
            testingWandDTO = testingDTOS.stream().filter(predicate).findAny().get();
            if (!testingWandDTO.getProductCodes().contains(productCode)) {
                testingWandDTO.getProductCodes().add(productCode);
            }
        } else {
            testingWandDTO = new TestingWandDTO();
            testingWandDTO.setDate(date);
            List<String> productCodes = new ArrayList<>();
            productCodes.add(productCode);
            testingWandDTO.setProductCodes(productCodes);
            testingDTOS.add(testingWandDTO);
        }
    }
}

