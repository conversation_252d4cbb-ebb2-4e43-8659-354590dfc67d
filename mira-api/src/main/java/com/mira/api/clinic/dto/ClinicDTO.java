package com.mira.api.clinic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-05-29
 **/
@Getter
@Setter
@ApiModel("ClinicDTO")
public class ClinicDTO {
    @ApiModelProperty("租户code")
    private String code;

    @ApiModelProperty("租户名称")
    private String name;

    @ApiModelProperty("租户图标")
    private String icon;

    @ApiModelProperty("租户描述")
    private String description;
}
