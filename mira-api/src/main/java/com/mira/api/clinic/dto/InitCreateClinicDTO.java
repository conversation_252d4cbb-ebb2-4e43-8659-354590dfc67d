package com.mira.api.clinic.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 初始创建诊所
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-04-04
 **/
@Getter
@Setter
public class InitCreateClinicDTO {
    @NotBlank(message = "clinic code can not be empty")
    @ApiModelProperty(value = "clinic code", required = true)
    private String clinicCode;
    @NotBlank(message = "clinic name can not be empty")
    @ApiModelProperty(value = "clinic 名称", required = true)
    private String clinicName;
    @ApiModelProperty(value = "clinic 类型：0:clinic;1:doctor; 默认0", required = true)
    private Integer clinicType;
    @NotBlank(message = "clinic icon can not be empty")
    @ApiModelProperty(value = "clinic 图标", required = true)
    private String icon;
    @NotBlank(message = "clinic notification Icon can not be empty")
    @ApiModelProperty(value = "clinic 接收app通知的图标", required = true)
    private String notificationIcon;
    @ApiModelProperty(value = "clinic websiteUrl", required = false)
    private String websiteUrl;
    @NotBlank(message = "clinic email can not be empty")
    @ApiModelProperty(value = "管理员email", required = true)
    private String email;
    @NotBlank(message = "clinic password can not be empty")
    @ApiModelProperty(value = "管理员登录密码", required = true)
    private String password;
}
