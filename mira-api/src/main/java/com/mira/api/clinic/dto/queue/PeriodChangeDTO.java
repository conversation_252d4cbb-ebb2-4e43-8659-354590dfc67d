package com.mira.api.clinic.dto.queue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("经期变动")
public class PeriodChangeDTO {
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("时区")
    private String timeZone;

    @ApiModelProperty("经期变动类型")
    private Integer algorithmRequestType;
}
