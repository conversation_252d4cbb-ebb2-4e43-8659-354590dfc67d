package com.mira.api.message.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * notification aggregate type
 *
 * <AUTHOR>
 */
@Getter
public enum NotificationAggregateTypeEnum {
    /**
     * app update、firmware update、rate the app
     */
    MESSAGES_FROM_MIRA(1, "Messages from Mira", Lists.newArrayList(
            SysNotificationTypeEnum.UPDATES.getValue(),
            SysNotificationTypeEnum.RATE_THE_APP.getValue(),
            SysNotificationTypeEnum.RETURN_TO_APP.getValue(),
            SysNotificationTypeEnum.CYCLE_PHASE.getValue(),
            SysNotificationTypeEnum.MANUAL_ADD_DATA.getValue(),
            SysNotificationTypeEnum.ATTENTION.getValue(),
            SysNotificationTypeEnum.WARNING.getValue(),
            SysNotificationTypeEnum.CLINIC_PATIENT.getValue(),
            SysNotificationTypeEnum.LONGER_CYCLE_CHECK.getValue()
    )),
    /**
     * testing reminder
     */
    REMINDERS(2, "Reminders", Lists.newArrayList(
            SysNotificationTypeEnum.TESTING_DAY_POPUP.getValue(),
            SysNotificationTypeEnum.TEST_AGAIN.getValue()
    )),
    /**
     * marketing communication
     */
    MARKETING(3, "System notifications", Lists.newArrayList(
            SysNotificationTypeEnum.MARKETING.getValue(),
            SysNotificationTypeEnum.PURCHASE_RECOMMENDATION.getValue(),
            SysNotificationTypeEnum.PROMO_CODE.getValue()
    )),
    /**
     * all notification
     */
    ALL(0, "All", getAllTypeList())
    ;

    private final Integer aggregateType;
    private final String content;
    private final List<Integer> typeList;

    NotificationAggregateTypeEnum(Integer aggregateType, String content, List<Integer> typeList) {
        this.aggregateType = aggregateType;
        this.content = content;
        this.typeList = typeList;
    }

    private static List<Integer> getAllTypeList() {
        List<Integer> allTypeList = new ArrayList<>(MESSAGES_FROM_MIRA.typeList);
        allTypeList.addAll(REMINDERS.typeList);
        allTypeList.addAll(MARKETING.typeList);
        return allTypeList;
    }

    public static NotificationAggregateTypeEnum get(Integer aggregateType) {
        for (NotificationAggregateTypeEnum aggregateTypeEnum : NotificationAggregateTypeEnum.values()) {
            if (Objects.equals(aggregateTypeEnum.getAggregateType(), aggregateType)) {
                return aggregateTypeEnum;
            }
        }
        return null;
    }
}
