package com.mira.api.thirdparty.provider;

import com.mira.api.thirdparty.consts.path.MedicationApiConst;
import com.mira.core.response.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2023-07-04 15:17
 **/
@FeignClient(name = "mira-third-party", contextId = "medication")
public interface IMedicationProvider {
    @GetMapping(MedicationApiConst.SEARCH)
    CommonResult<List<String>> search(@RequestParam("keyword") String keyword);
}