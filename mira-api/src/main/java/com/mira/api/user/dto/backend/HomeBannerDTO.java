package com.mira.api.user.dto.backend;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 首页banner
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("首页banner")
public class HomeBannerDTO {
    @ApiModelProperty(value = "所属组的标识")
    private String groupUid;

    @ApiModelProperty(value = "展示顺序，顺序相同按照标题首字母排序")
    private Integer orders;

    @ApiModelProperty(value = "激活状态，0未激活，1激活中")
    private Integer status;

    @ApiModelProperty(value = "过期状态，0未过期，1已过期")
    private Integer expire;

    @ApiModelProperty(value = "组元素")
    @NotEmpty(message = "Group elements cannot be empty")
    private List<HomeBannerSubDTO> group;

    @Getter
    @Setter
    public static class HomeBannerSubDTO {
        @ApiModelProperty(value = "bannerId")
        private Long id;

        @ApiModelProperty(value = "标题")
        private String title;

        @ApiModelProperty(value = "内容标题")
        private String mainTitle;

        @ApiModelProperty(value = "跳转链接地址")
        private String link;

        @ApiModelProperty(value = "货币类型：USD、GBP、EUR、CAD、AUD")
        private String currency;

        @ApiModelProperty(value = "文字颜色")
        private String fontColor;

        @ApiModelProperty(value = "背景色")
        private String backgroundColor;

        @ApiModelProperty(value = "首页banner中的图片")
        private String bannerImage;

        @ApiModelProperty(value = "banner主图")
        private String mainImage;

        @ApiModelProperty(value = "类型：1 产品，2 blog or events，3 course")
        @NotNull(message = "Type cannot be empty")
        private Integer type;

        @ApiModelProperty(value = "用户类型列表：-1 All，0 cycle tracking，1 TTA，2 TTC，复选用逗号分隔")
        @NotBlank(message = "User type cannot be empty")
        private String userTypes;

        /**
         * see UserConditionEnum
         */
        @ApiModelProperty("健康选项")

        private Integer[] healthCondition;

        /**
         * 1:(18 - 25), 2:(26 - 30), 3:(31 - 35), 4:(36 - 40), 5:(>40)
         * <p>
         * 5改成(41 - 45)，增加6:(46 - 50), 7:(>50)
         */
        @ApiModelProperty("年龄范围")
        private Integer[] age;

        @ApiModelProperty(value = "图片是否覆盖整个banner：0 false，1 true")
        @NotNull(message = "Image cover option not be empty")
        private Integer imageCover;

        @ApiModelProperty(value = "是否和货币有关：0 无关，1 有关")
        private Integer currencyFlag;

        @ApiModelProperty(value = "绑定校验：0 不需要校验，1 需要历史没有绑定过，2 需要历史绑定过")
        @NotNull(message = "Binding check cannot be empty")
        private Integer bindCheck;

        @ApiModelProperty(value = "内容按钮")
        private String mainButtonText;

        @ApiModelProperty(value = "描述")
        private String description;

        @ApiModelProperty(value = "图片padding")
        private String bannerImagePadding;

        @ApiModelProperty(value = "是否弹窗，0不弹窗，1弹窗")
        private Integer hasModal;

        @ApiModelProperty(value = "surveyId list")
        private List<String> surveyIds;

        @ApiModelProperty("是否跟踪更年期，null无标识;0 不跟踪;1跟踪")
        private Integer trackingMenopause;

        @ApiModelProperty("诊所id列表")
        private List<ClinicDTO> clinics;

        @ApiModelProperty(value = "扩展字段")
        private String mixedString;

        @ApiModelProperty(value = "Stories")
        private Object stories;

        @ApiModelProperty(value = "开始时间（USD） yyyy-MM-dd HH:mm:ss")
        private String startTime;

        @ApiModelProperty(value = "结束时间（USD） yyyy-MM-dd HH:mm:ss")
        private String endTime;

        @ApiModelProperty(value = "创建时间")
        private String createTime;

        @ApiModelProperty(value = "修改时间")
        private String modifyTime;
    }

    @Getter
    @Setter
    public static class ClinicDTO {
        @ApiModelProperty("id")
        private Long id;

        @ApiModelProperty("code")
        private String code;

        @ApiModelProperty("name")
        private String name;
    }
}
