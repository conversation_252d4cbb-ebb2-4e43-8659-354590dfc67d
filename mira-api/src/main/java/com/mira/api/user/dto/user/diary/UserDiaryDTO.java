package com.mira.api.user.dto.user.diary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel("用户日记记录")
public class UserDiaryDTO {
    @ApiModelProperty("日记时间戳,年月日（不带时分秒）")
    private Long diaryDay;

    @ApiModelProperty("日记时间（年月日）")
    private String diaryDayStr;

    @ApiModelProperty("日记")
    private String notes;

    @ApiModelProperty("温度")
    private BigDecimal tempC;

    @ApiModelProperty("温度")
    private BigDecimal tempF;

    @ApiModelProperty("温度测试时间")
    private String tempTime;

    @ApiModelProperty("体重")
    private BigDecimal weightK;

    @ApiModelProperty("体重")
    private BigDecimal weightL;

    @ApiModelProperty("心情")
    private String mood;

    @ApiModelProperty("是否同房")
    private String sex;

    @ApiModelProperty("白带状态(粘液状态)")
    private String mucusType;

    @ApiModelProperty("白带流量")
    private String mucusFlow;

    @ApiModelProperty("验孕记录")
    private Boolean pregnant;

    @ApiModelProperty("排卵测试记录")
    private Boolean opk;

    @ApiModelProperty("出血状态")
    private String flowAndSpotting;

    @ApiModelProperty("子宫位置")
    private String cervicalPosition;

    @ApiModelProperty("Firmness")
    private String cervicalFirmness;

    @ApiModelProperty("Openness")
    private String cervicalOpenness;

    @ApiModelProperty("Challenge with glucose control")
    private String glucoseControl;

    @ApiModelProperty("创建时间")
    private String createTimeStr;
}
