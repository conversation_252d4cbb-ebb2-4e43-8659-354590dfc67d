package com.mira.api.user.enums.daily;

import lombok.Getter;

/**
 * 锻炼
 * key:exercise
 *
 * <AUTHOR>
 */
@Getter
public enum DailyStatusMoodExerciseEnum {
    GREAT("great", "Yes, it felt great"),
    HARD("hard", "Yes, it felt hard"),
    NOT("n", "I don’t exercise"),
    SLUGGISH("sluggish", "No, feeling sluggish");

    private final String value;
    private final String description;

    DailyStatusMoodExerciseEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
