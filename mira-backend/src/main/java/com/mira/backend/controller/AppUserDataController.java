package com.mira.backend.controller;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.backend.AppDataUploadDTO;
import com.mira.api.bluetooth.dto.backend.AppDataUploadListDTO;
import com.mira.api.bluetooth.dto.backend.DataUploadPageRequestDTO;
import com.mira.api.bluetooth.dto.backend.DataUploadResponseDTO;
import com.mira.api.bluetooth.provider.IDataUploadProvider;
import com.mira.api.user.provider.IUserProvider;
import com.mira.backend.config.logs.AdminLog;
import com.mira.backend.dal.dao.user.SnToRecordDAO;
import com.mira.backend.dal.entity.user.SnToRecordEntity;
import com.mira.backend.exception.BackendException;
import com.mira.backend.pojo.vo.user.DataUploadVO;
import com.mira.backend.service.IAdminLoginService;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2021-08-17
 **/
@Api(tags = "04.app用户测试数据管理")
@RestController
@RequestMapping("app/user-data")
@Slf4j
public class AppUserDataController {
    @Resource
    private IAdminLoginService adminLoginService;
    @Resource
    private IDataUploadProvider dataUploadProvider;
    @Resource
    private IUserProvider userProvider;
    @Resource
    private SnToRecordDAO snToRecordDAO;

    /**
     * 1. 用户测试数据分页列表
     *
     * @param pageParam
     * @return
     */
    @ApiOperation("1. 用户测试数据分页列表")
    @PostMapping("/data-page")
    public PageResult<DataUploadVO> dataPage(@RequestBody DataUploadPageRequestDTO pageParam) {
        Long userId = pageParam.getUserId();
        adminLoginService.checkUserInfoPermission(userId);

        DataUploadResponseDTO dataUploadResponseDTO = dataUploadProvider.dataUploadPage(pageParam).getData();
        long total = dataUploadResponseDTO.getTotal();
        List<AppDataUploadListDTO> appDataUploadListDTOS = dataUploadResponseDTO.getAppDataUploadListDTOS();

        List<DataUploadVO> dataUploadVOS = new ArrayList<>();
        if (appDataUploadListDTOS != null && !appDataUploadListDTOS.isEmpty()) {
            for (AppDataUploadListDTO appDataUploadListDTO : appDataUploadListDTOS) {
                DataUploadVO dataUploadVO = new DataUploadVO();
                BeanUtil.copyProperties(appDataUploadListDTO, dataUploadVO);

                SnToRecordEntity snToRecordEntity = snToRecordDAO.getBySn(appDataUploadListDTO.getSn());
                if (!ObjectUtils.isEmpty(snToRecordEntity)) {
                    dataUploadVO.setSnBatchId(snToRecordEntity.getBatchId());
                }
                dataUploadVOS.add(dataUploadVO);
            }
        }

        return new PageResult<>(dataUploadVOS, total, pageParam.getSize(), pageParam.getCurrent());
    }

    @ApiOperation("5. V4删除存在的测试数据")
    @PostMapping("/v4-delete-exist-data")
    @Transactional(rollbackFor = Exception.class)
    @AdminLog("v4删除存在的测试数据")
    public void v4DeleteExistData(@RequestParam Long id, @RequestParam Integer deleted, @RequestParam String sysNote) {
        if (deleted == null || (deleted != 0 && deleted != 1)) {
            throw new BackendException("param error");
        }
        if (id == null) {
            return;
        }
        AppDataUploadDTO dataUploadDTO = dataUploadProvider.getByIdWithDeleted(id).getData();
        dataUploadProvider.changeDeleteStatus(id, dataUploadDTO.getUserId(), deleted, sysNote);
    }

    @ApiOperation("6. V4修改数据测试完成时间")
    @PostMapping("/v4-change-complete-time")
    @Transactional(rollbackFor = Exception.class)
    @AdminLog("修改数据测试完成时间")
    public void v4ChangeCompleteTime(@RequestParam Long id, @RequestParam String newCompleteTime,
                                     @RequestParam String sysNote) {
        if (StringUtils.isBlank(newCompleteTime)) {
            throw new BackendException("param erro");
        }
        if (id == null) {
            return;
        }
        AppDataUploadDTO dataUploadDTO = dataUploadProvider.getByIdWithDeleted(id).getData();

        String dataTimeZone = dataUploadDTO.getTimeZone();
        dataUploadProvider.changeCompleteTime(id, dataTimeZone, newCompleteTime, sysNote);
    }

    /**
     * 编辑用户经期
     * 仅给老用户使用
     * （注册登录成功过的用户称为老用户）
     */
    @ApiOperation(value = "7. V4帮助编辑用户经期", notes = "V4帮助编辑用户经期")
    @GetMapping("/v4-edit-period")
    @Transactional(rollbackFor = Exception.class)
    public void v4EditPeriod(@RequestParam Long userId) {
        userProvider.systemEditPeriod(userId);
    }

//    @ApiOperation(value = "8. 脚本编辑经期", notes = "脚本编辑经期")
//    @GetMapping("/script-edit-period")
//    @Transactional(rollbackFor = Exception.class)
//    public void scriptEditPeriod() throws IOException {
//        List<Long> userIds = new TxtUtil().readUserIds();
//        log.info("一共需要处理【{}】个email用户", userIds.size());
//        List<Long> errorEmails = new ArrayList<>();
//        for (int i = 0; i < userIds.size(); i++) {
//            Long userId = userIds.get(i);
//            log.info("开始处理第【{}】个email用户【{}】", i, userId);
//
//            try {
//                dataUploadProvider.systemEditPeriod(userId);
//            } catch (Exception e) {
//                log.info("第【{}】个email用户【{}】报错", i, userId);
//                errorEmails.add(userId);
//                continue;
//            }
//            log.info("结束处理第【{}】个email用户【{}】", i, userId);
//        }
//
//        log.info("error email用户个数【{}】", errorEmails.size());
//        for (Long errorId : errorEmails) {
//            log.info(String.valueOf(errorId));
//        }
//        log.info("结束程序处理");
//    }


}
