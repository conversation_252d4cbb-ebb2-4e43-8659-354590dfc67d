spring:
  application:
    name: mira-backend
  profiles:
    active: dev
  mvc:
    pathmatch:
      # spring boot 2.6 以上兼容 swagger3
      matching-strategy: ant_path_matcher
  cloud:
    loadbalancer:
      nacos:
        enabled: true

feign:
  client:
    config:
      default:
        connectTimeout: 30000
        readTimeout: 60000
  httpclient:
    enabled: false
  okhttp:
    enabled: true

server:
  port: 8099
  shutdown: graceful