package com.mira.bluetooth.handler.event.requestlog;

import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.bluetooth.dto.algorithm.request.BaseRequest;
import org.springframework.context.ApplicationEvent;

/**
 * Algorithm log
 *
 * <AUTHOR>
 */
public class AlgorithmLogEvent extends ApplicationEvent {
    private final Long userId;
    private final String timeZone;
    private final BaseRequest request;
    private final String errorMsg;
    private final AlgorithmRequestTypeEnum algorithmRequestTypeEnum;

    public AlgorithmLogEvent(Long userId, String timeZone, BaseRequest request,
                             AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        super("");
        this.userId = userId;
        this.timeZone = timeZone;
        this.request = request;
        this.errorMsg = "";
        this.algorithmRequestTypeEnum = algorithmRequestTypeEnum;
    }

    public AlgorithmLogEvent(Long userId, String timeZone, BaseRequest request,
                             AlgorithmRequestTypeEnum algorithmRequestTypeEnum,
                             String errorMsg) {
        super("");
        this.userId = userId;
        this.timeZone = timeZone;
        this.request = request;
        this.errorMsg = errorMsg;
        this.algorithmRequestTypeEnum = algorithmRequestTypeEnum;
    }

    public Long getUserId() {
        return userId;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public BaseRequest getRequest() {
        return request;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public AlgorithmRequestTypeEnum getAlgorithmRequestTypeEnum() {
        return algorithmRequestTypeEnum;
    }
}
