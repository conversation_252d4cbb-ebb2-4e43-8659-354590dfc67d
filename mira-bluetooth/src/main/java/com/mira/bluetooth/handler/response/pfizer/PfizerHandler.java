package com.mira.bluetooth.handler.response.pfizer;

import com.mira.api.user.dto.user.SysTipsDTO;
import com.mira.bluetooth.controller.vo.DataHistoryVO;
import com.mira.bluetooth.controller.vo.DataUploadVO;
import com.mira.bluetooth.handler.response.IResponseDataHandler;
import com.mira.bluetooth.handler.response.ResponseDataHandler;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.web.properties.SysDictProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * handle pfizer account data
 *
 * <AUTHOR>
 */
@Component
public class PfizerHandler implements IResponseDataHandler {
    @Resource
    private SysDictProperties sysDictProperties;

    public PfizerHandler() {
        ResponseDataHandler.set(this);
    }

    @Override
    public void handle(Object data) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        if (loginInfo == null) {
            return;
        }
        Long userId = loginInfo.getId();
        List<Long> pfizerAccounts = sysDictProperties.getPfizerAccounts();
        if (!pfizerAccounts.contains(userId)) {
            return;
        }

        /*
        No results visible in the app after uploading them from the analyzer
         */
        if (data instanceof DataUploadVO) {
            handleDataUploadVO(data);
        }
    }

    private void handleDataUploadVO(Object data) {
        DataUploadVO dataUploadVO = (DataUploadVO) data;
        dataUploadVO.setTips(new SysTipsDTO());
        DataHistoryVO dataHistoryDTO = dataUploadVO.getDataHistoryDTO();
        dataHistoryDTO.setTestingDTOS(new ArrayList<>());
        dataHistoryDTO.setWandTestDataList(new ArrayList<>());
        dataHistoryDTO.setNumOfTest(null);
    }
}
