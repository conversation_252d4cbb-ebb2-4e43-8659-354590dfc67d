package com.mira.bluetooth.handler.testresult;

import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import com.mira.bluetooth.handler.ITestResultHandler;
import com.mira.core.consts.enums.WandTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * PDG
 *
 * <AUTHOR>
 */
@Component
public class PdgTestResultHandler implements ITestResultHandler<AppDataUploadEntity, HormoneDTO.TestResult> {
    @PostConstruct
    public void init() {
        TestResultHandler.set("0" + WandTypeEnum.PDG.getString(), this);
    }

    @Override
    public void handle(AppDataUploadEntity dataUploadEntity, HormoneDTO.TestResult testResult) {
        testResult.setWand_type(WandTypeEnum.PDG.getInteger());
        testResult.setValue1(dataUploadEntity.getT1ConValue().floatValue());
    }
}
