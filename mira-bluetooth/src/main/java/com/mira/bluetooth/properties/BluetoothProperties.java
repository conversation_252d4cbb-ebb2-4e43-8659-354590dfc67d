package com.mira.bluetooth.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * bluetooth properties
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@RefreshScope
public class BluetoothProperties {
    @Value("${algorithm.client}")
    private String client;

    @Value("${hormone.filter.year:2018}")
    private Integer hormineFilterYear;
}
