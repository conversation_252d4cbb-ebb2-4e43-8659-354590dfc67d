package com.mira.bluetooth.service.manager;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.user.dto.user.AppUserInfoDTO;
import com.mira.api.user.provider.IUserProvider;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 周期是否规则的校验
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-08-13
 **/
@Slf4j
@Component
public class CycleIrregularManager {
    @Resource
    private IUserProvider userProvider;

    public void execuse(Long userId, String timeZone, AlgorithmReturnDTO algorithmReturnDTO) {
        List<CycleDataDTO> cycleDataDTOS = algorithmReturnDTO.getCycle_data();
        //For Menopause mode we need to define the users who have irregular cycles to provide them with different testing schedule.
        //So if users cycle length varies more than 7 days (>7 days) between consecutive cycles for the last 6 months:
        //      for 2 cycles in a row and more, we should mark them in system as irregular cycle for the algorithm.
        List<CycleDataDTO> userRealCycleDataDTOS =
                cycleDataDTOS.stream()
                        .filter(cycleDataDTO -> cycleDataDTO.getCycle_status() == CycleStatusEnum.REAL_CYCLE.getStatus())
                        .collect(Collectors.toList());
        if (userRealCycleDataDTOS.size() < 6) {
            return;
        }
        AppUserInfoDTO appUserInfoDTO = userProvider.getUserInfoById(userId).getData();
        Integer dbDefinedIrregularCycle = appUserInfoDTO.getDefinedIrregularCycle();
        if (dbDefinedIrregularCycle != null && dbDefinedIrregularCycle != 0) {
            //无须继续处理
            return;
        }

        Integer definedIrregularCycle = 0;

        int irregularCount = 0; // 记录连续两个周期长度差大于7天的次数
        // 前一个周期的长度
        Integer previousLength = null;
        // 遍历所有周期数据
        for (CycleDataDTO cycle : userRealCycleDataDTOS) {
            if (previousLength != null && Math.abs(cycle.getLen_cycle() - previousLength) > 7) {
                // 如果当前周期与上一个周期的长度差大于7天
                irregularCount++;
                if (irregularCount >= 2) {
                    // 如果连续两次周期不规则，返回true
                    definedIrregularCycle = 1;
                    break;
                }
            } else {
                // 如果当前周期与上一个周期的长度差不大于7天，重置不规则计数器
                irregularCount = 0;
            }
            // 更新前一个周期的长度
            previousLength = cycle.getLen_cycle();
        }

        //条件二：If the gap is not more than 60 days since the user’s last period start date, then not send and  not mark the flag
        String recentPeriodStartDate =
                userRealCycleDataDTOS.get(userRealCycleDataDTOS.size() - 1).getDate_period_start();
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        int subtractFromRecentPeriodStartDate = LocalDateUtil.minusToDay(today, recentPeriodStartDate);

        log.info("user:【{}】,realCycle.size:【{}】,definedIrregularCycle:【{}】,dbValue:【{}】," +
                        "subtractFromRecentPeriodStartDate:【{}】", userId,
                userRealCycleDataDTOS.size(),
                definedIrregularCycle, dbDefinedIrregularCycle, subtractFromRecentPeriodStartDate);
        if (subtractFromRecentPeriodStartDate < 60) {
            return;
        }

        if (dbDefinedIrregularCycle == null || !definedIrregularCycle.equals(dbDefinedIrregularCycle)) {
            appUserInfoDTO.setDefinedIrregularCycle(definedIrregularCycle);
            userProvider.updateUserInfo(appUserInfoDTO);
        }
    }
}
