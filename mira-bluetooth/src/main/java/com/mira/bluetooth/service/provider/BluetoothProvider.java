package com.mira.bluetooth.service.provider;

import com.mira.api.bluetooth.dto.bbt.BBTInfoDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.wand.*;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.bluetooth.dal.dao.AppDataUploadDAO;
import com.mira.bluetooth.dal.dao.AppUserBbtBindDAO;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import com.mira.bluetooth.dal.entity.AppUserBbtBindEntity;
import com.mira.bluetooth.service.manager.BluetoothManager;
import com.mira.bluetooth.service.util.DailyWandTestDataHelper;
import com.mira.bluetooth.service.util.WandsParamRecordHelper;
import com.mira.core.response.CommonResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 蓝牙服务接口实现
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@RestController
public class BluetoothProvider implements IBluetoothProvider {
    @Resource
    private AppDataUploadDAO appDataUploadDAO;
    @Resource
    private AppUserBbtBindDAO appUserBbtBindDAO;

    @Resource
    private WandsParamRecordHelper wandsParamRecordHelper;
    @Resource
    private DailyWandTestDataHelper dailyWandTestDataHelper;
    @Resource
    private BluetoothManager bluetoothManager;

    @Override
    public CommonResult<WandTestBiomarkerDTO> getWandTestBiomarkerData(Long id, String bioMarker, Integer trialFlag) {
        AppDataUploadEntity dataUploadEntity = appDataUploadDAO.getById(id);

        String testWandType = dataUploadEntity.getTestWandType();
        WandsParamRecordDTO wandsParamRecordDTO = wandsParamRecordHelper.getByWandBatch3(dataUploadEntity.getTestWandBatch(),
                testWandType.startsWith("0") ? testWandType.substring(1) : testWandType);
        HormoneDTO hormoneDTO = bluetoothManager.buildHormoneDTO(dataUploadEntity, wandsParamRecordDTO, trialFlag);

        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS =
                dailyWandTestDataHelper.getDailyWandTestData(Collections.singletonList(hormoneDTO), dataUploadEntity.getUserId());
        WandTestBiomarkerDTO biomarkerDTO = wandTestBiomarkerDTOS
                .stream()
                .filter(wandTestBiomarkerDTO -> bioMarker.equals(wandTestBiomarkerDTO.getWandType()))
                .findFirst().orElse(null);

        return CommonResult.OK(biomarkerDTO);
    }

    @Override
    public CommonResult<List<WandTestBiomarkerDTO>> getDayWandBiomarkerData(WandTestDataDTO wandTestDataDTO) {
        return getDayWandBiomarkerData(wandTestDataDTO, null);
    }

    @Override
    public CommonResult<List<WandDiaryTestBiomarkerDTO>> getDayWandBiomarkerData(List<WandTestDataDTO> wandTestDataDTOS) {
        return getDayWandBiomarkerData(wandTestDataDTOS, null);
    }

    @Override
    public CommonResult<List<WandTestBiomarkerDTO>> getDayWandBiomarkerData(WandTestDataDTO wandTestDataDTO, Long userId) {
        String requestType = StringUtils.isBlank(wandTestDataDTO.getRequestType()) ? "user" : wandTestDataDTO.getRequestType();
        List<WandTestBiomarkerDTO> dailyWandTestDatas =
                dailyWandTestDataHelper.getDailyWandTestData(wandTestDataDTO.getHormoneDTO(), userId, requestType);
        if (wandTestDataDTO.getFilter()) {
            dailyWandTestDatas.removeIf(wandTestBiomarkerDTO ->
                    StringUtils.isNotBlank(wandTestBiomarkerDTO.getEcode()) && !wandTestBiomarkerDTO.getEcode().startsWith("B"));
        }

        return CommonResult.OK(dailyWandTestDatas);
    }

    @Override
    public CommonResult<List<WandDiaryTestBiomarkerDTO>> getDayWandBiomarkerData(List<WandTestDataDTO> wandTestDataDTOS, Long userId) {
        ArrayList<WandDiaryTestBiomarkerDTO> wandTestBiomarkerDTOS = new ArrayList<>();

        for (WandTestDataDTO wandTestDataDTO : wandTestDataDTOS) {
            String requestType = StringUtils.isBlank(wandTestDataDTO.getRequestType()) ? "user" : wandTestDataDTO.getRequestType();
            List<WandTestBiomarkerDTO> dailyWandTestDatas = dailyWandTestDataHelper.getDailyWandTestData(wandTestDataDTO.getHormoneDTO(), userId, requestType);
            if (wandTestDataDTO.getFilter()) {
                dailyWandTestDatas.removeIf(wandTestBiomarkerDTO ->
                        StringUtils.isNotBlank(wandTestBiomarkerDTO.getEcode()) && !wandTestBiomarkerDTO.getEcode().startsWith("B"));
            }

            WandDiaryTestBiomarkerDTO wandTestBiomarkerDTO = new WandDiaryTestBiomarkerDTO();
            wandTestBiomarkerDTO.setDate(wandTestDataDTO.getDate());
            wandTestBiomarkerDTO.setWandTestBiomarkerDTOS(dailyWandTestDatas);
            wandTestBiomarkerDTOS.add(wandTestBiomarkerDTO);
        }

        return CommonResult.OK(wandTestBiomarkerDTOS);
    }

    @Deprecated
    @Override
    public CommonResult<List<TestRemindDTO>> getTestRemind(DayTestProductsDTO dayTestProductsDTO) {
        String date = dayTestProductsDTO.getDate();
        List<CycleDataDTO> cycleDataDTOS = dayTestProductsDTO.getCycleDataDTOS();
        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = dayTestProductsDTO.getWandTestBiomarkerDTOS();
        List<TestRemindDTO> testingProducts = dailyWandTestDataHelper.getTestingProducts(date, cycleDataDTOS, wandTestBiomarkerDTOS);

        return CommonResult.OK(testingProducts);
    }

    @Deprecated
    @Override
    public CommonResult<List<TestDiaryRemindDTO>> getTestRemind(List<DayTestProductsDTO> dayTestProductsDTOS) {
        List<TestDiaryRemindDTO> testDiaryRemindDTOS = new ArrayList<>();

        for (DayTestProductsDTO dayTestProductsDTO : dayTestProductsDTOS) {
            TestDiaryRemindDTO testDiaryRemindDTO = new TestDiaryRemindDTO();
            testDiaryRemindDTO.setDate(dayTestProductsDTO.getDate());
            testDiaryRemindDTO.setTestRemindDTOS(getTestRemind(dayTestProductsDTO).getData());
            testDiaryRemindDTOS.add(testDiaryRemindDTO);
        }

        return CommonResult.OK(testDiaryRemindDTOS);
    }

    @Override
    public CommonResult<WandsParamRecordDTO> getWandParamRecord(String wandBatch3, String uStripType) {
        return CommonResult.OK(wandsParamRecordHelper.getByWandBatch3(wandBatch3, uStripType));
    }

    @Override
    public CommonResult<BBTInfoDTO> getBbtInfo(Long userId) {
        AppUserBbtBindEntity bbtBindEntity = appUserBbtBindDAO.getByUserId(userId);
        if (bbtBindEntity == null) {
            return CommonResult.OK();
        }

        BBTInfoDTO bbtInfoDTO = new BBTInfoDTO();
        bbtInfoDTO.setBind(bbtBindEntity.getBindStatus() == null ? 0 : bbtBindEntity.getBindStatus());
        bbtInfoDTO.setFirstBindBbtTime(bbtBindEntity.getCreateTimeStr());
        return CommonResult.OK(bbtInfoDTO);
    }
}
