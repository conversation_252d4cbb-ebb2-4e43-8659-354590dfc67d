package com.mira.bluetooth.service.util;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestingProductDayDTO;
import com.mira.api.bluetooth.dto.wand.TestRemindDTO;
import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.bluetooth.enums.DeviceErrorCodeEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.BiomarkerNameEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.LocalDateUtil;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 构建每日测试数据
 */
@Slf4j
@Component
public class DailyWandTestDataHelper {
    @Resource
    private WandsParamDataVOHelper wandsParamDataVOHelper;
    @Resource
    private SysDictProperties sysDictProperties;

    public List<WandTestBiomarkerDTO> getDailyWandTestData(List<HormoneDTO> dailyHormoneDatas, Long userId) {
        return getDailyWandTestData(dailyHormoneDatas, userId, "user");
    }

    /**
     * 获取测试数据
     */
    public List<WandTestBiomarkerDTO> getDailyWandTestData(List<HormoneDTO> dailyHormoneDatas, Long userId, String requestType) {
        if (CollectionUtils.isEmpty(dailyHormoneDatas)) {
            return new ArrayList<>();
        }
        List<WandTestBiomarkerDTO> wandTestDataList = new ArrayList<>();

        // 同一试剂棒标记
        int sameMark = 0;

        for (HormoneDTO hormoneDTO : dailyHormoneDatas) {
            HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
            Integer wandType = testResult.getWand_type();

            switch (WandTypeEnum.get(wandType)) {
                case LH:
                    buildLh(hormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case E3G_LH:
                    buildE3gLh(hormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case PDG:
                    buildPdg(hormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case E3G_HIGH_RANGE:
                    buildE3gHighRange(hormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case LH_E3G_PDG:
                    buildLhE3gPdg(hormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case HCG:
                    buildHcg(hormoneDTO, wandTestDataList, userId, requestType, ++sameMark);
                    break;
                case HCG_QUALITATIVE:
                    buildHcgQualitative(hormoneDTO, wandTestDataList, requestType, ++sameMark);
                    break;
                case FSH:
                    buildFsh(hormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case UNKNOWN:
                    buildUnknown(hormoneDTO, wandTestDataList, ++sameMark);
                    break;
            }
        }

        return wandTestDataList;
    }

    private String operationEerrorCode(HormoneDTO.TestResult testResult) {
        String ecode = testResult.getEcode();
        // TODO 2021年9月28日：关闭Ecode为B01和B02的显示，防止用户投诉
        if (DeviceErrorCodeEnum.B01.getValue().equals(ecode) || DeviceErrorCodeEnum.B02.getValue().equals(ecode)) {
            ecode = null;
        }
        return ecode;
    }

    private void buildLh(HormoneDTO hormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int lhMark) {
        String testTime = hormoneDTO.getTest_time();
        String wandBatch3 = hormoneDTO.getWandBatch3();
        HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
        String ecode = operationEerrorCode(testResult);

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.LH.getName(), testTime, ecode);
        String valueResult = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.LH, testResult.getValue1(), null, null, wandBatch3);
        wandTestData.setTestValue(valueResult);
        wandTestData.setSameWandMark(lhMark);
        wandTestData.setProductCode("0".concat(WandTypeEnum.LH.getString()));
        checkError02(wandTestDataList, testResult, BiomarkerNameEnum.LH.getName());
        wandTestDataList.add(wandTestData);
    }

    private void buildE3gLh(HormoneDTO hormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int e3gLhMark) {
        String testTime = hormoneDTO.getTest_time();
        String wandBatch3 = hormoneDTO.getWandBatch3();
        HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
        String ecode = operationEerrorCode(testResult);

        if (StringUtils.isNotBlank(ecode) && !ecode.startsWith("B")) {
            // 只显示一条数据(异常)
            WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.E3G.getName(), testTime, ecode);
            String valueResult = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.E3G_LH, null, testResult.getValue2(), null, wandBatch3);
            wandTestData.setTestValue(valueResult);
            wandTestData.setSameWandMark(e3gLhMark);
            wandTestData.setProductCode("0".concat(WandTypeEnum.E3G_LH.getString()));
            checkError02(wandTestDataList, testResult, BiomarkerNameEnum.E3G.getName());
            wandTestDataList.add(wandTestData);
        } else {
            // 显示两条
            WandTestBiomarkerDTO wandTestData1 = new WandTestBiomarkerDTO(BiomarkerNameEnum.LH.getName(), testTime, ecode);
            String valueResult1 = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.E3G_LH, null, testResult.getValue2(), null, wandBatch3);
            wandTestData1.setTestValue(valueResult1);
            wandTestData1.setSameWandMark(e3gLhMark);
            wandTestData1.setProductCode("0".concat(WandTypeEnum.E3G_LH.getString()));
            wandTestDataList.add(wandTestData1);

            WandTestBiomarkerDTO wandTestData2 = new WandTestBiomarkerDTO(BiomarkerNameEnum.E3G.getName(), testTime, ecode);
            String valueResult2 = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.E3G_LH, testResult.getValue1(), null, null, wandBatch3);
            wandTestData2.setTestValue(valueResult2);
            wandTestData2.setSameWandMark(e3gLhMark);
            wandTestData2.setProductCode("0".concat(WandTypeEnum.E3G_LH.getString()));
            wandTestDataList.add(wandTestData2);
        }
    }

    private void buildPdg(HormoneDTO hormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int pdgMark) {
        String testTime = hormoneDTO.getTest_time();
        String wandBatch3 = hormoneDTO.getWandBatch3();
        HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
        String ecode = operationEerrorCode(testResult);

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.PDG.getName(), testTime, ecode);
        String valueResult = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.PDG, testResult.getValue1(), null, null, wandBatch3);
        wandTestData.setTestValue(valueResult);
        wandTestData.setSameWandMark(pdgMark);
        wandTestData.setProductCode("0".concat(WandTypeEnum.PDG.getString()));
        checkError02(wandTestDataList, testResult, BiomarkerNameEnum.PDG.getName());
        wandTestDataList.add(wandTestData);
    }

    private void buildE3gHighRange(HormoneDTO hormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int e3gHighRangeMark) {
        String testTime = hormoneDTO.getTest_time();
        String wandBatch3 = hormoneDTO.getWandBatch3();
        HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
        String ecode = operationEerrorCode(testResult);

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.E3G.getName(), testTime, ecode);
        String valueResult = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.E3G_HIGH_RANGE, testResult.getValue1(), null, null, wandBatch3);
        wandTestData.setTestValue(valueResult);
        wandTestData.setSameWandMark(e3gHighRangeMark);
        wandTestData.setProductCode(WandTypeEnum.E3G_HIGH_RANGE.getString());
        checkError02(wandTestDataList, testResult, BiomarkerNameEnum.E3G.getName());
        wandTestDataList.add(wandTestData);
    }

    private void buildLhE3gPdg(HormoneDTO hormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int lhE3gPdgMark) {
        String testTime = hormoneDTO.getTest_time();
        String wandBatch3 = hormoneDTO.getWandBatch3();
        HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
        String ecode = operationEerrorCode(testResult);

        if (StringUtils.isNotBlank(ecode) && !ecode.startsWith("B")) {
            // 只显示一条数据(异常)
            WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.PDG.getName(), testTime, ecode);
            String valueResult = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.LH_E3G_PDG, null, testResult.getValue2(), null, wandBatch3);
            wandTestData.setTestValue(valueResult);
            wandTestData.setSameWandMark(lhE3gPdgMark);
            wandTestData.setProductCode(WandTypeEnum.LH_E3G_PDG.getString());
            checkError02(wandTestDataList, testResult, BiomarkerNameEnum.PDG.getName());
            wandTestDataList.add(wandTestData);
        } else {
            // 显示三条
            WandTestBiomarkerDTO wandTestData1 = new WandTestBiomarkerDTO(BiomarkerNameEnum.LH.getName(), testTime, ecode);
            String valueResult1 = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.LH_E3G_PDG, testResult.getValue1(), null, null, wandBatch3);
            wandTestData1.setTestValue(valueResult1);
            wandTestData1.setSameWandMark(lhE3gPdgMark);
            wandTestData1.setProductCode(WandTypeEnum.LH_E3G_PDG.getString());
            wandTestDataList.add(wandTestData1);

            WandTestBiomarkerDTO wandTestData2 = new WandTestBiomarkerDTO(BiomarkerNameEnum.PDG.getName(), testTime, ecode);
            String valueResult2 = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.LH_E3G_PDG, null, testResult.getValue2(), null, wandBatch3);
            wandTestData2.setTestValue(valueResult2);
            wandTestData2.setSameWandMark(lhE3gPdgMark);
            wandTestData2.setProductCode(WandTypeEnum.LH_E3G_PDG.getString());
            wandTestDataList.add(wandTestData2);

            WandTestBiomarkerDTO wandTestData3 = new WandTestBiomarkerDTO(BiomarkerNameEnum.E3G.getName(), testTime, ecode);
            String valueResult3 = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.LH_E3G_PDG, null, null, testResult.getValue3(), wandBatch3);
            wandTestData3.setTestValue(valueResult3);
            wandTestData3.setSameWandMark(lhE3gPdgMark);
            wandTestData3.setProductCode(WandTypeEnum.LH_E3G_PDG.getString());
            wandTestDataList.add(wandTestData3);
        }
    }

    private void buildHcg(HormoneDTO hormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList,
                          Long userId, String requestType, int hcgMark) {
        String testTime = hormoneDTO.getTest_time();
        String wandBatch3 = hormoneDTO.getWandBatch3();
        HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
        String ecode = operationEerrorCode(testResult);

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.HCG.getName(), testTime, ecode);
        String valueResult = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.HCG, testResult.getValue1(), null, null, wandBatch3);
        // TODO HCG
        List<Long> hideHcgValueUserIds = sysDictProperties.getHideHcgValueUserIds();
        if (userId != null && hideHcgValueUserIds != null && hideHcgValueUserIds.contains(userId) && "user".equals(requestType)) {
            wandTestData.setTestValue("**");
        } else {
            wandTestData.setTestValue(valueResult);
        }
        wandTestData.setSameWandMark(hcgMark);
        wandTestData.setProductCode("0".concat(WandTypeEnum.HCG.getString()));
        checkError02(wandTestDataList, testResult, BiomarkerNameEnum.HCG.getName());
        wandTestDataList.add(wandTestData);
    }

    private void buildHcgQualitative(HormoneDTO hormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList,
                                     String requestType, int hcgQualitativeMark) {
        String testTime = hormoneDTO.getTest_time();
        HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
        String ecode = operationEerrorCode(testResult);

        Float value1 = testResult.getValue1();
        switch (requestType) {
            case "doctor":
                WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.HCG.getName(), testTime, ecode);
                wandTestData.setTestValue(value1 + " (HCG QUALITATIVE)");
                wandTestData.setSameWandMark(hcgQualitativeMark);
                wandTestData.setProductCode(WandTypeEnum.HCG_QUALITATIVE.getString());
                checkError02(wandTestDataList, testResult, BiomarkerNameEnum.HCG.getName());
                wandTestDataList.add(wandTestData);
                break;
            case "user":
            default:
                WandTestBiomarkerDTO wandTestDataDefault = new WandTestBiomarkerDTO(BiomarkerNameEnum.HCG2.getName(), testTime, ecode);
                String valueResultDefault = "Pregnant";
                if (value1 < 10) {
                    valueResultDefault = "Not Pregnant";
                }
                wandTestDataDefault.setTestValue(valueResultDefault);
                wandTestDataDefault.setSameWandMark(hcgQualitativeMark);
                wandTestDataDefault.setProductCode(WandTypeEnum.HCG_QUALITATIVE.getString());
                checkError02(wandTestDataList, testResult, BiomarkerNameEnum.HCG.getName());
                wandTestDataList.add(wandTestDataDefault);
                break;
        }
    }

    private void buildFsh(HormoneDTO hormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int fshMark) {
        String testTime = hormoneDTO.getTest_time();
        String wandBatch3 = hormoneDTO.getWandBatch3();
        HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
        String ecode = operationEerrorCode(testResult);

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.FSH.getName(), testTime, ecode);
        String valueResult = wandsParamDataVOHelper.buildTestValueResult(WandTypeEnum.FSH, testResult.getValue1(), null, null, wandBatch3);
        wandTestData.setTestValue(valueResult);
        wandTestData.setSameWandMark(fshMark);
        wandTestData.setProductCode(WandTypeEnum.FSH.getString());
        checkError02(wandTestDataList, testResult, BiomarkerNameEnum.FSH.getName());
        wandTestDataList.add(wandTestData);
    }

    private void buildUnknown(HormoneDTO hormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int unknownMark) {
        String testTime = hormoneDTO.getTest_time();
        HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
        String ecode = operationEerrorCode(testResult);

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(null, testTime, ecode);
        wandTestData.setTestValue(null);
        wandTestData.setSameWandMark(unknownMark);
        wandTestData.setProductCode(null);
        wandTestDataList.add(wandTestData);
    }

    /**
     * 温度报错的数据，同种试剂每天只显示最新的一条
     */
    private void checkError02(List<WandTestBiomarkerDTO> wandTestDataList, HormoneDTO.TestResult testResult, String wandType) {
        boolean anyMatch = wandTestDataList.stream().anyMatch(wandTestDataDTO ->
                wandType.equals(wandTestDataDTO.getWandType())
                        && (DeviceErrorCodeEnum.E02L.getValue().equals(wandTestDataDTO.getEcode())
                        || DeviceErrorCodeEnum.E02H.getValue().equals(wandTestDataDTO.getEcode())));

        if (anyMatch && (DeviceErrorCodeEnum.E02L.getValue().equals(testResult.getEcode())
                || DeviceErrorCodeEnum.E02H.getValue().equals(testResult.getEcode()))) {
            wandTestDataList.removeIf(wandTestDataDTO -> wandType.equals(wandTestDataDTO.getWandType())
                    && (DeviceErrorCodeEnum.E02L.getValue().equals(wandTestDataDTO.getEcode())
                    || DeviceErrorCodeEnum.E02H.getValue().equals(wandTestDataDTO.getEcode())));
        }
    }

    /**
     * 获取当天需要测试的产品列表
     */
    @Deprecated
    public List<TestRemindDTO> getTestingProducts(String dateStr,
                                                  List<CycleDataDTO> cycleDataDTOS,
                                                  List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS) {
        List<TestRemindDTO> testingDTOS = new ArrayList<>();
        List<String> testingProducts = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
            if (LocalDateUtil.minusToDay(dateStr, datePeriodStart) < 0
                    || LocalDateUtil.minusToDay(dateStr, dateCycleEnd) >= 0) {
                continue;
            }
            TestingProductDayDTO testingDayList = cycleDataDTO.getTesting_day_list();
            if (testingDayList == null) {
                continue;
            }
            List<String> product03TestingDayList = testingDayList.getProduct03();
            List<String> product02TestingDayList = testingDayList.getProduct02();
            List<String> product09TestingDayList = testingDayList.getProduct09();
            List<String> product12TestingDayList = testingDayList.getProduct12();
            List<String> product14TestingDayList = testingDayList.getProduct14();
            List<String> product16TestingDayList = testingDayList.getProduct16();
            if (product03TestingDayList != null && product03TestingDayList.contains(dateStr)) {
                testingProducts.add("0".concat(WandTypeEnum.E3G_LH.getString()));
            }
            if (product02TestingDayList != null && product02TestingDayList.contains(dateStr)) {
                testingProducts.add("0".concat(WandTypeEnum.HCG.getString()));
            }
            if (product09TestingDayList != null && product09TestingDayList.contains(dateStr)) {
                testingProducts.add("0".concat(WandTypeEnum.PDG.getString()));
            }
            if (product12TestingDayList != null && product12TestingDayList.contains(dateStr)) {
                testingProducts.add(WandTypeEnum.LH_E3G_PDG.getString());
            }
            if (product14TestingDayList != null && product14TestingDayList.contains(dateStr)) {
                testingProducts.add(WandTypeEnum.HCG_QUALITATIVE.getString());
            }
            if (product16TestingDayList != null && product16TestingDayList.contains(dateStr)) {
                testingProducts.add(WandTypeEnum.FSH.getString());
            }
        }

        Set<String> biomarkers = wandTestBiomarkerDTOS
                .stream()
                .filter(wandTestBiomarkerDTO -> StringUtils.isBlank(wandTestBiomarkerDTO.getEcode())
                        || (wandTestBiomarkerDTO.getEcode().startsWith("B") && !DeviceErrorCodeEnum.B01.getValue().equals(wandTestBiomarkerDTO.getEcode())))
                .map(WandTestBiomarkerDTO::getWandType)
                .collect(Collectors.toSet());

        for (String testingProduct : testingProducts) {
            if ("0".concat(WandTypeEnum.E3G_LH.getString()).equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.E3G.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.E3G.getName());
                    testingDTOS.add(testingDTO);
                }
                if (!biomarkers.contains(BiomarkerNameEnum.LH.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.LH.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if ("0".concat(WandTypeEnum.HCG.getString()).equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.HCG.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.HCG.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if ("0".concat(WandTypeEnum.PDG.getString()).equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.PDG.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.PDG.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if (WandTypeEnum.LH_E3G_PDG.getString().equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.E3G.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.E3G.getName());
                    testingDTOS.add(testingDTO);
                }
                if (!biomarkers.contains(BiomarkerNameEnum.LH.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.LH.getName());
                    testingDTOS.add(testingDTO);
                }
                if (!biomarkers.contains(BiomarkerNameEnum.PDG.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.PDG.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if (WandTypeEnum.HCG_QUALITATIVE.getString().equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.HCG2.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.HCG2.getName());
                    testingDTOS.add(testingDTO);
                }
                continue;
            }

            if (WandTypeEnum.FSH.getString().equals(testingProduct)) {
                if (!biomarkers.contains(BiomarkerNameEnum.FSH.getName())) {
                    TestRemindDTO testingDTO = new TestRemindDTO();
                    testingDTO.setProductCode(testingProduct);
                    testingDTO.setBiomarker(BiomarkerNameEnum.FSH.getName());
                    testingDTOS.add(testingDTO);
                }
            }
        }

        return testingDTOS;
    }
}
