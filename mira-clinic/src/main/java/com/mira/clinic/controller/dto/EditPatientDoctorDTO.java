package com.mira.clinic.controller.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("修改病人关联的医生/护士请求参数")
public class EditPatientDoctorDTO {
    @ApiModelProperty(value = "病人id", required = true)
    private Long patientId;

    @ApiModelProperty("关联医生列表")
    private List<Long> tenantIds;
}
