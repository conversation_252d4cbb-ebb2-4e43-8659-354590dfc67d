package com.mira.clinic.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.api.clinic.dto.ClinicListDTO;
import com.mira.clinic.dal.entity.AppTenantEntity;
import com.mira.clinic.dal.mapper.AppTenantMapper;
import com.mira.clinic.dto.TenantBaseInfo;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AppTenantDAO extends ServiceImpl<AppTenantMapper, AppTenantEntity> {
    public AppTenantEntity getByTenantCode(String tenantCode) {
        return getOne(Wrappers.<AppTenantEntity>lambdaQuery()
                .eq(AppTenantEntity::getCode, tenantCode));
    }

    public long countByKeyword(String keyword) {
        return getBaseMapper().countByKeyword(keyword);
    }

    public List<ClinicListDTO> listByPage(int currIndex, int pageSize, String keyWord) {
        return getBaseMapper().listByPage(currIndex, pageSize, keyWord);
    }

    public List<ClinicListDTO> all() {
        return getBaseMapper().all();
    }

    public TenantBaseInfo getTenantBaseInfo(Long doctorId) {
        return getBaseMapper().getTenantBaseInfo(doctorId);
    }
}
