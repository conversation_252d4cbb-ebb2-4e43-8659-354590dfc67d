package com.mira.clinic.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.api.clinic.dto.TenantDoctorNurseListDTO;
import com.mira.clinic.dal.entity.AppTenantDoctorNurseEntity;
import com.mira.clinic.dal.mapper.AppTenantDoctorNurseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AppTenantDoctorNurseDAO extends ServiceImpl<AppTenantDoctorNurseMapper, AppTenantDoctorNurseEntity> {
    public List<AppTenantDoctorNurseEntity> listByDoctorId(Long doctorId) {
        return list(Wrappers.<AppTenantDoctorNurseEntity>lambdaQuery()
                .eq(AppTenantDoctorNurseEntity::getDoctorId, doctorId));
    }

    public List<AppTenantDoctorNurseEntity> listByNureseId(Long nurseId) {
        return list(Wrappers.<AppTenantDoctorNurseEntity>lambdaQuery()
                .eq(AppTenantDoctorNurseEntity::getNurseId, nurseId));
    }

    public List<TenantDoctorNurseListDTO> listNurseDTO(List<Long> doctorIds) {
        return baseMapper.listNurseDTO(doctorIds);
    }

    public List<TenantDoctorNurseListDTO> listDoctorDTO(List<Long> nurseIds) {
        return baseMapper.listDoctorDTO(nurseIds);
    }

    public void removeByDoctorId(Long doctorId) {
        baseMapper.removeByDoctorId(doctorId);
    }

    public void removeByDoctorIdAndNurseIds(Long doctorId, List<Long> nurseIds) {
        baseMapper.removeByDoctorIdAndNurseIds(doctorId, nurseIds);
    }

    public void removeByNurseId(Long nurseId) {
        baseMapper.removeByNurseId(nurseId);
    }

    public void removeByNurseIdAndDoctorIds(Long nurseId, List<Long> doctorIds) {
        baseMapper.removeByNurseIdAndDoctorIds(nurseId, doctorIds);
    }
}
