package com.mira.clinic.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-04-19
 **/
@Getter
@Setter
@TableName("app_tenant")
public class AppTenantEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户code
     */
    private String code;

    /**
     * 租户名称
     */
    private String name;

    /**
     * 类型：0:clinic;1:doctor; 默认0
     */
    private Integer type;

    /**
     * 租户图标
     */
    private String icon;

    /**
     * 通知图标
     */
    private String notificationIcon;

    /**
     * 初始密码
     */
    private String initPassword;

    /**
     * 租户描述
     */
    private String description;
}