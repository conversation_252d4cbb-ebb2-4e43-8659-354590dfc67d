package com.mira.clinic.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.api.clinic.dto.ClinicListDTO;
import com.mira.clinic.dal.entity.AppTenantEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppTenantMapper extends BaseMapper<AppTenantEntity> {
    List<ClinicListDTO> listByPage(
            @Param("currIndex") int currIndex,
            @Param("pageSize") int pageSize,
            @Param("keyWord") String keyWord);

    List<ClinicListDTO> all();

    @Delete("DELETE FROM app_tenant WHERE id = #{id}")
    void deleteClinicInfo(@Param("id") Long id);

    Long countByKeyword(@Param("keyWord") String keyWord);
}
