package com.mira.clinic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("TenantPatientListDTO")
public class TenantPatientListDTO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("病人状态:1:邮件邀请中；2:正常激活状态;3:用户账号已注销;4:用户账号注册中;")
    private Integer patientStatus;

    @ApiModelProperty("病人修改时间")
    private Long modifyTime;

    @ApiModelProperty("病人email")
    private String email;

    @ApiModelProperty("病人mobile")
    private String mobile;

    @ApiModelProperty("病人名称")
    private String nickName;

    @ApiModelProperty("病人编号")
    private String patientNumber;

    @ApiModelProperty("用户状态:0:禁用;1:正常;2:未激活")
    private Integer userStatus;

    @ApiModelProperty("用户删除状态:0未删除;1删除")
    private Integer userDeleted;

    @ApiModelProperty("是否新用户：0新用户，1老用户")
    private Integer fresh;
}
