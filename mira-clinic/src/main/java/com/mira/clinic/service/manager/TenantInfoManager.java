package com.mira.clinic.service.manager;

import cn.hutool.core.bean.BeanUtil;
import com.mira.clinic.dal.dao.AppTenantHipaaDAO;
import com.mira.clinic.dal.entity.AppTenantHipaaEntity;
import com.mira.clinic.dto.TenantBaseInfo;
import com.mira.clinic.dto.TenantInfoDTO;
import com.mira.clinic.dal.dao.AppTenantDAO;
import com.mira.clinic.dal.dao.AppTenantDoctorDAO;
import com.mira.clinic.dal.entity.AppTenantDoctorEntity;
import com.mira.clinic.dal.entity.AppTenantEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * 租户信息 manager
 *
 * <AUTHOR>
 */
@Component
public class TenantInfoManager {
    @Resource
    private AppTenantDoctorDAO appTenantDoctorDAO;
    @Resource
    private AppTenantDAO appTenantDAO;
    @Resource
    private AppTenantHipaaDAO appTenantHipaaDAO;

    public TenantInfoDTO getTenantInfo(Long id) {
        AppTenantDoctorEntity tenantDoctor = appTenantDoctorDAO.getById(id);
        if (ObjectUtils.isEmpty(tenantDoctor)) {
            return null;
        }
        return getTenantInfo(tenantDoctor);
    }

    public TenantInfoDTO getTenantInfo(AppTenantDoctorEntity tenantDoctor) {
        AppTenantEntity appTenantEntity = appTenantDAO.getByTenantCode(tenantDoctor.getTenantCode());
        TenantInfoDTO tenantInfoDTO = new TenantInfoDTO();
        BeanUtil.copyProperties(tenantDoctor, tenantInfoDTO);

        TenantBaseInfo tenantBaseInfo = new TenantBaseInfo();
        tenantBaseInfo.setIcon(appTenantEntity.getIcon());
        tenantBaseInfo.setName(appTenantEntity.getName());
        tenantBaseInfo.setDescription(appTenantEntity.getDescription());
        AppTenantHipaaEntity appTenantHipaa = appTenantHipaaDAO.getByDoctorId(tenantDoctor.getId());
        if (!ObjectUtils.isEmpty(appTenantHipaa)) {
            tenantBaseInfo.setAgreeHipaa(appTenantHipaa.getAgree());
        }
        tenantInfoDTO.setTenant(tenantBaseInfo);
        return tenantInfoDTO;
    }
}
