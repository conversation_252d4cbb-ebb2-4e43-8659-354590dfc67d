package com.mira.web.util;

import okhttp3.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * HTTP请求工具类，基于OkHttp3实现
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-01-16
 * @update: 重构并增加GET方法
 **/
public class OkHttpPostUtil {
    /**
     * JSON媒体类型
     */
    public static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    /**
     * 表单媒体类型
     */
    public static final MediaType FORM = MediaType.get("application/x-www-form-urlencoded; charset=utf-8");

    /**
     * 默认连接超时时间（秒）
     */
    private static final int DEFAULT_CONNECT_TIMEOUT = 10;

    /**
     * 默认读取超时时间（秒）
     */
    private static final int DEFAULT_READ_TIMEOUT = 30;

    /**
     * 默认写入超时时间（秒）
     */
    private static final int DEFAULT_WRITE_TIMEOUT = 10;

    /**
     * 默认HTTP客户端
     */
    private static final OkHttpClient DEFAULT_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(DEFAULT_READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.SECONDS)
            .build();

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String get(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        return executeRequest(request);
    }

    /**
     * 发送带请求头的GET请求
     *
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String get(String url, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .get();

        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::addHeader);
        }

        return executeRequest(requestBuilder.build());
    }

    /**
     * 发送POST请求，请求体为JSON格式
     *
     * @param url 请求URL
     * @param json JSON字符串
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String post(String url, String json) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        return executeRequest(request);
    }

    /**
     * 发送带请求头的POST请求，请求体为JSON格式
     *
     * @param url 请求URL
     * @param json JSON字符串
     * @param headers 请求头
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String post(String url, String json, Map<String, String> headers) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);

        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::addHeader);
        }

        return executeRequest(requestBuilder.build());
    }

    /**
     * 发送表单POST请求
     *
     * @param url 请求URL
     * @param formParams 表单参数
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String postForm(String url, Map<String, String> formParams) throws IOException {
        FormBody.Builder formBodyBuilder = new FormBody.Builder();

        // 添加表单参数
        if (formParams != null && !formParams.isEmpty()) {
            formParams.forEach(formBodyBuilder::add);
        }

        Request request = new Request.Builder()
                .url(url)
                .post(formBodyBuilder.build())
                .build();

        return executeRequest(request);
    }

    /**
     * 发送PUT请求，请求体为JSON格式
     *
     * @param url 请求URL
     * @param json JSON字符串
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String put(String url, String json) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .url(url)
                .put(body)
                .build();
        return executeRequest(request);
    }

    /**
     * 发送DELETE请求
     *
     * @param url 请求URL
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String delete(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .delete()
                .build();
        return executeRequest(request);
    }

    /**
     * 发送DELETE请求，请求体为JSON格式
     *
     * @param url 请求URL
     * @param json JSON字符串
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String delete(String url, String json) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .url(url)
                .delete(body)
                .build();
        return executeRequest(request);
    }

    /**
     * 执行HTTP请求
     *
     * @param request 请求对象
     * @return 响应内容
     * @throws IOException IO异常
     */
    private static String executeRequest(Request request) throws IOException {
        // 使用try-with-resources自动关闭响应体
        try (Response response = DEFAULT_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response.code() + " " + response.message());
            }

            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                return responseBody.string();
            }
            return null;
        }
    }
}
