spring:
  cloud:
    nacos:
      # 配置
      config:
        server-addr: 3.133.91.163:8848 # 地址
        namespace: 32fe18c2-fa4d-42ee-847d-f18d83c95930 # 命名空间
        group: DEFAULT_GROUP # 组
        file-extension: yaml # 扩展文件格式
        # 通用配置
        shared-configs[0]:
          data_id: common.yaml
          group: DEFAULT_GROUP
          refresh: true
        # 字典表
        shared-configs[1]:
          data_id: sys_dict.yaml
          group: DEFAULT_GROUP
          refresh: true
        # 数据库
        shared-configs[2]:
          data_id: datasource.yaml
          group: DEFAULT_GROUP
          refresh: true
        username: nacos
        password: <EMAIL>
      # 服务注册发现
      discovery:
        server-addr: 3.133.91.163:8848
        namespace: 32fe18c2-fa4d-42ee-847d-f18d83c95930
        group: DEFAULT_GROUP
        username: nacos
        password: kAiEr2022<PERSON><PERSON><EMAIL>

logging:
  config: classpath:logback/logback-${spring.profiles.active}.xml