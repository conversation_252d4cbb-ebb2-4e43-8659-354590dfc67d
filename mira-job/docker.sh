#!/bin/bash

IMAGE_NAME="mira-job"
CONTAINER_NAME="mira-job"
PORT=8087

echo "===== Building Maven project ====="
mvn clean package -DskipTests

echo "===== Building Docker image ====="
docker build -t $IMAGE_NAME .

# Check if container already exists and remove it
if [ "$(docker ps -aq -f name=$CONTAINER_NAME)" ]; then
    echo "===== Stopping and removing existing container ====="
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
fi

echo "===== Running Docker container ====="
docker run -d \
    --name $CONTAINER_NAME \
    -p $PORT:$PORT \
    $IMAGE_NAME