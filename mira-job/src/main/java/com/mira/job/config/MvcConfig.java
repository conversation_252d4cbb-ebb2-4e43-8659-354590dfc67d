package com.mira.job.config;

import com.mira.job.interceptor.TokenInterceptor;
import com.mira.web.interceptor.HeaderInterceptor;
import com.mira.web.interceptor.LogInterceptor;
import com.mira.web.properties.JwtProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.List;

/**
 * interceptor config
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Configuration
public class MvcConfig implements WebMvcConfigurer {
    @Resource
    private JwtProperties jwtProperties;

    @Bean
    public TokenInterceptor tokenInterceptor() {
        return new TokenInterceptor();
    }

    @Bean
    public HeaderInterceptor headerInterceptor() {
        return new HeaderInterceptor();
    }

    @Bean
    public LogInterceptor logInterceptor() {
        return new LogInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 白名单
        String[] resourcesPaths = jwtProperties.getResourcesWhite().split(",");
        //        registry.addInterceptor(headerInterceptor()).excludePathPatterns(resourcesPaths).order(1);
        //        registry.addInterceptor(tokenInterceptor()).excludePathPatterns(resourcesPaths).order(2);
        registry.addInterceptor(logInterceptor()).excludePathPatterns(resourcesPaths).order(3);
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(0, new MappingJackson2HttpMessageConverter());
    }
}
