package com.mira.job.consts.enums;

import lombok.Getter;

@Getter
public enum NotificationConditionEnum {
    FIREBASE_SCHEDULE_CONDITION_1(21, "After 1st week of compliant testing"),
    FIREBASE_SCHEDULE_CONDITION_2(22, "After 2nd completed cycle"),
    FIREBASE_SCHEDULE_CONDITION_3(23, "After 3rd month of using Mira"),

    AB_NOTIFICATION_OVULATION_CONFIRMED(31, "AB notification ovulation confirmed"),
    AB_NOTIFICATION_ONE_DAY_BEFORE_PREDICATED_PERIOD(32, "AB notification one day before predicated period"),
    AB_NOTIFICATION_AFTER_5_TESTS(33, "AB notification after 5 tests"),
    AB_NOTIFICATION_AFTER_15_TESTS(34, "AB notification after 15 tests"),

    TESTING_COMPLIANCE_CONDITION1(35, "Testing compliance condition1"),
    TESTING_COMPLIANCE_CONDITION2(36, "Testing compliance condition2"),
    TESTING_COMPLIANCE_CONDITION3(37, "Testing compliance condition3"),
    TESTING_COMPLIANCE_CONDITION4(38, "Testing compliance condition4"),
    TESTING_COMPLIANCE_CONDITION5(39, "Testing compliance condition5"),
    TESTING_COMPLIANCE_CONDITION6(40, "Testing compliance condition6"),
    TESTING_COMPLIANCE_MUTEX(41, "Testing compliance condition mutex"),
    TESTING_COMPLIANCE_CONDITION_MENOPAUSE1(42, "Testing compliance condition menopause1"),
    TESTING_COMPLIANCE_CONDITION_MENOPAUSE2(43, "Testing compliance condition menopause2"),
    TESTING_COMPLIANCE_CONDITION_MENOPAUSE3(44, "Testing compliance condition menopause3"),

    TESTING_MENOPAUSE_STAGE_1(45, "send the notification if they haven't started testing fsh in menopause mode"),

    TESTING_COMPLIANCE_FLOW_1ST_CONDITION1(50, "Testing compliance flow 1st condition1"),
    TESTING_COMPLIANCE_FLOW_1ST_CONDITION2(51, "Testing compliance flow 1st condition2"),
    TESTING_COMPLIANCE_FLOW_1ST_CONDITION3(52, "Testing compliance flow 1st condition3"),
    TESTING_COMPLIANCE_FLOW_1ST_CONDITION4(53, "Testing compliance flow 1st condition4"),
    TESTING_COMPLIANCE_FLOW_1ST_CONDITION5(54, "Testing compliance flow 1st condition5"),

    UPSELLING_FLOW_CONDITION1(60, "Upselling flow condition1"),
    UPSELLING_FLOW_CONDITION2(61, "Upselling flow condition2"),
    UPSELLING_FLOW_CONDITION3(62, "Upselling flow condition3"),
    UPSELLING_FLOW_CONDITION4(63, "Upselling flow condition4"),
    UPSELLING_FLOW_CONDITION5(64, "Upselling flow condition5"),
    UPSELLING_FLOW_CONDITION6(65, "Upselling flow condition6"),
    UPSELLING_FLOW_CONDITION7(66, "Upselling flow condition7"),
    UPSELLING_FLOW_CONDITION8(67, "Upselling flow condition8"),

    DESK_CUSTOM_NOTIFICATION_JOB(99, "后台系统自定义的定时任务")

    ;

    private final Integer value;
    private final String description;

    NotificationConditionEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
}
