package com.mira.job.dal.dao.master;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.job.dal.DataSourceName;
import com.mira.job.dal.entity.master.AppDataUploadEntity;
import com.mira.job.dal.mapper.master.AppDataUploadMapper;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * app_data_upload DAO
 *
 * <AUTHOR>
 */
@DS(DataSourceName.SLAVE)
@Repository
public class AppDataUploadDAO extends ServiceImpl<AppDataUploadMapper, AppDataUploadEntity> {

    public List<AppDataUploadEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppDataUploadEntity>lambdaQuery()
                .eq(AppDataUploadEntity::getUserId, userId)
                .in(AppDataUploadEntity::getError, Arrays.asList("00", "01", "02L", "02H", "03", "04", "05", "06"))
                .in(AppDataUploadEntity::getWarning, Arrays.asList("00", "01", "05", "06", "07", "09"))
                .orderByAsc(AppDataUploadEntity::getCompleteTime));
    }

    /**
     * 1. 调用算法数据有效：仅当error=00 warning in (00,05,09)
     *
     * @param userIds
     * @return
     */
    public List<AppDataUploadEntity> listValidDataByUserIds(List<Long> userIds) {
        return list(Wrappers.<AppDataUploadEntity>lambdaQuery()
                .in(AppDataUploadEntity::getUserId, userIds)
                .in(AppDataUploadEntity::getError, Arrays.asList("00"))
                .in(AppDataUploadEntity::getWarning, Arrays.asList("00", "05", "09"))
        );

    }

    public Long getTotalCount() {
        return baseMapper.getTotalCount();
    }
}
