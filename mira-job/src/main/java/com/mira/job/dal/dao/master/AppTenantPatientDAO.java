package com.mira.job.dal.dao.master;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.api.clinic.enums.ClinicStatusEnum;
import com.mira.job.dal.DataSourceName;
import com.mira.job.dal.entity.master.AppTenantPatientEntity;
import com.mira.job.dal.mapper.master.AppTenantPatientMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

//@DS(DataSourceName.SLAVE)
@Repository
public class AppTenantPatientDAO extends ServiceImpl<AppTenantPatientMapper, AppTenantPatientEntity> {
    public List<AppTenantPatientEntity> listAllActivePatients() {
        return list(Wrappers.<AppTenantPatientEntity>lambdaQuery()
                .eq(AppTenantPatientEntity::getStatus, ClinicStatusEnum.NORMAL.getCode())
                .isNotNull(AppTenantPatientEntity::getUserId)
                .select(AppTenantPatientEntity::getId, AppTenantPatientEntity::getPatientNumber,
                        AppTenantPatientEntity::getNickname, AppTenantPatientEntity::getUserId));
    }

    public AppTenantPatientEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppTenantPatientEntity>lambdaQuery()
                .eq(AppTenantPatientEntity::getUserId, userId));
    }
}
