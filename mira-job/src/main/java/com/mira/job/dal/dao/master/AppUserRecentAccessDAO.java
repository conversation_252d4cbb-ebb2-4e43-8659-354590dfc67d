package com.mira.job.dal.dao.master;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.job.dal.DataSourceName;
import com.mira.job.dal.entity.master.AppUserRecentAccessEntity;
import com.mira.job.dal.mapper.master.AppUserRecentAccessMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_user_recent_access DAO
 *
 * <AUTHOR>
 */
@DS(DataSourceName.SLAVE)
@Repository
public class AppUserRecentAccessDAO extends ServiceImpl<AppUserRecentAccessMapper, AppUserRecentAccessEntity> {
    public AppUserRecentAccessEntity getByUserId(Long userId) {
        return lambdaQuery().eq(AppUserRecentAccessEntity::getUserId, userId).one();
    }

    public List<AppUserRecentAccessEntity> listByUserIds(List<Long> userIds) {
        return list(Wrappers.<AppUserRecentAccessEntity>lambdaQuery()
                            .in(AppUserRecentAccessEntity::getUserId, userIds));
    }
}
