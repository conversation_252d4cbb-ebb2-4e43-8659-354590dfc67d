package com.mira.job.dal.dao.master;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.job.dal.entity.master.WandsParamRecordEntity;
import com.mira.job.dal.mapper.master.WandsParamRecordMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * wands_param_record DAO
 *
 * <AUTHOR>
 */
@Repository
public class WandsParamRecordDAO extends ServiceImpl<WandsParamRecordMapper, WandsParamRecordEntity> {
    public Long getMaxId() {
        String sql = "select max(max_id) from wands_param_record";
        return baseMapper.getMaxId(sql);
    }

    public WandsParamRecordEntity getByUBatchAndType(String uBatch, String uStripType) {
        return lambdaQuery().eq(WandsParamRecordEntity::getUBatch, uBatch)
                .eq(WandsParamRecordEntity::getUStripType, uStripType)
                .one();
    }

    public void update(WandsParamRecordEntity wandsParamRecord) {
        update(wandsParamRecord, Wrappers.<WandsParamRecordEntity>lambdaQuery()
                .eq(WandsParamRecordEntity::getUBatch, wandsParamRecord.getUBatch())
                .eq(WandsParamRecordEntity::getUStripType, wandsParamRecord.getUStripType()));
    }

    public List<WandsParamRecordEntity> listAll() {
        return list(Wrappers.<WandsParamRecordEntity>lambdaQuery()
                            .orderByDesc(WandsParamRecordEntity::getUStampManufacture));
    }
}
