package com.mira.job.dal.entity.factory;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("wands_param")
public class WandsParamEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("testWandFormatVersion")
    private String testWandFormatVersion;

    @TableField("uStripType")
    private String uStripType;

    @TableField("uBatch")
    private String uBatch;

    @TableField("palce")
    private String palce;

    @TableField("uNumInBatch")
    private String uNumInBatch;

    @TableField("uStampManufacture")
    private String uStampManufacture;

    @TableField("uShelfLifeDay")
    private String uShelfLifeDay;

    @TableField("RD_Leader")
    private String rdLeader;

    @TableField("Production_Leader")
    private String productionLeader;

    @TableField("QC_Leader")
    private String qcLeader;

    @TableField("uFirstInuseStamp")
    private String uFirstInuseStamp;

    @TableField("uScanCompleteStamp")
    private String uScanCompleteStamp;

    @TableField("uIsFin")
    private String uIsFin;

    @TableField("uInCount")
    private String uInCount;

    @TableField("uLinNum")
    private String uLinNum;

    @TableField("uIncubateTime")
    private String uIncubateTime;

    @TableField("uThresholdBlank")
    private String uThresholdBlank;

    @TableField("uThresholdTimeError")
    private String uThresholdTimeError;

    @TableField("eLineType1")
    private String eLineType1;

    @TableField("uPeakPosition1")
    private String uPeakPosition1;

    @TableField("uHalfPeakWidth1")
    private double uHalfPeakWidth1;

    @TableField("uHalfBaseWidth1")
    private String uHalfBaseWidth1;

    @TableField("fLowerLimit1")
    private String fLowerLimit1;

    @TableField("fUpperLimit1")
    private String fUpperLimit1;

    @TableField("eLineType2")
    private String eLineType2;

    @TableField("uPeakPosition2")
    private String uPeakPosition2;

    @TableField("uHalfPeakWidth2")
    private String uHalfPeakWidth2;

    @TableField("uHalfBaseWidth2")
    private String uHalfBaseWidth2;

    @TableField("fLowerLimit2")
    private String fLowerLimit2;

    @TableField("fUpperLimit2")
    private String fUpperLimit2;

    @TableField("eLineType3")
    private String eLineType3;

    @TableField("uPeakPosition3")
    private String uPeakPosition3;

    @TableField("uHalfPeakWidth3")
    private double uHalfPeakWidth3;

    @TableField("uHalfBaseWidth3")
    private double uHalfBaseWidth3;

    @TableField("fLowerLimit3")
    private String fLowerLimit3;

    @TableField("fUpperLimit3")
    private String fUpperLimit3;

    @TableField("eLineType4")
    private String eLineType4;

    @TableField("uPeakPosition4")
    private String uPeakPosition4;

    @TableField("uHalfPeakWidth4")
    private double uHalfPeakWidth4;

    @TableField("uHalfBaseWidth4")
    private double uHalfBaseWidth4;

    @TableField("fLowerLimit4")
    private String fLowerLimit4;

    @TableField("fUpperLimit4")
    private String fUpperLimit4;

    @TableField("CAL_PARA_Version")
    private String calParaVersion;

    @TableField("parType1")
    private String parType1;

    @TableField("funcType1")
    private String funcType1;

    @TableField("para1")
    private String para1;

    @TableField("parb1")
    private String parb1;

    @TableField("parc1")
    private String parc1;

    @TableField("pard1")
    private String pard1;

    @TableField("pare1")
    private String pare1;

    @TableField("unitType1")
    private String unitType1;

    @TableField("parType2")
    private String parType2;

    @TableField("funcType2")
    private String funcType2;

    @TableField("para2")
    private String para2;

    @TableField("parb2")
    private String parb2;

    @TableField("parc2")
    private String parc2;

    @TableField("pard2")
    private String pard2;

    @TableField("pare2")
    private String pare2;

    @TableField("unitType2")
    private String unitType2;

    @TableField("parType3")
    private String parType3;

    @TableField("funcType3")
    private String funcType3;

    @TableField("para3")
    private String para3;

    @TableField("parb3")
    private String parb3;

    @TableField("parc3")
    private String parc3;

    @TableField("pard3")
    private String pard3;

    @TableField("pare3")
    private String pare3;

    @TableField("unitType3")
    private String unitType3;

    @TableField("romType")
    private String romType;

    @TableField("adjustVer")
    private String adjustVer;

    @TableField("more")
    private String more;

    /**
     * t1c斜率
     */
    @TableField("slope_t1c")
    private Float slopeT1c;

    /**
     * t2c斜率
     */
    @TableField("slope_t2c")
    private Float slopeT2c;

    /**
     * 平均基线高
     */
    @TableField("avg_base_line")
    private Float avgBaseLine;
}
