package com.mira.job.dal.entity.master;


import com.baomidou.mybatisplus.annotation.*;
import com.mira.api.user.enums.daily.*;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 用户日记记录表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_diary")
public class AppUserDiaryEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 日记时间戳,年月日（不带时分秒）
     */
    private Long diaryDay;

    /**
     * 日记时间（年月日）
     */
    private String diaryDayStr;

    /**
     * 日记
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String notes;

    /**
     * 温度
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private BigDecimal tempC;

    /**
     * 温度
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private BigDecimal tempF;

    /**
     * 温度测试时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String tempTime;

    /**
     * 体重
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private BigDecimal weightK;

    /**
     * 体重
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private BigDecimal weightL;

    /**
     * 心情
     *
     * @see DailyStatusMoodEnum
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String mood;

    /**
     * 是否同房
     *
     * @see DailyStatusSexEnum
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String sex;

    /**
     * Cervical Mucus -- 白带状态(粘液状态)
     *
     * @see DailyStatusMucusTypeEnum
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String mucusType;

    /**
     * Cervical Mucus -- 白带流量
     *
     * @see DailyStatusMucusFlowEnum
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String mucusFlow;

    /**
     * 验孕记录
     *
     * @see DailyStatusPregnantEnum
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Boolean pregnant;

    /**
     * 排卵测试记录
     *
     * @see DailyStatusOPKEnum
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Boolean opk;

    /**
     * 出血状态
     *
     * @see DailyStatusFlowAndSpottingEnum
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String flowAndSpotting;

    /**
     * 子宫位置
     * <p>
     * High、Low、Medium
     *
     * @see DailyCervicalPosition
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String cervicalPosition;

    /**
     * Firmness
     * <p>
     * Firm、Soft、Medium
     *
     * @see DailyCervicalTexture
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String cervicalFirmness;

    /**
     * Openness
     * <p>
     * Closed、Open、Medium
     *
     * @see DailyCervicalOpenness
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String cervicalOpenness;

    /**
     * 0:未测试，1:已测试
     *
     * @deprecated
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer hormone;

    /**
     * 症状 (数组)
     *
     * @deprecated
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String symptoms;

    /**
     * 斑点
     *
     * @see DailyStatusSpottingEnum
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String spotting;

    /**
     * 经期，Other
     *
     * @see DailyStatusPeriodOtherEnum
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String periodOther;

    /**
     * 经期，Other
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String glucoseControl;
}
