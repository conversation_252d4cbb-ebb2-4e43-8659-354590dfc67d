package com.mira.job.dal.entity.master;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * beta测试的goal标识表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("user_product_trial")
public class UserProductTrialEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * beta测试的goal标识
     */
    private Integer flag;
}
