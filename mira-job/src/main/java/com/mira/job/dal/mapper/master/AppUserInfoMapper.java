package com.mira.job.dal.mapper.master;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.job.consts.dto.desk.PushUserInfoDTO;
import com.mira.job.dal.DataSourceName;
import com.mira.job.dal.entity.master.AppUserInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * app_user_info
 *
 * <AUTHOR>
 */
@Mapper
public interface AppUserInfoMapper extends BaseMapper<AppUserInfoEntity> {
    @DS(DataSourceName.SLAVE)
    @Select("${sql}")
    List<AppUserInfoEntity> listBySql(@Param("sql") String sql);

    @DS(DataSourceName.SLAVE)
    ArrayList<PushUserInfoDTO> queryUserInfo(@Param("pageSize") long pageSize,
                                             @Param("queryBatch") long queryBatch);

    @DS(DataSourceName.SLAVE)
    @Select("SELECT " +
            "  SUM(CASE WHEN goal_status = 0 THEN 1 ELSE 0 END) AS cc, " +
            "  SUM(CASE WHEN goal_status = 1 THEN 1 ELSE 0 END) AS tta, " +
            "  SUM(CASE WHEN goal_status = 2 THEN 1 ELSE 0 END) AS ttc, " +
            "  SUM(CASE WHEN goal_status = 3 THEN 1 ELSE 0 END) AS pregnancy, " +
            "  SUM(CASE WHEN goal_status = 4 THEN 1 ELSE 0 END) AS oft, " +
            "  SUM(CASE WHEN tracking_menopause = 1 THEN 1 ELSE 0 END) AS menopause " +
            "FROM app_user_info " +
            "WHERE deleted = 0")
    Map<String, BigDecimal> getModeSumMap();
}
