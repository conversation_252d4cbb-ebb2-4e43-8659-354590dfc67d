package com.mira.job.dal.mapper.master;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.job.dal.entity.master.SysRateAppLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * sys_rate_app_log
 *
 * <AUTHOR>
 */
@Mapper
public interface SysRateAppLogMapper extends BaseMapper<SysRateAppLogEntity> {
    @Select("${sql}")
    List<SysRateAppLogEntity> listBySql(@Param("sql") String sql);
}
