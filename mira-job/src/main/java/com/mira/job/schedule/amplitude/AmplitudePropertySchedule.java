package com.mira.job.schedule.amplitude;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.thirdparty.dto.amplitude.AmplitudeDTO;
import com.mira.api.thirdparty.enums.AmplitudeEventTypeEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.schedule.AbstractSchedule;
import com.mira.job.service.manager.PushUserInfoCacheManager;
import com.mira.job.service.util.AmplitudeHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * amplitude push property
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AmplitudePropertySchedule extends AbstractSchedule {
    @Resource
    private AmplitudeHelper amplitudeHelper;
    @Resource
    private PushUserInfoCacheManager pushUserInfoCacheManager;

    private final static List<Long> SKIP_TEST_COUNT = Arrays.asList(5L, 10L, 15L, 20L);

    @XxlJob("amplitudePropertyHandler")
    public void amplitudePropertyHandler() {
        log.info("amplitudePropertyHandler: start execute");
        run();
        log.info("amplitudePropertyHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        List<String> cacheIndex = pushUserInfoCacheManager.getCacheIndex();
        for (String index : cacheIndex) {
            ArrayList<PushUserInfoDTO> pushUserInfoCache = pushUserInfoCacheManager.getPushUserInfoCache(index);
            if (CollectionUtils.isNotEmpty(pushUserInfoCache)) {
                handle(pushUserInfoCache);
            }
        }
    }

    private void handle(List<PushUserInfoDTO> userInfoList) {
        Map<Long, String> testRatioMap = new HashMap<>();
        Map<Long, String> skipTestMap = new HashMap<>();
        for (PushUserInfoDTO userInfo : userInfoList) {
            Long userId = userInfo.getUserId();
            try {
                String today = ZoneDateUtil.format(userInfo.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
                // cycle
                List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);
                CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
                // hormone
                List<HormoneDTO> hormoneDTOS = JsonUtil.toArray(userInfo.getHormoneData(), HormoneDTO.class);
                List<HormoneDTO> currentCycleHormones = CycleDataUtil.hormoneByCurrentCycle(currentCycleData, hormoneDTOS);
                // ratio
                long testRatio = CycleDataUtil.testRatioByCurrentCycle(currentCycleData, currentCycleHormones);
                if (testRatio < 50) {
                    testRatioMap.put(userId, "<50%");
                } else if (testRatio > 70) {
                    testRatioMap.put(userId, ">70%");
                } else {
                    testRatioMap.put(userId, "50-70%");
                }

                long skipTestCount = CycleDataUtil.skipTestCountByCurrentCycle(currentCycleData, currentCycleHormones, today);
                if (SKIP_TEST_COUNT.contains(skipTestCount)) {
                    skipTestMap.put(userId, String.valueOf(skipTestCount));
                }

            } catch (Exception e) {
                log.error("[Amplitude Job] user:{} handler error.", userId, e);
            }
        }
        createEvent(testRatioMap, skipTestMap);
    }

    private void createEvent(Map<Long, String> testRatioMap, Map<Long, String> skipTestMap) {
        int pushEventsSize = 0;
        for (Map.Entry<Long, String> entry : testRatioMap.entrySet()) {
            Long userId = entry.getKey();
            if (checkPfizerAccount(userId)) {
                continue;
            }

            AmplitudeDTO amplitudeDTO = new AmplitudeDTO();
            amplitudeDTO.setUserId(userId);
            amplitudeDTO.setAmplitudeEventTypeEnum(AmplitudeEventTypeEnum.UPDATE_USER_PROPERTIES);

            HashMap<String, String> userPropsMap = new HashMap<>();
            userPropsMap.put("Testing compliance", entry.getValue());
            String skipTestCount = skipTestMap.get(userId);
            if (StringUtils.isNotEmpty(skipTestCount)) {
                userPropsMap.put("Skipped tests", skipTestCount);
            }
            amplitudeDTO.setUserProps(userPropsMap);

            amplitudeHelper.logEvent(amplitudeDTO);
            pushEventsSize++;
        }
        log.info("events[{}], create size:{}", AmplitudeEventTypeEnum.UPDATE_USER_PROPERTIES.getType(), pushEventsSize);
    }
}
