package com.mira.job.schedule.testing.push;

import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.JobNotificationDTO;
import com.mira.job.consts.dto.desk.PushUserInfoDTO;
import com.mira.job.consts.enums.ReminderEnum;
import com.mira.job.dal.dao.master.SysNotificationTestingHistoryDAO;
import com.mira.job.dal.dao.master.SysNotificationTestingStatisticsDAO;
import com.mira.job.dal.entity.master.AppUserInfoEntity;
import com.mira.job.dal.entity.master.SysNotificationDefineEntity;
import com.mira.job.dal.entity.master.SysNotificationTestingHistoryEntity;
import com.mira.job.dal.entity.master.SysNotificationTestingStatisticsEntity;
import com.mira.job.service.manager.CommonManager;
import com.mira.job.service.manager.JobManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.redis.cache.RedisComponent;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Test env：0 5,15,25,35,45,55 * * * ?
 * Product env：0 6,16,26,36,46,56 * * * ?
 * <p>
 * 推送测试日
 * <p>
 * 发送firebase，并记录sys_notification_record和sys_notification_testing_history
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2021-08-04
 **/
@Slf4j
@Component
public class TestingDayPushSchedule extends AbstractPushTesting {
    @Resource
    private SysNotificationTestingHistoryDAO sysNotificationTestingHistoryDAO;
    @Resource
    private SysNotificationTestingStatisticsDAO sysNotificationTestingStatisticsDAO;
    @Resource
    private JobManager jobManager;
    @Resource
    private CommonManager commonManager;
    @Resource
    private RedisComponent redisComponent;

    private static final Set<String> MAX_WAND_TYPES = Set.of("3", "9", "12");
    private static final Set<String> MAX2_WAND_TYPES = Set.of("3", "9", "16", "18");
    private static final String OVUM_WAND_TYPE = "16";

    private static final long TEN_MINUTES_MILLIS = 10L * 60 * 1000;

    @XxlJob("testingDayPushHandler")
    public void testingDayPushHandler() {
        log.info("testingDayPushHandler: start execute");
        run();
        log.info("testingDayPushHandler: end execute");
        log.info("------------------------------------");
    }

    public void run() {
        long pushCurrentTime = System.currentTimeMillis();
        // push时间区间：10分钟前 ~ 10分钟后
        Long pushBeforeTime = pushCurrentTime - TEN_MINUTES_MILLIS;
        Long pushAfterTime = pushCurrentTime + TEN_MINUTES_MILLIS;

        // 移除在push开始时间之前的first_reminder_time
        removeBeforePushStartTime(pushBeforeTime);

        // 构建需要推送的通知列表并推送（同时记录）
        List<JobNotificationDTO> jobNotificationDTOS = buildJobNotificationDTOS(pushBeforeTime, pushAfterTime);
        push(jobNotificationDTOS, jobManager.getAllUserInfo());
    }

    /**
     * 移除在pushStartTime之前的first_reminder_time
     */
    private void removeBeforePushStartTime(Long pushStartTime) {
        List<SysNotificationTestingStatisticsEntity> removeTestingDayList = sysNotificationTestingStatisticsDAO.listBeforePushStartTime(pushStartTime);
        log.info("remove testingDayList size:{}", removeTestingDayList.size());

        for (SysNotificationTestingStatisticsEntity statisticsEntity : removeTestingDayList) {
            // firstReminderTime和对应的reminderTimes
            processReminder(statisticsEntity, statisticsEntity.getE3gFirstReminderTime(),
                    statisticsEntity.getE3gReminderTimes(), WandTypeEnum.E3G_LH, pushStartTime);
            processReminder(statisticsEntity, statisticsEntity.getHcgFirstReminderTime(),
                    statisticsEntity.getHcgReminderTimes(), WandTypeEnum.HCG, pushStartTime);
            processReminder(statisticsEntity, statisticsEntity.getPdgFirstReminderTime(),
                    statisticsEntity.getPdgReminderTimes(), WandTypeEnum.PDG, pushStartTime);
            processReminder(statisticsEntity, statisticsEntity.getProduct12FirstReminderTime(),
                    statisticsEntity.getProduct12ReminderTimes(), WandTypeEnum.LH_E3G_PDG, pushStartTime);
            processReminder(statisticsEntity, statisticsEntity.getProduct14FirstReminderTime(),
                    statisticsEntity.getProduct14ReminderTimes(), WandTypeEnum.HCG_QUALITATIVE, pushStartTime);
            processReminder(statisticsEntity, statisticsEntity.getProduct16FirstReminderTime(),
                    statisticsEntity.getProduct16ReminderTimes(), WandTypeEnum.FSH, pushStartTime);
            processReminder(statisticsEntity, statisticsEntity.getProduct18FirstReminderTime(),
                    statisticsEntity.getProduct18ReminderTimes(), WandTypeEnum.MAX2, pushStartTime);

            // 更新时间信息
            UpdateEntityTimeUtil.updateBaseEntityTime(statisticsEntity.getTimeZone(), statisticsEntity);
        }
        sysNotificationTestingStatisticsDAO.updateBatchById(removeTestingDayList);
    }

    /**
     * 若reminderTime不为空且小于pushStartTime，则将其移除
     */
    private void processReminder(SysNotificationTestingStatisticsEntity statisticsEntity,
                                 Long reminderTime,
                                 String reminderTimes,
                                 WandTypeEnum wandType,
                                 Long pushStartTime) {
        if (reminderTime != null && reminderTime < pushStartTime) {
            removeFirstReminderTime(statisticsEntity, reminderTimes, wandType);
        }
    }

    /**
     * 在pushStartTime~pushEndTime时间区间内尚未推送过的用户，需要发推送，并进行记录
     */
    private List<JobNotificationDTO> buildJobNotificationDTOS(Long pushBeforeTime, Long pushAfterTime) {
        List<SysNotificationTestingStatisticsEntity> testingDayList = sysNotificationTestingStatisticsDAO.listBetweenPushTime(pushBeforeTime, pushAfterTime);
        log.info("push testingDayList size:{}", testingDayList.size());
        return testingDayList.stream()
                .map(statistics -> buildNotification(statistics, pushBeforeTime, pushAfterTime))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private JobNotificationDTO buildNotification(SysNotificationTestingStatisticsEntity statistics,
                                                 Long pushBeforeTime, Long pushAfterTime) {
        Long userId = statistics.getUserId();
        String timeZone = statistics.getTimeZone();

        /*
         Testing day: Mira Max Wand，03/09/12 测了其中一个及以上，不推送
         Testing day: Mira Max Wand Mira 和 Ovum Wand，03/09/12/16 测了其中一个及以上，不推送
         Testing day: Mira Max2.0 Wand，03/09/16/18 测了其中一个及以上，不推送
         */
        // 当日已测试试剂集合
        String currentDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        String key = RedisCacheKeyConst.USER_NEW_HORMONE_WANDTYPE_DAY + currentDate + ":" + userId;
        Set<String> alreadyTestWandTypeSet = redisComponent.get(key, Set.class);

        // 判断是否已测试过对应试剂类型
        boolean hasTestedMax = CollectionUtils.isNotEmpty(alreadyTestWandTypeSet) &&
                alreadyTestWandTypeSet.stream().anyMatch(MAX_WAND_TYPES::contains);
        boolean hasTestedOvum = CollectionUtils.isNotEmpty(alreadyTestWandTypeSet) &&
                alreadyTestWandTypeSet.contains(OVUM_WAND_TYPE);
        boolean hasTestedMax2 = CollectionUtils.isNotEmpty(alreadyTestWandTypeSet) &&
                alreadyTestWandTypeSet.stream().anyMatch(MAX2_WAND_TYPES::contains);

        // 构建不同通知类型
        JobNotificationDTO maxNotification = !hasTestedMax ? buildMaxNotification(statistics, pushBeforeTime, pushAfterTime) : null;
        JobNotificationDTO ovumNotification = !hasTestedOvum ? buildOvumNotification(statistics, pushBeforeTime, pushAfterTime) : null;
        JobNotificationDTO max2Notification = !hasTestedMax2 ? buildMax2Notification(statistics, pushBeforeTime, pushAfterTime) : null;

        // 是否推送Max2.0
        max2Notification =  determineMax2Notification(max2Notification, userId, hasTestedMax2);
        if (Objects.nonNull(max2Notification)) {
            return max2Notification;
        }

        // 根据已有数据决定返回哪种通知类型
        return determineNotification(maxNotification, ovumNotification, userId, hasTestedMax, hasTestedOvum);
    }

    private JobNotificationDTO buildMaxNotification(SysNotificationTestingStatisticsEntity statistics,
                                                    Long pushBeforeTime, Long pushAfterTime) {
        Long reminderTime = Stream.of(
                        statistics.getProduct12FirstReminderTime(),
                        statistics.getE3gFirstReminderTime(),
                        statistics.getPdgFirstReminderTime())
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
        return getPushNotificationDTO(pushBeforeTime, pushAfterTime, reminderTime,
                NotificationDefineEnum.TESTING_DAY_REMINDER_MAX.getDefineId(),
                statistics.getUserId(), statistics.getTimeZone());
    }

    private JobNotificationDTO buildOvumNotification(SysNotificationTestingStatisticsEntity statistics,
                                                     Long pushBeforeTime, Long pushAfterTime) {
        return getPushNotificationDTO(pushBeforeTime, pushAfterTime,
                statistics.getProduct16FirstReminderTime(),
                NotificationDefineEnum.TESTING_DAY_REMINDER_FSH.getDefineId(),
                statistics.getUserId(), statistics.getTimeZone());
    }

    private JobNotificationDTO buildMax2Notification(SysNotificationTestingStatisticsEntity statistics,
                                                     Long pushBeforeTime, Long pushAfterTime) {
        Long reminderTime = Stream.of(statistics.getProduct18FirstReminderTime())
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
        return getPushNotificationDTO(pushBeforeTime, pushAfterTime, reminderTime,
                NotificationDefineEnum.TESTING_DAY_REMINDER_MAX2.getDefineId(),
                statistics.getUserId(), statistics.getTimeZone());
    }

    private JobNotificationDTO determineNotification(JobNotificationDTO maxNotification,
                                                     JobNotificationDTO ovumNotification,
                                                     Long userId,
                                                     boolean hasTestedMax,
                                                     boolean hasTestedOvum) {
        // 同时存在Max和Ovum的情况
        if (Objects.nonNull(maxNotification) && Objects.nonNull(ovumNotification)) {
            if (hasTestedMax || hasTestedOvum) {
                return null;
            }
            if (sysNotificationTestingHistoryDAO.checkExist(userId,
                    NotificationDefineEnum.TESTING_DAY_REMINDER_MAX_OVUM.getDefineId(),
                    ovumNotification.getReminderTime())) {
                return null;
            }
            maxNotification.setDefineId(NotificationDefineEnum.TESTING_DAY_REMINDER_MAX_OVUM.getDefineId());
            return maxNotification;
        }
        // 单独只有Max
        if (Objects.nonNull(maxNotification)) {
            if (hasTestedMax || sysNotificationTestingHistoryDAO.checkExist(userId,
                    NotificationDefineEnum.TESTING_DAY_REMINDER_MAX.getDefineId(),
                    maxNotification.getReminderTime())) {
                return null;
            }
            return maxNotification;
        }
        // 单独只有Ovum
        if (Objects.nonNull(ovumNotification)) {
            if (hasTestedOvum || sysNotificationTestingHistoryDAO.checkExist(userId,
                    NotificationDefineEnum.TESTING_DAY_REMINDER_FSH.getDefineId(),
                    ovumNotification.getReminderTime())) {
                return null;
            }
            return ovumNotification;
        }
        return null;
    }

    private JobNotificationDTO determineMax2Notification(JobNotificationDTO max2Notification,
                                                         Long userId, boolean hasTestedMax2) {
        // 有Max2.0的情况
        if (Objects.nonNull(max2Notification)) {
            if (hasTestedMax2 && sysNotificationTestingHistoryDAO.checkExist(userId,
                    NotificationDefineEnum.TESTING_DAY_REMINDER_MAX2.getDefineId(),
                    max2Notification.getReminderTime())) {
                return null;
            }
        }
        return max2Notification;
    }

    private JobNotificationDTO getPushNotificationDTO(Long pushBeforeTime, Long pushAfterTime,
                                                      Long firstReminderTime, Long defineId,
                                                      Long userId, String timeZone) {
        return Optional.ofNullable(firstReminderTime)
                .filter(time -> time > pushBeforeTime && time < pushAfterTime)
                .map(time -> new JobNotificationDTO(userId, timeZone, defineId, time))
                .orElse(null);
    }

    private void push(List<JobNotificationDTO> jobNotificationDTOS, Map<Long, AppUserInfoEntity> allUserInfoMap) {
        List<SysNotificationTestingHistoryEntity> testingHistoryEntityList = new ArrayList<>();
        List<SysNotificationTestingStatisticsEntity> testingStatisticsEntityList = new ArrayList<>();

        Map<FirebasePushDTO, List<Long>> firebasePushMap = new HashMap<>();
        Map<FirebasePushDTO, List<Long>> saveRecordNoFirebasePushMap = new HashMap<>();

        for (JobNotificationDTO jobNotificationDTO : jobNotificationDTOS) {
            // app user info
            Long userId = jobNotificationDTO.getUserId();
            AppUserInfoEntity appUserInfo = allUserInfoMap.get(userId);
            if (appUserInfo == null) {
                continue;
            }
            // notification define
            SysNotificationDefineEntity notificationDefine = commonManager.getNotificationDefine(jobNotificationDTO.getDefineId());
            if (notificationDefine == null) {
                continue;
            }
            // user reminder info
            UserReminderInfoDTO userReminderInfo = jobManager.getUserReminderInfo(userId);
            if (userReminderInfo == null) {
                continue;
            }
            // firebase push check
            boolean schedulePushFlag = 1 == userReminderInfo.getTestingScheduleFlag();
            if (!schedulePushFlag) {
                continue;
            }
            // firbase push dto
            FirebasePushDTO firebasePushDTO = FirebaseBuildUtil
                    .buildPushNotification(notificationDefine, jobManager.userRemindOpen(userId, ReminderEnum.HIDE_CONTENT_SWITCH));
            // build push map
            boolean firebasePushFlag = 1 == userReminderInfo.getRemindFlag();
            if (firebasePushFlag) {
                firebasePushMap.computeIfAbsent(firebasePushDTO, k -> new ArrayList<>()).add(userId);
            } else {
                saveRecordNoFirebasePushMap.computeIfAbsent(firebasePushDTO, k -> new ArrayList<>()).add(userId);
            }
            // sys_notification_testing_history
            Integer pushType = commonManager.getPushType(appUserInfo.getPlatform());
            testingHistoryEntityList.add(buildTestingHistory(jobNotificationDTO, pushType));
            // 从统计记录中移除对应的first_reminder_time
            SysNotificationTestingStatisticsEntity statisticsEntity = sysNotificationTestingStatisticsDAO.getByUserId(jobNotificationDTO.getUserId());
            testingStatisticsEntityList.add(updateTestingStatistics(jobNotificationDTO, statisticsEntity));
        }

        // save testing push history
        if (CollectionUtils.isNotEmpty(testingHistoryEntityList)) {
            sysNotificationTestingHistoryDAO.saveBatch(testingHistoryEntityList);
        }
        // update testing statistics
        if (CollectionUtils.isNotEmpty(testingStatisticsEntityList)) {
            sysNotificationTestingStatisticsDAO.updateBatchById(testingStatisticsEntityList);
        }

        // push
        log.info("-----------------------------------------");
        long[] pushCount = {0L};
        long[] noFirebasePushCount = {0L};
        firebasePushMap.values().forEach(list -> pushCount[0] += list.size());
        saveRecordNoFirebasePushMap.values().forEach(list -> noFirebasePushCount[0] += list.size());
        log.info("TestingDayPushSchedule -> Start firebase push, type size:{}, push count:{}", firebasePushMap.size(), pushCount[0]);
        log.info("TestingDayPushSchedule -> Start firebase push, type size:{}, noFirebasePush count:{}", saveRecordNoFirebasePushMap.size(), pushCount[0]);

        Map<Long, PushUserInfoDTO> pushUserInfoMap = buildPushUserInfoDTO(allUserInfoMap);
        commonManager.firebasePush(firebasePushMap, pushUserInfoMap, null);
        commonManager.saveRecordNoFirebasePush(saveRecordNoFirebasePushMap, pushUserInfoMap, null);
    }

    private Map<Long, PushUserInfoDTO> buildPushUserInfoDTO(Map<Long, AppUserInfoEntity> allUserInfoMap) {
        Map<Long, PushUserInfoDTO> pushUserInfoDTOMap = new HashMap<>();
        for (Map.Entry<Long, AppUserInfoEntity> entry : allUserInfoMap.entrySet()) {
            Long userId = entry.getKey();
            AppUserInfoEntity value = entry.getValue();

            PushUserInfoDTO pushUserInfoDTO = new PushUserInfoDTO();
            pushUserInfoDTO.setUserId(userId);
            pushUserInfoDTO.setPushToken(value.getPushToken());
            pushUserInfoDTO.setPlatform(value.getPlatform());
            pushUserInfoDTO.setTimeZone(value.getTimeZone());
            pushUserInfoDTOMap.put(userId, pushUserInfoDTO);
        }
        return pushUserInfoDTOMap;
    }
}
