package com.mira.job.service.handler.desk.notification;

import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.job.consts.dto.PushUserInfoDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * notification job handler
 *
 * <AUTHOR>
 */
public class NotificationJobHandler {
    private final static List<INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>>> HANDLERS = new ArrayList<>();

    public static void add(INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> notificationJobHandler) {
        HANDLERS.add(notificationJobHandler);
    }

    public static void handle(NotificationPushCreateDTO dto, List<PushUserInfoDTO> allUserInfo) {
        for (INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> jobHandler : HANDLERS) {
            jobHandler.handle(dto, allUserInfo);
        }
    }
}
