package com.mira.job.service.handler.desk.notification.condition;

import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.job.consts.dto.desk.PushUserInfoDTO;
import com.mira.job.service.handler.desk.notification.INotificationJobHandler;
import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * Analyzer
 *
 * <AUTHOR>
 */
@Component
public class Analy<PERSON><PERSON>andler implements INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> {
    @PostConstruct
    public void init() {
        NotificationJobHandler.add(this);
    }

    @Override
    public void handle(NotificationPushCreateDTO dto, List<PushUserInfoDTO> allUserInfo) {
        Integer bindAnalyzer = dto.getBindAnalyzer();
        if (bindAnalyzer == null || bindAnalyzer == -1) {
            return;
        }

        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>();

        // 未绑定
        if (bindAnalyzer == 0) {
            for (PushUserInfoDTO userInfo : allUserInfo) {
                if (StringUtils.isNotBlank(userInfo.getBindDevice())) {
                    waitDeleteList.add(userInfo);
                }
            }
        }
        // 绑定
        if (bindAnalyzer == 1) {
            for (PushUserInfoDTO userInfo : allUserInfo) {
                if (StringUtils.isBlank(userInfo.getBindDevice())) {
                    waitDeleteList.add(userInfo);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(waitDeleteList)) {
            allUserInfo.removeAll(waitDeleteList);
        }
    }
}
