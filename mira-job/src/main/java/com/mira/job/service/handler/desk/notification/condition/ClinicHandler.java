package com.mira.job.service.handler.desk.notification.condition;

import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.service.handler.desk.notification.INotificationJobHandler;
import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Clinic
 * <br/>
 * clinic id list
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ClinicHandler implements INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> {
    @PostConstruct
    public void init() {
        NotificationJobHandler.add(this);
    }

    @Override
    public void handle(NotificationPushCreateDTO dto, List<PushUserInfoDTO> allUserInfo) {
        List<NotificationPushCreateDTO.ClinicDTO> clinics = dto.getClinics();
        if (CollectionUtils.isEmpty(clinics)) {
            return;
        }
        Long[] clinicIds = clinics.stream().map(NotificationPushCreateDTO.ClinicDTO::getId).toArray(Long[]::new);
        List<String> clinicIdList = Stream.of(clinicIds).map(String::valueOf).collect(Collectors.toList());

        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>();

        for (PushUserInfoDTO userInfo : allUserInfo) {
            String userBindClinicIds = userInfo.getClinicIds();
            if (StringUtils.isBlank(userBindClinicIds)) {
                waitDeleteList.add(userInfo);
            } else {
                List<String> userBindClinicIdsList = Stream.of(userBindClinicIds.split(","))
                        .collect(Collectors.toList());
                boolean noneMatch = userBindClinicIdsList.stream().noneMatch(clinicIdList::contains);
                if (noneMatch) {
                    waitDeleteList.add(userInfo);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(waitDeleteList)) {
            allUserInfo.removeAll(waitDeleteList);
        }
    }
}
