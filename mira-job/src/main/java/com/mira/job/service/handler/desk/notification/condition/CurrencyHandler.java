package com.mira.job.service.handler.desk.notification.condition;

import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.core.consts.enums.CountryCodeEnum;
import com.mira.core.consts.enums.CurrencyEnum;
import com.mira.job.consts.dto.desk.PushUserInfoDTO;
import com.mira.job.service.handler.desk.notification.INotificationJobHandler;
import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Currency
 *
 * <AUTHOR>
 */
@Component
public class CurrencyHandler implements INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> {
    @PostConstruct
    public void init() {
        NotificationJobHandler.add(this);
    }

    @Override
    public void handle(NotificationPushCreateDTO dto, List<PushUserInfoDTO> allUserInfo) {
        String[] currencys = dto.getCurrency();
        if (currencys == null || currencys.length == 0) {
            return;
        }
        List<String> currencyList = Stream.of(currencys)
                .map(String::toLowerCase)
                .collect(Collectors.toList());

        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>();

        for (PushUserInfoDTO userInfo : allUserInfo) {
            String userCurrency = getUserCurrency(userInfo).toLowerCase();
            if (!currencyList.contains(userCurrency)) {
                waitDeleteList.add(userInfo);
            }
        }

        if (CollectionUtils.isNotEmpty(waitDeleteList)) {
            allUserInfo.removeAll(waitDeleteList);
        }
    }

    private String getUserCurrency(PushUserInfoDTO userInfoDTO) {
        String currentCurrency = userInfoDTO.getCurrentCurrency();
        if (StringUtils.isNotBlank(currentCurrency)) {
            return currentCurrency;
        }

        String countryCode = userInfoDTO.getCountryCode();
        String continentCode = userInfoDTO.getContinentCode();
        if (StringUtils.isNotBlank(countryCode) && StringUtils.isNotBlank(continentCode)) {
            CurrencyEnum currencyEnum = CountryCodeEnum.getCurrencyByCountry(countryCode, continentCode);
            return currencyEnum.getCode();
        }

        return CurrencyEnum.USD.getCode();
    }
}
