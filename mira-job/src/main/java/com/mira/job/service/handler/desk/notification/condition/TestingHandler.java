package com.mira.job.service.handler.desk.notification.condition;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.service.handler.desk.notification.INotificationJobHandler;
import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
import com.mira.api.bluetooth.util.CycleDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * Testing
 * <br/>
 * 试剂测试，1:As recommended (more than 70%)，2:Not as recommended (less than 70%)，
 * 3:Not testing (at least 10 recommended tests skipped)
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TestingHandler implements INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> {
    @PostConstruct
    public void init() {
        NotificationJobHandler.add(this);
    }

    private final static int AS_RECOMMENDED = 1;
    private final static int NOT_AS_RECOMMENDED = 2;
    private final static int NOT_TESTING = 3;

    @Override
    public void handle(NotificationPushCreateDTO dto, List<PushUserInfoDTO> allUserInfo) {
        Integer testing = dto.getTesting();
        if (testing == null || testing == -1) {
            return;
        }

        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>();

        for (PushUserInfoDTO userInfo : allUserInfo) {
            if (StringUtils.isBlank(userInfo.getCycleData())) {
                waitDeleteList.add(userInfo);
                continue;
            }
            String today = ZoneDateUtil.format(userInfo.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
            try {
                // cycle
                List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);
                CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
                // hormone
                List<HormoneDTO> hormoneDTOS = JsonUtil.toArray(userInfo.getHormoneData(), HormoneDTO.class);
                List<HormoneDTO> currentCycleHormones = CycleDataUtil.hormoneByCurrentCycle(currentCycleData, hormoneDTOS);

                long testRatio = CycleDataUtil.testRatioByCurrentCycle(currentCycleData, currentCycleHormones);
                if (AS_RECOMMENDED == testing && (testRatio == -1 || testRatio <= 70)) {
                    waitDeleteList.add(userInfo);
                    continue;
                }
                if (NOT_AS_RECOMMENDED == testing && (testRatio == -1 || testRatio >= 70)) {
                    waitDeleteList.add(userInfo);
                    continue;
                }
                long skipTestCount = CycleDataUtil.skipTestCountByCurrentCycle(currentCycleData, currentCycleHormones, today);
                if (NOT_TESTING == testing && (skipTestCount == -1 || skipTestCount < 10)) {
                    waitDeleteList.add(userInfo);
                }
            } catch (Exception e) {
                log.error("[Desk Job] user:{} testing handler error.", userInfo.getUserId(), e);
            }
        }

        if (CollectionUtils.isNotEmpty(waitDeleteList)) {
            allUserInfo.removeAll(waitDeleteList);
        }
    }
}
