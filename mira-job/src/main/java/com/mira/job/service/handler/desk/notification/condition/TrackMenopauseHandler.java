package com.mira.job.service.handler.desk.notification.condition;

import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.core.util.AgeUtil;
import com.mira.job.consts.dto.desk.PushUserInfoDTO;
import com.mira.job.service.handler.desk.notification.INotificationJobHandler;
import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * TrackMenopause
 * <br/>
 * 是否跟踪更年期，null无标识;0 不跟踪;1跟踪
 *
 * <AUTHOR>
 */
@Component
public class TrackMenopauseHandler implements INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> {
    @PostConstruct
    public void init() {
        NotificationJobHandler.add(this);
    }

    @Override
    public void handle(NotificationPushCreateDTO dto, List<PushUserInfoDTO> allUserInfo) {
        Integer trackingMenopauseCondition = dto.getTrackingMenopause();
        if (trackingMenopauseCondition == null || trackingMenopauseCondition == -1) {
            return;
        }

        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>();

        for (PushUserInfoDTO userInfo : allUserInfo) {
            Integer trackingMenopause = userInfo.getTrackingMenopause();
            if (trackingMenopause == null && trackingMenopauseCondition == 0) {
                continue;
            }
            if (!trackingMenopauseCondition.equals(trackingMenopause)) {
                waitDeleteList.add(userInfo);
            }
        }

        if (CollectionUtils.isNotEmpty(waitDeleteList)) {
            allUserInfo.removeAll(waitDeleteList);
        }
    }


}
