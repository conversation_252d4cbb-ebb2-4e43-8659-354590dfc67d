package com.mira.job.service.handler.desk.notification.condition;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.desk.PushUserInfoDTO;
import com.mira.job.service.handler.desk.notification.INotificationJobHandler;
import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
import com.mira.api.bluetooth.util.CycleDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * Wand errors
 * <br/>
 * 试剂错误，1:1 in a cycle, 2:2 in a cycle, 3:3 in a cycle, 4:4 in a cycle, 5:>4 in a cycle
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Slf4j
@Component
public class WandErrorHandler implements INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> {
    @PostConstruct
    public void init() {
        NotificationJobHandler.add(this);
    }

    private final static int ONE_IN_CYCLE = 1;
    private final static int TWO_IN_CYCLE = 2;
    private final static int THREE_IN_CYCLE = 3;
    private final static int FOUR_IN_CYCLE = 4;
    private final static int GT_4_CYCLE = 5;

    @Override
    public void handle(NotificationPushCreateDTO dto, List<PushUserInfoDTO> allUserInfo) {
        Integer wandErrorCondition = dto.getWandError();
        if (wandErrorCondition == null || wandErrorCondition == -1) {
            return;
        }

        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>();

        for (PushUserInfoDTO userInfo : allUserInfo) {
            if (StringUtils.isBlank(userInfo.getCycleData())) {
                waitDeleteList.add(userInfo);
                continue;
            }
            String today = ZoneDateUtil.format(userInfo.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
            try {
                // cycle
                List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);
                CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
                // hormone
                List<HormoneDTO> hormoneDTOS = JsonUtil.toArray(userInfo.getHormoneData(), HormoneDTO.class);
                List<HormoneDTO> currentCycleHormones = CycleDataUtil.hormoneByCurrentCycle(currentCycleData, hormoneDTOS);
                // wand error count
                long wandErrorCountByUser = currentCycleHormones.stream()
                        .filter(hormone -> hormone.getFlag() == 0)
                        .count();
                if (ONE_IN_CYCLE == wandErrorCondition
                        && ONE_IN_CYCLE != wandErrorCountByUser) {
                    waitDeleteList.add(userInfo);
                    continue;
                }
                if (TWO_IN_CYCLE == wandErrorCondition
                        && TWO_IN_CYCLE != wandErrorCountByUser) {
                    waitDeleteList.add(userInfo);
                    continue;
                }
                if (THREE_IN_CYCLE == wandErrorCondition
                        && THREE_IN_CYCLE != wandErrorCountByUser) {
                    waitDeleteList.add(userInfo);
                    continue;
                }
                if (FOUR_IN_CYCLE == wandErrorCondition
                        && FOUR_IN_CYCLE != wandErrorCountByUser) {
                    waitDeleteList.add(userInfo);
                    continue;
                }
                if (GT_4_CYCLE == wandErrorCondition
                        && wandErrorCountByUser <= FOUR_IN_CYCLE) {
                    waitDeleteList.add(userInfo);
                }
            } catch (Exception e) {
                log.error("[Desk Job] user:{} wand error handler error.", userInfo.getUserId(), e);
                continue;
            }
        }

        if (CollectionUtils.isNotEmpty(waitDeleteList)) {
            allUserInfo.removeAll(waitDeleteList);
        }
    }
}
