package com.mira.job.service.impl;

import com.mira.api.job.consts.JobStatusConsts;
import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.dal.dao.master.AdminNotificationJobDAO;
import com.mira.job.dal.dao.master.SysNotificationDefineDAO;
import com.mira.job.dal.entity.master.AdminNotificationJobEntity;
import com.mira.job.dal.entity.master.SysNotificationDefineEntity;
import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
import com.mira.job.service.manager.DeskPushManager;
import com.mira.job.service.manager.PushUserInfoCacheManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * promo job task
 *
 * <AUTHOR>
 */
@Slf4j
public class PromoJobTask implements Runnable {
    private final AdminNotificationJobDAO adminNotificationJobDAO;
    private final SysNotificationDefineDAO sysNotificationDefineDAO;
    private final DeskPushManager deskPushManager;
    private final PushUserInfoCacheManager pushUserInfoCacheManager;
    private final ConcurrentHashMap<String, ScheduledFuture<?>> scheduledTasks;
    private final String taskId;

    /**
     * default time zone
     */
    private final static String TIME_ZONE = "America/Los_Angeles";

    public PromoJobTask(AdminNotificationJobDAO adminNotificationJobDAO,
                        SysNotificationDefineDAO sysNotificationDefineDAO,
                        DeskPushManager deskPushManager,
                        PushUserInfoCacheManager pushUserInfoCacheManager,
                        ConcurrentHashMap<String, ScheduledFuture<?>> scheduledTasks,
                        String taskId) {
        this.adminNotificationJobDAO = adminNotificationJobDAO;
        this.sysNotificationDefineDAO = sysNotificationDefineDAO;
        this.deskPushManager = deskPushManager;
        this.pushUserInfoCacheManager = pushUserInfoCacheManager;
        this.scheduledTasks = scheduledTasks;
        this.taskId = taskId;
    }

    @Override
    public void run() {
        // job entity
        AdminNotificationJobEntity notificationJobEntity = adminNotificationJobDAO.getByTaskId(taskId);
        // all filter user count
        AtomicInteger atomicInteger = new AtomicInteger(0);

        List<String> cacheIndex = pushUserInfoCacheManager.getCacheIndex();
        for (String index : cacheIndex) {
            ArrayList<PushUserInfoDTO> pushUserInfoCache = pushUserInfoCacheManager.getPushUserInfoCache(index);
            if (CollectionUtils.isNotEmpty(pushUserInfoCache)) {
                handle(notificationJobEntity, pushUserInfoCache);
                atomicInteger.addAndGet(pushUserInfoCache.size());
            }
        }

        log.info("筛选用户的总数：{}", atomicInteger.get());
        // update db
        if (notificationJobEntity != null) {
            notificationJobEntity.setJobStatus(JobStatusConsts.FINISHED);
            UpdateEntityTimeUtil.updateBaseEntityTime(TIME_ZONE, notificationJobEntity);
            adminNotificationJobDAO.updateById(notificationJobEntity);
        }
        // update map
        scheduledTasks.remove(taskId);
    }

    private void handle(AdminNotificationJobEntity notificationJobEntity,
                        List<PushUserInfoDTO> allUserInfo) {
        try {
            // handle job
            NotificationPushCreateDTO pushCreateDTO = JsonUtil.toObject(notificationJobEntity.getJobJson(), NotificationPushCreateDTO.class);
            NotificationJobHandler.handle(pushCreateDTO, allUserInfo);
            log.info("filter user size:{}", allUserInfo.size());
            // push
            SysNotificationDefineEntity defineEntity = sysNotificationDefineDAO.getByDefineId(notificationJobEntity.getDefineId(), "en-us");
            Map<FirebasePushDTO, List<PushUserInfoDTO>> firebasePushMap = getFirebasePushMap(defineEntity, allUserInfo);
            String expireTime = pushCreateDTO.getExpireTime();
            Long expireTimestamp = null;
            if (StringUtils.isNotBlank(expireTime)) {
                expireTimestamp = ZoneDateUtil.timestamp(TIME_ZONE, pushCreateDTO.getExpireTime(), DatePatternConst.DATE_TIME_PATTERN);
            }
            Map<Long, Long> expireTimestampMap = new HashMap<>();
            expireTimestampMap.put(-99L, expireTimestamp);
            if (pushCreateDTO.getSilent() == 1) {
                deskPushManager.firebasePushSilent(firebasePushMap, expireTimestampMap);
            } else {
                deskPushManager.firebasePush(firebasePushMap, expireTimestampMap);
            }

        } catch (Exception e) {
            if (e instanceof InterruptedException) {
                log.info("taskId:{} interrupted", taskId);
            } else {
                log.error("taskId:{} occur error", taskId, e);
            }
        }
    }

    private Map<FirebasePushDTO, List<PushUserInfoDTO>> getFirebasePushMap(SysNotificationDefineEntity sysNotificationDefine,
                                                                           List<PushUserInfoDTO> filterResult) {
        FirebasePushDTO firebasePushDTO = FirebaseBuildUtil.buildPushNotification(sysNotificationDefine, false);
        Map<FirebasePushDTO, List<PushUserInfoDTO>> firebasePushMap = new HashMap<>();
        firebasePushMap.putIfAbsent(firebasePushDTO, filterResult);

        return firebasePushMap;
    }
}
