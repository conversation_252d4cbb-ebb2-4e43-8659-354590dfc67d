package com.mira.job.service.manager;

import com.google.common.collect.Lists;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.message.enums.PlatformEnum;
import com.mira.api.message.enums.PushTypeEnum;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.core.consts.enums.LocalEnum;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.dal.dao.master.AppUserInfoDAO;
import com.mira.job.dal.dao.master.SysNotificationDefineDAO;
import com.mira.job.dal.dao.master.SysNotificationRecordDAO;
import com.mira.job.dal.entity.master.AppUserInfoEntity;
import com.mira.job.dal.entity.master.SysNotificationDefineEntity;
import com.mira.job.dal.entity.master.SysNotificationRecordEntity;
import com.mira.job.properties.CacheExpireProperties;
import com.mira.job.service.util.FirebasePushUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.redis.cache.RedisComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Common Manager
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommonManager {
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private SysNotificationDefineDAO sysNotificationDefineDAO;
    @Resource
    private SysNotificationRecordDAO sysNotificationRecordDAO;

    @Resource
    private CacheExpireProperties cacheExpireProperties;
    @Resource
    private RedisComponent redisComponent;

    /**
     * 通知定义表
     */
    private volatile Map<Long, SysNotificationDefineEntity> notificationDefineMap;

    /**
     * 查询所有通知定义记录
     *
     * @param defineId 通知定义编号
     * @return k:define_id, v:entity
     */
    public SysNotificationDefineEntity getNotificationDefine(Long defineId) {
        if (Objects.isNull(notificationDefineMap) || notificationDefineMap.isEmpty()) {
            synchronized (CommonManager.class) {
                if (Objects.isNull(notificationDefineMap) || notificationDefineMap.isEmpty()) {
                    notificationDefineMap = sysNotificationDefineDAO.getMapByLanguage(LocalEnum.LOCAL_US.getValue());
                }
            }
        }
        return notificationDefineMap.get(defineId);
    }

    /**
     * 获取推送token
     *
     * @param userIdList 用户编号列表
     * @return key:user_id, value:token
     */
    public Map<Long, String> getPushToken(List<Long> userIdList) {
        Map<Long, String> pushTokenMap = new HashMap<>(userIdList.size());
        for (Long userId : userIdList) {
            PushTokenDTO pushTokenDTO = getPushToken(userId);
            if (Objects.nonNull(pushTokenDTO) && StringUtils.isNotBlank(pushTokenDTO.getPushToken())) {
                pushTokenMap.put(userId, pushTokenDTO.getPushToken());
            }
        }

        return pushTokenMap;
    }

    /**
     * 获取用户的 push token
     *
     * @param userId 用户id
     * @return PushTokenDTO
     */
    private PushTokenDTO getPushToken(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_FIREBASE_TOKEN + userId;
        PushTokenDTO cache = redisComponent.get(cacheKey, PushTokenDTO.class);
        if (ObjectUtils.isNotEmpty(cache) && StringUtils.isNotBlank(cache.getPushToken())) {
            return cache;
        }

        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(userId);
        PushTokenDTO pushTokenDTO = new PushTokenDTO();
        pushTokenDTO.setPushToken(appUserInfo.getPushToken());
        pushTokenDTO.setPlatform(appUserInfo.getPlatform());
        redisComponent.setEx(cacheKey, pushTokenDTO, cacheExpireProperties.getPushToken(), TimeUnit.DAYS);

        return pushTokenDTO;
    }

    /**
     * Firebase Push
     *
     * @param firebasePushMap key:推送内容，value:user_id list
     * @param allUserInfoMap  用户信息
     * @param expireTimeMap   key:user_id，value:过期时间（时间戳）
     */
    public void firebasePush(Map<FirebasePushDTO, List<Long>> firebasePushMap,
                             Map<Long, PushUserInfoDTO> allUserInfoMap,
                             Map<Long, Long> expireTimeMap) {
        // 按照通知类型分组推送
        for (Map.Entry<FirebasePushDTO, List<Long>> firebaseEntry : firebasePushMap.entrySet()) {
            CompletableFuture.runAsync(() -> {
                // 分组，每组500（Firebase 最大单次次数）
                List<String> pushTokenList = new ArrayList<>();
                for (Long userId : firebaseEntry.getValue()) {
                    PushUserInfoDTO appUserInfo = allUserInfoMap.get(userId);
                    if (Objects.nonNull(appUserInfo)) {
                        String pushToken = appUserInfo.getPushToken();
                        if (StringUtils.isNotEmpty(pushToken)) {
                            pushTokenList.add(pushToken);
                        }
                    }
                }
                List<List<String>> pushTokenPartition = Lists.partition(pushTokenList, 500);
                // 分组推送
                for (List<String> list : pushTokenPartition) {
                    FirebasePushUtil.sendMultiPush(firebaseEntry.getKey(), list);
                }
                // 保存记录
                batchSaveNotificationRecord(firebaseEntry.getValue(), allUserInfoMap, firebaseEntry.getKey(), expireTimeMap);

                log.info("Define ID:{} -> Number of users for this push:{}", firebaseEntry.getKey().getDefineId(), firebaseEntry.getValue().size());
                log.info("Define ID:{} -> Push finished", firebaseEntry.getKey().getDefineId());
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("Firebase push error:", ex);
                return null;
            });
        }
    }

    /**
     * Firebase Push
     *
     * @param firebasePushMap key:推送内容，value:user_id list
     * @param allUserInfoMap  用户信息
     * @param expireTimeMap   key:user_id，value:过期时间（时间戳）
     */
    public void saveRecordNoFirebasePush(Map<FirebasePushDTO, List<Long>> firebasePushMap,
                                         Map<Long, PushUserInfoDTO> allUserInfoMap,
                                         Map<Long, Long> expireTimeMap) {
        // 按照通知类型分组推送
        for (Map.Entry<FirebasePushDTO, List<Long>> firebaseEntry : firebasePushMap.entrySet()) {
            CompletableFuture.runAsync(() -> {
                // 保存记录
                batchSaveNotificationRecord(firebaseEntry.getValue(), allUserInfoMap, firebaseEntry.getKey(), expireTimeMap);
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("save notification record error:", ex);
                return null;
            });
        }
    }

    private SysNotificationRecordEntity buildNotificationRecord(PushUserInfoDTO appUserInfo, FirebasePushDTO firebasePushDTO, Long expireTime) {
        Integer platform = appUserInfo.getPlatform();
        Integer pushType = getPushType(platform);

        SysNotificationRecordEntity sysNotificationRecordEntity = new SysNotificationRecordEntity();
        sysNotificationRecordEntity.setUserId(appUserInfo.getUserId());
        sysNotificationRecordEntity.setNotificationDefineId(firebasePushDTO.getDefineId());
        sysNotificationRecordEntity.setPushType(pushType);
        sysNotificationRecordEntity.setPushStatus(0);
        sysNotificationRecordEntity.setRead(0);
        sysNotificationRecordEntity.setContent(firebasePushDTO.getBody());
        UpdateEntityTimeUtil.setBaseEntityTime(appUserInfo.getTimeZone(), sysNotificationRecordEntity);
        if (Objects.nonNull(expireTime)) {
            sysNotificationRecordEntity.setExpireTime(expireTime);
        }
        return sysNotificationRecordEntity;
    }

    public Integer getPushType(Integer platform) {
        return PlatformEnum.IOS.getPlatform().equals(platform) ? PushTypeEnum.IOS_PUSH.getType()
                : (PlatformEnum.ANDROID.getPlatform().equals(platform) ? PushTypeEnum.ANDROID_PUSH.getType() : -1);
    }

    private void batchSaveNotificationRecord(List<Long> userIdList, Map<Long, PushUserInfoDTO> allUserInfoMap,
                                             FirebasePushDTO firebasePushDTO, Map<Long, Long> expireTimeMap) {
        List<SysNotificationRecordEntity> notificationRecordList = new ArrayList<>();
        for (Long userId : userIdList) {
            PushUserInfoDTO appUserInfo = allUserInfoMap.get(userId);
            if (Objects.nonNull(appUserInfo)) {
                notificationRecordList.add(buildNotificationRecord(appUserInfo, firebasePushDTO,
                        Objects.isNull(expireTimeMap) ? null : expireTimeMap.get(userId)));
            }
        }
        sysNotificationRecordDAO.saveBatch(notificationRecordList);
    }
}
