package com.mira.job.service.manager;

import com.google.common.collect.Lists;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.message.enums.PlatformEnum;
import com.mira.api.message.enums.PushTypeEnum;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.dal.dao.master.SysNotificationRecordDAO;
import com.mira.job.dal.entity.master.SysNotificationRecordEntity;
import com.mira.job.service.util.FirebasePushUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * notification push manager
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeskPushManager {
    @Resource
    private SysNotificationRecordDAO sysNotificationRecordDAO;

    /**
     * Firebase Push
     *
     * @param firebasePushMap key:推送内容
     * @param expireTimeMap   过期时间（时间戳）
     */
    public void firebasePush(Map<FirebasePushDTO, List<PushUserInfoDTO>> firebasePushMap,
                             Map<Long, Long> expireTimeMap) {
        // 按照通知类型分组推送
        for (Map.Entry<FirebasePushDTO, List<PushUserInfoDTO>> firebaseEntry : firebasePushMap.entrySet()) {
            CompletableFuture.runAsync(() -> {
                // 分组，每组500（Firebase 最大单次次数）
                List<String> pushTokenList = new ArrayList<>();
                for (PushUserInfoDTO pushUserInfoDTO : firebaseEntry.getValue()) {
                    String pushToken = pushUserInfoDTO.getPushToken();
                    if (StringUtils.isBlank(pushToken)) {
                        continue;
                    }
                    pushTokenList.add(pushToken);
                }
                List<List<String>> pushTokenPartition = Lists.partition(pushTokenList, 500);
                // 分组推送
                for (List<String> list : pushTokenPartition) {
                    FirebasePushUtil.sendMultiPush(firebaseEntry.getKey(), list);
                }
                // 保存记录
                batchSaveNotificationRecord(firebaseEntry, expireTimeMap);

                log.info("Define ID:{} -> Number of users for this push:{}", firebaseEntry.getKey().getDefineId(), firebaseEntry.getValue().size());
                log.info("Define ID:{} -> Push finished", firebaseEntry.getKey().getDefineId());
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("Firebase push error:", ex);
                return null;
            });
        }
    }

    /**
     * Firebase Push Silent
     *
     * @param firebasePushMap key:推送内容
     * @param expireTimeMap   过期时间（时间戳）
     */
    public void firebasePushSilent(Map<FirebasePushDTO, List<PushUserInfoDTO>> firebasePushMap,
                                   Map<Long, Long> expireTimeMap) {
        // 按照通知类型分组推送
        for (Map.Entry<FirebasePushDTO, List<PushUserInfoDTO>> firebaseEntry : firebasePushMap.entrySet()) {
            CompletableFuture.runAsync(() -> {
                // 分组，每组500（Firebase 最大单次次数）
                List<String> pushTokenList = new ArrayList<>();
                for (PushUserInfoDTO pushUserInfoDTO : firebaseEntry.getValue()) {
                    String pushToken = pushUserInfoDTO.getPushToken();
                    if (StringUtils.isBlank(pushToken)) {
                        continue;
                    }
                    pushTokenList.add(pushToken);
                }
                List<List<String>> pushTokenPartition = Lists.partition(pushTokenList, 500);
                // 分组推送
                for (List<String> list : pushTokenPartition) {
                    FirebasePushUtil.sendMultiPushBySilent(firebaseEntry.getKey(), list);
                }
                // 保存记录
                batchSaveNotificationRecord(firebaseEntry, expireTimeMap);

                log.info("Define ID:{} -> Number of users for this push:{}", firebaseEntry.getKey().getDefineId(), firebaseEntry.getValue().size());
                log.info("Define ID:{} -> Push finished", firebaseEntry.getKey().getDefineId());
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("Firebase push error:", ex);
                return null;
            });
        }
    }

    private void batchSaveNotificationRecord(Map.Entry<FirebasePushDTO, List<PushUserInfoDTO>> firebasePushEntry,
                                             Map<Long, Long> expireTimeMap) {
        FirebasePushDTO firebasePushDTO = firebasePushEntry.getKey();
        List<PushUserInfoDTO> pushUserInfoList = firebasePushEntry.getValue();

        List<SysNotificationRecordEntity> notificationRecordList = new ArrayList<>();
        for (PushUserInfoDTO pushUserInfoDTO : pushUserInfoList) {
            if (Objects.nonNull(pushUserInfoDTO)) {
                notificationRecordList.add(buildNotificationRecord(pushUserInfoDTO, firebasePushDTO, expireTimeMap));
            }
        }
        if (CollectionUtils.isNotEmpty(notificationRecordList)) {
            sysNotificationRecordDAO.saveBatch(notificationRecordList);
        }
    }

    private SysNotificationRecordEntity buildNotificationRecord(PushUserInfoDTO appUserInfo,
                                                                FirebasePushDTO firebasePushDTO,
                                                                Map<Long, Long> expireTimeMap) {
        Integer platform = appUserInfo.getPlatform();
        Integer pushType = getPushType(platform);

        SysNotificationRecordEntity sysNotificationRecordEntity = new SysNotificationRecordEntity();
        sysNotificationRecordEntity.setUserId(appUserInfo.getUserId());
        sysNotificationRecordEntity.setNotificationDefineId(firebasePushDTO.getDefineId());
        sysNotificationRecordEntity.setPushType(pushType);
        sysNotificationRecordEntity.setPushStatus(0);
        sysNotificationRecordEntity.setRead(0);
        sysNotificationRecordEntity.setContent(firebasePushDTO.getBody());
        UpdateEntityTimeUtil.setBaseEntityTime(appUserInfo.getTimeZone(), sysNotificationRecordEntity);
        if (MapUtils.isNotEmpty(expireTimeMap)) {
            if (expireTimeMap.size() == 1 && expireTimeMap.containsKey(-99L)) {
                sysNotificationRecordEntity.setExpireTime(expireTimeMap.get(-99L));
            } else {
                sysNotificationRecordEntity.setExpireTime(expireTimeMap.get(appUserInfo.getUserId()));
            }
        }
        return sysNotificationRecordEntity;
    }

    public Integer getPushType(Integer platform) {
        return PlatformEnum.IOS.getPlatform().equals(platform) ? PushTypeEnum.IOS_PUSH.getType()
                : (PlatformEnum.ANDROID.getPlatform().equals(platform) ? PushTypeEnum.ANDROID_PUSH.getType() : -1);
    }
}
