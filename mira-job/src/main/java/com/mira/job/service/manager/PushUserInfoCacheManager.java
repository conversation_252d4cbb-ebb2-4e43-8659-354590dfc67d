package com.mira.job.service.manager;

import com.mira.core.util.ThreadPoolUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.dal.dao.master.AppUserInfoDAO;
import com.mira.job.properties.JobProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.ehcache.Cache;
import org.ehcache.CacheManager;
import org.ehcache.config.CacheConfiguration;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.config.builders.ExpiryPolicyBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.units.EntryUnit;
import org.ehcache.config.units.MemoryUnit;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * ehcache manager
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PushUserInfoCacheManager {
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private JobProperties jobProperties;

    private final CacheManager cacheManager;
    private final String cacheFileName = "pushuserinfo-cache";
    private final String cacheIndexKey = "pushuserinfo-cache-index";

    public PushUserInfoCacheManager() {
        CacheConfiguration<String, ArrayList> configuration = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(String.class, ArrayList.class, ResourcePoolsBuilder.newResourcePoolsBuilder()
                        .heap(1, EntryUnit.ENTRIES)
                        .disk(100, MemoryUnit.GB, true))
                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofHours(2)))
                .build();

        this.cacheManager = CacheManagerBuilder.newCacheManagerBuilder()
                .with(CacheManagerBuilder.persistence("./cache"))
                .withCache("pushuserinfo-cache", configuration)
                .build(true);
    }

    private void put(String key, ArrayList value) {
        Cache<String, ArrayList> cache = cacheManager.getCache(cacheFileName, String.class, ArrayList.class);
        cache.put(key, value);
    }

    private ArrayList get(String key) {
        Cache<String, ArrayList> cache = cacheManager.getCache(cacheFileName, String.class, ArrayList.class);
        return cache.get(key);
    }

    private void remove(String key) {
        Cache<String, ArrayList> cache = cacheManager.getCache(cacheFileName, String.class, ArrayList.class);
        cache.remove(key);
    }

    public List<String> getCacheIndex() {
        ArrayList cacheIndexList = get(cacheIndexKey);
        if (0 == jobProperties.getCacheSwitch()) {
            remove(cacheIndexKey);
            if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                cacheIndexList = null;
            }
        }
        if (CollectionUtils.isEmpty(cacheIndexList)) {
            synchronized (PushUserInfoCacheManager.class) {
                // double check
                cacheIndexList = get(cacheIndexKey);
                if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                    return cacheIndexList;
                }
                getPushUserInfoCache("-1");
                return get(cacheIndexKey);
            }
        }
        return cacheIndexList;
    }

    public ArrayList<PushUserInfoDTO> getPushUserInfoCache(String cacheIndex) {
        // query cache
        ArrayList pushUserInfoList = get(cacheIndex);
        if (CollectionUtils.isNotEmpty(pushUserInfoList)) {
            return pushUserInfoList;
        }

        // query db
        synchronized (PushUserInfoCacheManager.class) {
            // double check
            pushUserInfoList = get(cacheIndex);
            if (CollectionUtils.isNotEmpty(pushUserInfoList)) {
                return pushUserInfoList;
            }
            // batch query
            int queryBatch = 10000;
            long pageSize = 0;
            long recordCount = appUserInfoDAO.getCount();
            log.info("User records size: {}", recordCount);
            // batch query
            long cdCount = recordCount % queryBatch == 0 ? recordCount / queryBatch : recordCount / queryBatch + 1;
            CountDownLatch cd = new CountDownLatch((int) cdCount);
            long queryIndex = cdCount;
            ArrayList<String> queryIndexList = new ArrayList<>();

            while (recordCount > 0) {
                long finalPageSize = pageSize;
                long finalQueryIndex = queryIndex;
                CompletableFuture.runAsync(() -> {
                    ArrayList<PushUserInfoDTO> recordList = appUserInfoDAO.getBaseMapper().queryUserInfo(finalPageSize, queryBatch);
                    if (CollectionUtils.isNotEmpty(recordList)) {
                        String key = String.valueOf(finalQueryIndex);
                        queryIndexList.add(key);
                        put(key, recordList);
                    }
                    cd.countDown();
                }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                    cd.countDown();
                    log.error("get push user info error occurred", ex);
                    return null;
                });

                pageSize += queryBatch;
                recordCount -= queryBatch;
                queryIndex--;
            }

            // wait future
            try {
                cd.await();
            } catch (InterruptedException ex) {
                log.error(ex.getMessage(), ex);
            }
            // cache index
            if (CollectionUtils.isNotEmpty(queryIndexList)) {
                put(cacheIndexKey, queryIndexList);
            }
        }

        return get(cacheIndex);
    }
}
