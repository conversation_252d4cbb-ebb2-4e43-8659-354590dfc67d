package com.mira.mongo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.mongo.dto.burying.AccessInInfoMongoDTO;
import com.mira.api.mongo.dto.burying.AccessOutInfoMongoDTO;
import com.mira.api.user.dto.user.UserRecentAccessDTO;
import com.mira.mongo.domain.AccessInLog;
import com.mira.mongo.domain.AccessLog;
import com.mira.mongo.domain.RecentAccessLog;
import com.mira.mongo.repository.AccessInLogRepository;
import com.mira.mongo.repository.AccessLogRepository;
import com.mira.mongo.repository.RecentAccessLogRepository;
import com.mira.mongo.service.IAccessLogService;
import com.mira.mongo.util.DomainTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * access log service impl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AccessLogServiceImpl implements IAccessLogService {
    @Resource
    private AccessLogRepository accessLogRepository;
    @Resource
    private AccessInLogRepository accessInLogRepository;
    @Resource
    private RecentAccessLogRepository recentAccessLogRepository;

    @Override
    public void accessLog(AccessOutInfoMongoDTO accessOutInfoDTO) {
        String ip = accessOutInfoDTO.getIp();
        String timeZone = accessOutInfoDTO.getTimeZone();

        AccessLog accessLog = new AccessLog();
        accessLog.setIp(ip);
        accessLog.setOs(accessOutInfoDTO.getOs());
        accessLog.setVersion(accessOutInfoDTO.getVersion());
        accessLog.setDevice(accessOutInfoDTO.getDevice());
        accessLog.setAppVersion(accessOutInfoDTO.getAppVersion());
        accessLog.setTimes(1L);
        accessLog.setUserId(accessOutInfoDTO.getUserId());
        accessLog.setNetworkType(accessOutInfoDTO.getNetworkType());
        accessLog.setHomeStart(accessOutInfoDTO.getHomeStart());
        accessLog.setHomeEnd(accessOutInfoDTO.getHomeEnd());
        accessLog.setCalendarStart(accessOutInfoDTO.getCalendarStart());
        accessLog.setCalendarEnd(accessOutInfoDTO.getCalendarEnd());
        accessLog.setCureStart(accessOutInfoDTO.getCureStart());
        accessLog.setCureEnd(accessOutInfoDTO.getCureEnd());
        DomainTimeUtil.setEntityTime(timeZone, accessLog);

        accessLogRepository.save(accessLog);
    }

    @Override
    public void accessInLog(AccessInInfoMongoDTO accessInInfoDTO) {
        String ip = accessInInfoDTO.getIp();
        String timeZone = accessInInfoDTO.getTimeZone();

        AccessInLog accessInLog = new AccessInLog();
        accessInLog.setIp(ip);
        accessInLog.setOs(accessInInfoDTO.getOs());
        accessInLog.setVersion(accessInInfoDTO.getVersion());
        accessInLog.setDevice(accessInInfoDTO.getDevice());
        accessInLog.setAppVersion(accessInInfoDTO.getAppVersion());
        accessInLog.setUserId(accessInInfoDTO.getUserId());
        accessInLog.setNetworkType(accessInInfoDTO.getNetworkType());
        accessInLog.setAccessTimeStamp(accessInInfoDTO.getAccessTimeStamp());
        DomainTimeUtil.setEntityTime(timeZone, accessInLog);

        accessInLogRepository.save(accessInLog);
    }

    @Override
    public void recentAccessLog(AccessInInfoMongoDTO accessInInfoDTO) {
        Long userId = accessInInfoDTO.getUserId();
        String ip = accessInInfoDTO.getIp();
        String timeZone = accessInInfoDTO.getTimeZone();

        try {
            RecentAccessLog recentAccessLog = recentAccessLogRepository.findByUserId(userId);
            if (recentAccessLog == null) {
                recentAccessLog = new RecentAccessLog();
            }
            recentAccessLog.setUserId(userId);
            recentAccessLog.setIp(ip);
            recentAccessLog.setOs(accessInInfoDTO.getOs());
            recentAccessLog.setVersion(accessInInfoDTO.getVersion());
            recentAccessLog.setDevice(accessInInfoDTO.getDevice());
            recentAccessLog.setAppVersion(accessInInfoDTO.getAppVersion());
            recentAccessLog.setUserId(userId);
            recentAccessLog.setNetworkType(accessInInfoDTO.getNetworkType());
            DomainTimeUtil.setEntityTime(timeZone, recentAccessLog);

            recentAccessLogRepository.save(recentAccessLog);
        } catch (Exception e) {
            log.error("user:{} save or update recent log error:{}", userId, e.getMessage());
        }
    }

    @Override
    public UserRecentAccessDTO getUserRecentAccess(Long userId) {
        RecentAccessLog recentAccessLog = recentAccessLogRepository.findByUserId(userId);
        return BeanUtil.toBean(recentAccessLog, UserRecentAccessDTO.class);
    }
}
