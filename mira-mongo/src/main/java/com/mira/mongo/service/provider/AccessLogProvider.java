package com.mira.mongo.service.provider;

import com.mira.api.mongo.dto.burying.AccessInInfoMongoDTO;
import com.mira.api.mongo.dto.burying.AccessOutInfoMongoDTO;
import com.mira.api.mongo.provider.IAccessLogProvider;
import com.mira.api.user.dto.user.UserRecentAccessDTO;
import com.mira.core.response.CommonResult;
import com.mira.mongo.service.IAccessLogService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * access log provider
 *
 * <AUTHOR>
 */
@RestController
public class AccessLogProvider implements IAccessLogProvider {
    @Resource
    private IAccessLogService accessLogService;

    @Override
    public CommonResult<String> accessLog(AccessOutInfoMongoDTO accessOutInfoDTO) {
        accessLogService.accessLog(accessOutInfoDTO);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> accessInLog(AccessInInfoMongoDTO accessInInfoDTO) {
        accessLogService.accessInLog(accessInInfoDTO);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> recentAccessLog(AccessInInfoMongoDTO accessInInfoDTO) {
        accessLogService.recentAccessLog(accessInInfoDTO);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<UserRecentAccessDTO> getUserRecentAccess(Long userId) {
        UserRecentAccessDTO userRecentAccessDTO = accessLogService.getUserRecentAccess(userId);
        return CommonResult.OK(userRecentAccessDTO);
    }
}
