package com.mira.script.dal.dao.app;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.script.dal.entity.app.AppDataUploadEntity;
import com.mira.script.dal.mapper.app.AppDataUploadMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * 蓝牙上传数据
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class AppDataUploadDAO extends ServiceImpl<AppDataUploadMapper, AppDataUploadEntity> {

    @Slave
    public List<AppDataUploadEntity> listRunBoardCheckEntity() {
        return list(Wrappers.<AppDataUploadEntity>lambdaQuery()
                            .eq(AppDataUploadEntity::getError, "00")
                            .in(AppDataUploadEntity::getWarning, Arrays.asList("00", "05", "09"))
                            .eq(AppDataUploadEntity::getAutoFlag, 0)
                            .isNull(AppDataUploadEntity::getRunBoardFlag)
        );
    }

    @Slave
    public List<AppDataUploadEntity> listBySql(String sql, long cycle, long testingDataCount) {
        log.info("第【{}】次查询；剩余:【{}】", cycle, testingDataCount);
        return this.baseMapper.listBySql(sql);
    }

    @Slave
    public long countRunBoardCheckEntity() {
        return count(
                Wrappers.<AppDataUploadEntity>lambdaQuery()
                        .eq(AppDataUploadEntity::getError, "00")
                        .in(AppDataUploadEntity::getWarning, Arrays.asList("00", "05", "09"))
                        .eq(AppDataUploadEntity::getAutoFlag, 0)
                        .isNull(AppDataUploadEntity::getRunBoardFlag)
        );
    }

    @Slave
    public List<AppDataUploadEntity> listBySnAndWandType18(String sn) {
        return list(Wrappers.<AppDataUploadEntity>lambdaQuery()
                .eq(AppDataUploadEntity::getSn, sn)
                .eq(AppDataUploadEntity::getTestWandType, 18));
    }

    @Slave
    public List<AppDataUploadEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppDataUploadEntity>lambdaQuery()
                .eq(AppDataUploadEntity::getUserId, userId)
        );
    }
}
