package com.mira.script.dal.entity.app;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户信息表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user")
public class AppUserEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    private String name;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐
     */
    private String salt;

    /**
     * 密码等级，1～10，默认10级（最高）
     */
    private Integer passwordGrade;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 状态 0：禁用，1：正常，2：未激活
     */
    private Integer status;

    /**
     * 来源：0:1.2数据同步，1:1.3新数据，2:Genea医生创建的用户，3:Genea医生，4:1.4新数据
     */
    private Integer source;

    /**
     * 0:1.2用户未同步，1:1.2用户已同步到1.3，2:已同步到1.4
     */
    private Integer transferFlag;

    /**
     * 测试模式：0否，1是
     */
    private Integer testMode;

    /**
     * 用户当前ip地址
     */
    private String currentIp;

    /**
     * ip 修改时间
     */
    private Long ipModifyTime;

    /**
     * ip 修改时间
     */
    private String ipModifyTimeStr;

    /**
     * 国家编号
     */
    private String countryCode;

    /**
     * 洲编号
     */
    private String continentCode;

    /**
     * 当前货币
     */
    private String currentCurrency;

    /**
     * 国家编号修改时间
     */
    private String countryModifyTimeStr;

    /**
     * 用户删除账号类型:删除测试数据：0，保留测试数据：1
     */
    private Integer deleteType;

}
