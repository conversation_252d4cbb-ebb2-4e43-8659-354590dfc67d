package com.mira.script.service.hub;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mira.script.utils.TxtUtil;
import com.mira.script.dal.dao.hub.HubUserInfoDAO;
import com.mira.script.dal.dao.hub_duquesne.HubDuquesneUserInfoDAO;
import com.mira.script.dal.entity.hub.HubUserInfoEntity;
import com.mira.script.dal.entity.hub_duquesne.HubDuquesneUserInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-12-22
 **/
@Slf4j
@Service
public class HubDuquesneUserInfoService {
    @Resource
    private HubUserInfoDAO hubUserInfoDAO;

    @Resource
    private HubDuquesneUserInfoDAO hubDuquesneUserInfoDAO;

    public void transferHubInfo() {
        List<String> hubIds = TxtUtil.getHubIds();

        List<HubUserInfoEntity> hubUserInfoEntities = hubUserInfoDAO.list(Wrappers.<HubUserInfoEntity>lambdaQuery()
                                                                                  .in(HubUserInfoEntity::getHubId, hubIds));
        log.info("查询hubUserInfo结束:【{}】", hubUserInfoEntities.size());
        List<HubDuquesneUserInfoEntity> hubDuquesneUserInfoEntities = new ArrayList<>();
        hubUserInfoEntities
                .forEach(hubUserInfoEntity -> {
                    HubDuquesneUserInfoEntity hubDuquesneUserInfoEntity = new HubDuquesneUserInfoEntity();
                    BeanUtils.copyProperties(hubUserInfoEntity, hubDuquesneUserInfoEntity);
                    hubDuquesneUserInfoEntity.setId(null);
                    hubDuquesneUserInfoEntities.add(hubDuquesneUserInfoEntity);
                });
        log.info("开始插入hubUserInfo");
        hubDuquesneUserInfoDAO.saveBatch(hubDuquesneUserInfoEntities, 50);
        log.info("结束插入hubUserInfo");
    }
}
