package com.mira.sso.consts;

/**
 * 用户标记常量
 *
 * <AUTHOR>
 */
public class UserFlagConst {
    /**
     * 首次注册标记
     */
    public final static int REGISTER_FLAG = 0;

    /**
     * 再次调用注册标记
     */
    public final static int REGISTER_RESEND_FLAG = 1;

    /**
     * 首次忘记密码
     */
    public final static int RESET_PASSWORD_FLAG = 0;

    /**
     * 再次调用忘记密码
     */
    public final static int RESET_PASSWORD_RESEND_FLAG = 1;

    /**
     * 首次修改邮箱
     */
    public final static int CHANGE_EMAIL_FLAG = 0;

    /**
     * 再次调用修改邮箱
     */
    public final static int CHANGE_EMAIL_RESEND_FLAG = 1;

    /**
     * 首次邀请
     */
    public final static int INVITE_FLAG = 0;

    /**
     * 再次调用邀请
     */
    public final static int INVITE_RESEND_FLAG = 1;
}
