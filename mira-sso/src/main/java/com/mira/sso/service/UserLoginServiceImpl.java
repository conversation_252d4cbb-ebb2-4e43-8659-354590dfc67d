package com.mira.sso.service;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.bbt.BBTInfoDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.clinic.dto.UserClinicDTO;
import com.mira.api.clinic.provider.IClinicProvider;
import com.mira.api.iam.dto.AuthTokenDTO;
import com.mira.api.iam.provider.IAuthProvider;
import com.mira.api.sso.dto.LoginInfoDTO;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.thirdparty.dto.blog.Ip2CountryDTO;
import com.mira.api.thirdparty.dto.shopify.CustomerRegisterDTO;
import com.mira.api.thirdparty.provider.IBlogProvider;
import com.mira.api.thirdparty.provider.IShopifyProvider;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.consts.UserSourceConst;
import com.mira.api.user.consts.UserStatusConst;
import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.core.util.*;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.sso.async.KlaviyoProducer;
import com.mira.sso.consts.UserChannelConst;
import com.mira.sso.consts.UserConst;
import com.mira.sso.controller.vo.UserInfoVO;
import com.mira.sso.controller.vo.WebsiteLoginVO;
import com.mira.sso.dal.dao.*;
import com.mira.sso.dal.entity.AppUserBindLogEntity;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.sso.dal.entity.AppUserInfoEntity;
import com.mira.sso.dal.entity.UserDeviceEntity;
import com.mira.sso.exception.SsoException;
import com.mira.sso.producer.EmailProducer;
import com.mira.sso.properties.CacheExpireProperties;
import com.mira.sso.service.dto.ResetInitPasswordDTO;
import com.mira.sso.service.dto.UserDeleteDTO;
import com.mira.sso.service.dto.UserRegisterDTO;
import com.mira.sso.service.dto.VerifyLoginDTO;
import com.mira.sso.service.event.LoginLogDTO;
import com.mira.sso.service.event.LoginLogEvent;
import com.mira.sso.service.manager.CacheManager;
import com.mira.sso.service.manager.UserInfoManager;
import com.mira.sso.service.util.ShopifyMultiPassUtil;
import com.mira.web.properties.SysDictProperties;
import com.mira.web.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.List;
import java.util.Objects;

/**
 * 用户登录接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserLoginServiceImpl implements IUserLoginService {
    private final AppUserDAO appUserDAO;
    private final AppUserInfoDAO appUserInfoDAO;
    private final AppUserBindLogDAO appUserBindLogDAO;
    private final ReportGoodUsersDAO reportGoodUsersDAO;
    private final UserReminderComplaintDAO userReminderComplaintDAO;
    private final UserDeviceDAO userDeviceDAO;
    private final UserGoalTrialDAO userGoalTrialDAO;
    private final UserProductTrialDAO userProductTrialDAO;
    private final SysDictProperties sysDictProperties;
    private final CacheExpireProperties cacheExpireProperties;
    private final UserInfoManager userInfoManager;
    private final CacheManager cacheManager;
    private final EmailProducer emailProducer;
    private final IAuthProvider authProvider;
    private final IBluetoothProvider bluetoothProvider;
    private final IBlogProvider blogProvider;
    private final KlaviyoProducer klaviyoProducer;
    private final IClinicProvider clinicProvider;
    private final IShopifyProvider shopifyProvider;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String login(LoginInfoDTO loginInfoDTO) throws Exception {
        AppUserEntity appUser = appUserDAO.getByEmail(loginInfoDTO.getEmail());
        if (Objects.isNull(appUser)) {
            throw new SsoException("The account doesn't exist. Let's get you registered first.");
        }
        checkStatus(appUser);

        // 正常模式用户
        if (UserConst.MODE_NORMAL == appUser.getTestMode()) {
            handleNormalUser(appUser, loginInfoDTO);
        }

        // check device id
        checkDeviceId(appUser, UserChannelConst.APP, loginInfoDTO.getDeviceId());

        // 记录登录日志
        SpringContextHolder.publishEvent(new LoginLogEvent(new LoginLogDTO()
                .setLoginInfoDTO(loginInfoDTO)
                .setAppUser(appUser)
                .setSuccess(1)
                .setChannel(UserChannelConst.APP)));
        // Klaviyo
        klaviyoProducer.userLogin(appUser, loginInfoDTO.getOs());

        // id-uuid
        return getAppToken(appUser, loginInfoDTO);
    }

    private void checkStatus(AppUserEntity appUser) {
        if (UserStatusConst.DISABLE == appUser.getStatus()) {
            throw new SsoException("Your account is locked. Please contact us for help.");
        }
        if (UserStatusConst.INACTIVE == appUser.getStatus()) {
            throw new SsoException("Your account is not active. Please check your email to activate the account.");
        }
    }

    /**
     * app端deviceId只允许存在一个，如果数据库不存在或者不一致，需要verifyCode校验，校验正确后更新数据库的deviceId。
     * <p>
     * website端deviceId允许存在3个，如果数据库不存在传入的deviceId，就需要verifyCode校验,从第4个开始，先入先出依次剔除更新。
     *
     * @param appUser  用户信息
     * @param channel  渠道
     * @param deviceId 设备id
     */
    private void checkDeviceId(AppUserEntity appUser, int channel, String deviceId) {
        if (sysDictProperties.getVerifyCodeSwitch() == 0) {
            return;
        }

        Boolean isWhiteUser = Boolean.FALSE;
        try {
            isWhiteUser = cacheManager.isInWhiteDeviceList(appUser.getEmail());
        } catch (Exception e) {
            log.error("DEVICE_ID_WHITE key not found, please check.");
        }
        if (isWhiteUser) {
            return;
        }

        // 特定email后缀不校验
        if (appUser.getEmail().endsWith(sysDictProperties.getEmailSuffix())) {
            return;
        }

        if (StringUtils.isNotBlank(deviceId) && UserChannelConst.APP == channel) {
            AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(appUser.getId());
            // app登录时，校验deviceId是否和DB一致
            UserDeviceEntity appDevice = userDeviceDAO.getByUserIdAndChannel(appUser.getId(), channel);
            if (appDevice == null || !deviceId.equals(appDevice.getDeviceId())) {
                String verifyCode = emailProducer.sendDeviceVerifyCode(appUser, appUserInfo);
                cacheManager.cacheDeviceVerifyCode(appUser.getId(), verifyCode);
                throw new SsoException(BizCodeEnum.DEVICEID_ERROR);
            }
        }

        if (StringUtils.isNotBlank(deviceId) && UserChannelConst.WEBSITE == channel) {
            AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(appUser.getId());
            // website登录时，校验deviceId是否和DB一致
            List<UserDeviceEntity> websiteDevices = userDeviceDAO.listByUserIdAndChannel(appUser.getId(), channel);
            if (websiteDevices == null) {
                String verifyCode = emailProducer.sendDeviceVerifyCode(appUser, appUserInfo);
                cacheManager.cacheDeviceVerifyCode(appUser.getId(), verifyCode);
                throw new SsoException(BizCodeEnum.DEVICEID_ERROR);
            }
            boolean exist = false;
            for (UserDeviceEntity websiteDevice : websiteDevices) {
                if (deviceId.equals(websiteDevice.getDeviceId())) {
                    exist = true;
                    break;
                }
            }
            if (!exist) {
                String verifyCode = emailProducer.sendDeviceVerifyCode(appUser, appUserInfo);
                cacheManager.cacheDeviceVerifyCode(appUser.getId(), verifyCode);
                throw new SsoException(BizCodeEnum.DEVICEID_ERROR);
            }
        }
    }

    private void handleNormalUser(AppUserEntity appUser, LoginInfoDTO loginInfoDTO) {
        String encryptPassword = PasswordUtil.encryptPassword(loginInfoDTO.getPassword(), appUser.getSalt());
        if (appUser.getPasswordGrade() == UserConst.PASSWORD_GRADE_LOWEST && appUser.getPassword().equals(encryptPassword)) {
            throw new SsoException(BizCodeEnum.RESET_PASSWORD);
        }
        // 是否锁定
        if (cacheManager.cacheExist(appUser.getId(), RedisCacheKeyConst.USER_LOGIN_LOCK)) {
            String errorMsg = "Your account is locked. Please try again in a bit or reset your password.";
            throw new SsoException(errorMsg);
        }
        // 是否需要数据迁移
        if (UserSourceConst.ZERO == appUser.getSource() && 0 == appUser.getTransferFlag()) {
            throw new SsoException("User need transfer.");
        }
        // 密码校验
        if (!PasswordUtil.match(loginInfoDTO.getPassword(), appUser.getPassword(), appUser.getSalt())) {
            // 记录失败登录的日志
            SpringContextHolder.publishEvent(new LoginLogEvent(new LoginLogDTO()
                    .setLoginInfoDTO(loginInfoDTO)
                    .setAppUser(appUser)
                    .setSuccess(0)));
            // 校验错误次数
            Long cacheLoginErrorCount = cacheManager.getCacheLoginErrorIncr(appUser.getId());
            if (Objects.isNull(cacheLoginErrorCount)) {
                cacheLoginErrorCount = 1L;
            }
            if (cacheLoginErrorCount >= sysDictProperties.getLoginLock()) {
                // lock
                cacheManager.cacheLoginLock(appUser.getId(), cacheExpireProperties.getLoginLock());
                String errorMsg = "Your account is temporarily locked. please try again in {0} minutes or reset your password.";
                throw new SsoException(MessageFormat.format(errorMsg, cacheExpireProperties.getLoginLock()));
            }

            throw new SsoException("Email address or password doesn't match. Give it another try?");
        }
        cacheManager.cacheDelete(appUser.getId(), RedisCacheKeyConst.USER_LOGIN_ERROR);
    }

    private String getAppToken(AppUserEntity appUser, LoginInfoDTO loginInfoDTO) throws Exception {
        // 删除旧令牌
        String oldToken = cacheManager.getAppMarkToken(appUser.getId());
        if (StringUtils.isNotBlank(oldToken)) {
            authProvider.deleteToken(oldToken, UserTypeEnum.APP_USER.getType());
        }
        // 设置用户类别
        ContextHolder.put(HeaderConst.USER_TYPE, UserTypeEnum.APP_USER.getType());
        // 发放令牌
        CommonResult<AuthTokenDTO> tokenResult = authProvider.generalAccessToken(loginInfoDTO.getEmail(), loginInfoDTO.getPassword());

        // 更新用户信息
        appUser.setTimeZone(ContextHolder.get(HeaderConst.TIME_ZONE));
        appUser.setCurrentIp(ContextHolder.get(HeaderConst.IP));
        long nowTimeStamp = System.currentTimeMillis();
        appUser.setModifyTime(nowTimeStamp);
        appUser.setModifyTimeStr(ZoneDateUtil.format(appUser.getTimeZone(), nowTimeStamp, DatePatternConst.DATE_TIME_PATTERN));
        try {
            CommonResult<Ip2CountryDTO> commonResult = blogProvider.getCountryByIp(appUser.getCurrentIp(), appUser.getId());
            Ip2CountryDTO ip2CountryDTO = commonResult.getData();
            appUser.setCountryCode(ip2CountryDTO.getCountryCode());
            appUser.setContinentCode(ip2CountryDTO.getContinentCode());
            appUser.setCountryModifyTimeStr(appUser.getModifyTimeStr());
        } catch (Exception e) {
            log.error("an exception occurred to update the user:{} ip, IP:{}", appUser.getId(), appUser.getCurrentIp());
        }
        appUser.setTransferFlag(null);

        //mira-country
        String miraCountry = ContextHolder.get(HeaderConst.COUNTRY_FLAG);
        if (StringUtils.isNotBlank(miraCountry)) {
            appUser.setMiraCountry(miraCountry);
        }

        appUserDAO.updateById(appUser);
        cacheManager.deleteUserDetailCache(appUser.getId());

        return appUser.getId().toString().concat("-").concat(tokenResult.getData().getAccess_token());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String appCodeLogin(VerifyLoginDTO verifyLoginDTO) throws Exception {
        AppUserEntity appUser = appUserDAO.getByEmail(verifyLoginDTO.getEmail());
        if (Objects.isNull(appUser)) {
            throw new SsoException("The account doesn't exist.");
        }
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // 校验verifyCode是否一致
        checkVerifyCode(appUser, verifyLoginDTO);

        // 其他校验
        handleNormalUser(appUser, verifyLoginDTO);

        // device save or update
        UserDeviceEntity appDevice = userDeviceDAO.getByUserIdAndChannel(appUser.getId(), UserChannelConst.APP);
        if (appDevice == null) {
            createDevice(appUser.getId(), verifyLoginDTO.getDeviceId(), UserChannelConst.APP, timeZone);
        } else {
            updateAppDevice(verifyLoginDTO.getDeviceId(), timeZone, appDevice);
        }

        // 记录登录日志
        SpringContextHolder.publishEvent(new LoginLogEvent(new LoginLogDTO()
                .setLoginInfoDTO(verifyLoginDTO)
                .setAppUser(appUser)
                .setSuccess(1)
                .setChannel(UserChannelConst.APP)));
        // Klaviyo
        klaviyoProducer.userLogin(appUser, verifyLoginDTO.getOs());

        // id-uuid
        return getAppToken(appUser, verifyLoginDTO);
    }

    @Override
    public UserInfoVO info() {
        // 登录用户详情
        LoginUserInfoDTO loginUserInfoDTO = userInfoManager.loginUserInfo();
        Long userId = loginUserInfoDTO.getUserId();

        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtil.copyProperties(loginUserInfoDTO, userInfoVO);

        if (UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(userInfoVO.getGoalStatus())) {
            userInfoVO.getGoalStatusOptions().add(UserGoalEnum.PREGNANCY_TRACKING.getValue());
        }

        updateGoalOptions(loginUserInfoDTO, userInfoVO);
        userInfoVO.setConditions(StringListUtil.strToIntegerList(loginUserInfoDTO.getConditions(), ","));

        if (StringUtils.isNotBlank(loginUserInfoDTO.getPeriodData())
                && !loginUserInfoDTO.getPeriodData().equals("[]")) {
            List<UserPeriodDataDTO> userPeriodDataDTOList = JsonUtil.toArray(loginUserInfoDTO.getPeriodData(), UserPeriodDataDTO.class);
            UserPeriodDataDTO recentPeriodDataDTO = userPeriodDataDTOList.get(userPeriodDataDTOList.size() - 1);
            UserInfoVO.RecentPeriod recentPeriod = new UserInfoVO.RecentPeriod();
            recentPeriod.setDatePeriodStart(ZoneDateUtil.timestamp(loginUserInfoDTO.getTimeZone(),
                    recentPeriodDataDTO.getDate_period_start(), DatePatternConst.DATE_PATTERN));
            recentPeriod.setDatePeriodEnd(ZoneDateUtil.timestamp(loginUserInfoDTO.getTimeZone(),
                    recentPeriodDataDTO.getDate_period_end(), DatePatternConst.DATE_PATTERN));
            userInfoVO.setRecentPeriod(recentPeriod);
        } else if (StringUtils.isNotBlank(loginUserInfoDTO.getPeriodData())
                && loginUserInfoDTO.getPeriodData().equals("[]")) {
            userInfoVO.setRecentPeriod(new UserInfoVO.RecentPeriod());
        }

        if (StringUtils.isNotBlank(loginUserInfoDTO.getPartnerEmail())) {
            UserInfoVO.Partner partner = new UserInfoVO.Partner();
            partner.setPartnerEmail(loginUserInfoDTO.getPartnerEmail());
            partner.setPartnerStatus(loginUserInfoDTO.getPartnerStatus());
            userInfoVO.setPartner(partner);
        }

        if (reportGoodUsersDAO.isAmazonUser(userId)) {
            userInfoVO.setAmazonUser(1);
        } else {
            userInfoVO.setAmazonUser(0);
        }

        // user_reminder_complaint
        userInfoVO.setReminderComplaint(userReminderComplaintDAO.getCountByUserId(userId) > 0);
        // first bind device
        AppUserBindLogEntity userBindLog = appUserBindLogDAO.getFirstBindTimeByUserId(userId);
        userInfoVO.setFirstBindDeviceTime(userBindLog == null ? null : userBindLog.getBindTime());
        // first bind bbt
        BBTInfoDTO bbtInfoDTO = bluetoothProvider.getBbtInfo(userId).getData();
        userInfoVO.setFirstBindBbtTime(bbtInfoDTO == null ? null : bbtInfoDTO.getFirstBindBbtTime());
        // no period
        userInfoVO.setNoPeriod(NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null ? 1 : 0);

        try {
            // clinic
            List<UserClinicDTO> userClinicDTOS = clinicProvider.listClinicInfos(loginUserInfoDTO.getEmail()).getData();
            if (CollectionUtils.isNotEmpty(userClinicDTOS)) {
                UserClinicDTO userClinicDTO = userClinicDTOS.get(0);
                UserInfoVO.Clinic clinic = new UserInfoVO.Clinic();
                BeanUtil.copyProperties(userClinicDTO, clinic);
                clinic.setBindTimeStr(ZoneDateUtil.format(loginUserInfoDTO.getTimeZone(), userClinicDTO.getBindTime(), DatePatternConst.DATE_TIME_PATTERN));
                userInfoVO.setClinic(clinic);

                for (UserClinicDTO userClinic2 : userClinicDTOS) {
                    UserInfoVO.Clinic clinic2 = new UserInfoVO.Clinic();
                    BeanUtil.copyProperties(userClinic2, clinic2);
                    clinic2.setBindTimeStr(ZoneDateUtil.format(loginUserInfoDTO.getTimeZone(), userClinic2.getBindTime(), DatePatternConst.DATE_TIME_PATTERN));
                    userInfoVO.getClinics().add(clinic2);
                }
            }
        } catch (Exception e) {
            log.error("an exception occurred to clinic server, user:{}", userId);
        }

        // special account type
        handleSpecialAccountType(loginUserInfoDTO, userInfoVO);

        return userInfoVO;
    }

    private void updateGoalOptions(LoginUserInfoDTO loginUserInfoDTO, UserInfoVO userInfoVO) {
        if (loginUserInfoDTO.getGoalStatus() != null
                && !userInfoVO.getGoalStatusOptions().contains(loginUserInfoDTO.getGoalStatus())) {
            userInfoVO.getGoalStatusOptions().add(loginUserInfoDTO.getGoalStatus());
        }
        if (loginUserInfoDTO.getGoalTrialFlag() != null
                && !userInfoVO.getGoalStatusOptions().contains(loginUserInfoDTO.getGoalTrialFlag())) {
            userInfoVO.getGoalStatusOptions().add(loginUserInfoDTO.getGoalTrialFlag());
        }
    }

    private void handleSpecialAccountType(LoginUserInfoDTO loginUserInfoDTO, UserInfoVO userInfoVO) {
        String extraInfo = loginUserInfoDTO.getExtraInfo();
        if (StringUtils.isNotBlank(extraInfo) && extraInfo.equals("pfizer")) {
            userInfoVO.setSpecialAccountType(1);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resetInitPassword(ResetInitPasswordDTO resetInitPasswordDTO) {
        String password = resetInitPasswordDTO.getPwd();
        String email = resetInitPasswordDTO.getEmail();

        AppUserEntity appUser = appUserDAO.getByEmail(email);
        if (Objects.isNull(appUser)) {
            throw new SsoException("No account found. Let's make sure your email is correct.");
        }
        if (appUser.getPasswordGrade() != 1) {
            throw new SsoException("Something went wrong! Your password wasn’t reset, please try again.");
        }

        appUser.setSalt(PasswordUtil.generateSalt(20));
        appUser.setPassword(PasswordUtil.encryptPassword(password, appUser.getSalt()));
        appUser.setPasswordGrade(10);
        UpdateEntityTimeUtil.updateBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), appUser);
        appUserDAO.updateById(appUser);
        cacheManager.deleteUserDetailCache(appUser.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editPassword(EditPasswordDTO editPasswordDTO) {
        String oldPassword = editPasswordDTO.getOldPwd();
        String newPassword = editPasswordDTO.getNewPwd();

        AppUserEntity appUser = appUserDAO.getById(ContextHolder.<BaseLoginInfo>getLoginInfo().getId());
        if (!PasswordUtil.match(oldPassword, appUser.getPassword(), appUser.getSalt())) {
            throw new SsoException("Old password isn't matching. Let's double-check it!");
        }

        appUser.setSalt(PasswordUtil.generateSalt(20));
        appUser.setPassword(PasswordUtil.encryptPassword(newPassword, appUser.getSalt()));
        appUser.setPasswordGrade(10);
        UpdateEntityTimeUtil.updateBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), appUser);
        appUserDAO.updateById(appUser);
        cacheManager.deleteUserDetailCache(appUser.getId());
    }

    @Override
    public void logout() {
        List<String> tokenHeaderList = UserTypeEnum.APP_USER.getTokenHeaderList();
        for (String tokenHeader : tokenHeaderList) {
            String authorization = RequestUtil.getRequest().getHeader(tokenHeader);
            if (StringUtils.isNotEmpty(authorization)) {
                authProvider.deleteToken(authorization, UserTypeEnum.APP_USER.getType());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long delete(UserDeleteDTO userDeleteDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        AppUserEntity appUser = appUserDAO.getById(loginInfo.getId());
        if (Objects.isNull(appUser)) {
            return loginInfo.getId();
        }
        appUser.setEmail(appUser.getEmail()
                + ZoneDateUtil.format(loginInfo.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN)
                + StringUtil.random(8));
        appUser.setModifier(loginInfo.getId());
        appUser.setDeleted(1);
        appUser.setDeleteType(userDeleteDTO.getType());
        appUserDAO.updateById(appUser);
        appUserDAO.removeById(appUser);
        cacheManager.deleteUserDetailCache(appUser.getId());
        // 删除info表
        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(loginInfo.getId());
        if (Objects.nonNull(appUserInfo)) {
            appUserInfoDAO.removeById(appUserInfo);
        }
        // 删除beta表
        userGoalTrialDAO.removeByUserId(appUser.getId());
        userProductTrialDAO.removeByUserId(appUser.getId());

        log.info("{} 删除用户，编号：{}", loginInfo.getUsername(), loginInfo.getId());
        logout();

        return loginInfo.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public WebsiteLoginVO websiteLogin(LoginInfoDTO loginInfoDTO) throws Exception {
        AppUserEntity appUser = appUserDAO.getByEmail(loginInfoDTO.getEmail());
        String currency = ContextHolder.get(HeaderConst.CURRENCY);
        // shopify是否存在
        Boolean existShopify = shopifyProvider.checkAccountExist(loginInfoDTO.getEmail(), currency).getData();
        if (appUser == null && !existShopify) { // app user存在，shopify不存在
            // 返回账户不存在,让用户去注册
            throw new SsoException(BizCodeEnum.USER_NOT_EXIST_IN_DB_AND_SHOPIFY);
        }
        if (appUser == null) { // app user不存在，shopify存在
            // 返回账户不存在,让用户以shopify账户去注册
            throw new SsoException(BizCodeEnum.USER_NOT_EXIST_IN_DB_BUT_EXIST_SHOPIFY);
        }

        // check
        checkStatus(appUser);

        // 正常模式用户
        if (UserConst.MODE_NORMAL == appUser.getTestMode()) {
            handleNormalUser(appUser, loginInfoDTO);
        }

        // check device id
        checkDeviceId(appUser, UserChannelConst.WEBSITE, loginInfoDTO.getDeviceId());

        // create shopify
        if (!existShopify) {
            createShopifyAccount(appUser, loginInfoDTO.getEmail(), loginInfoDTO.getPassword(), currency);
        }

        // 记录登录日志
        SpringContextHolder.publishEvent(new LoginLogEvent(new LoginLogDTO()
                .setLoginInfoDTO(loginInfoDTO)
                .setAppUser(appUser)
                .setSuccess(1)
                .setChannel(UserChannelConst.APP)));
        // Klaviyo
        klaviyoProducer.userLogin(appUser, loginInfoDTO.getOs());

        return getWebsiteToken(appUser, loginInfoDTO, currency);
    }

    private WebsiteLoginVO getWebsiteToken(AppUserEntity appUser, LoginInfoDTO loginInfoDTO, String currency) throws Exception {
        // 删除旧令牌
        String oldToken = cacheManager.getAppMarkToken(appUser.getId());
        if (StringUtils.isNotBlank(oldToken)) {
            authProvider.deleteToken(oldToken, UserTypeEnum.APP_USER.getType());
        }
        // 设置用户类别
        ContextHolder.put(HeaderConst.USER_TYPE, UserTypeEnum.APP_USER.getType());
        // 发放令牌
        CommonResult<AuthTokenDTO> tokenResult = authProvider.generalAccessToken(loginInfoDTO.getEmail(), loginInfoDTO.getPassword());
        String token = appUser.getId().toString().concat("-").concat(tokenResult.getData().getAccess_token());

        String multiPassUrl = ShopifyMultiPassUtil.getUrl(loginInfoDTO.getEmail(), currency);
        WebsiteLoginVO loginVO = new WebsiteLoginVO();
        loginVO.setMultiPassUrl(multiPassUrl);
        loginVO.setAccessToken(token);

        return loginVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public WebsiteLoginVO websiteCodeLogin(VerifyLoginDTO verifyLoginDTO) throws Exception {
        AppUserEntity appUser = appUserDAO.getByEmail(verifyLoginDTO.getEmail());
        String currency = ContextHolder.get(HeaderConst.CURRENCY);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        if (appUser == null) {
            throw new SsoException("The account doesn't exist.");
        }
        // shopify是否存在
        Boolean existShopify = shopifyProvider.checkAccountExist(verifyLoginDTO.getEmail(), currency).getData();

        // 校验verifyCode是否一致
        checkVerifyCode(appUser, verifyLoginDTO);

        // 其他校验
        handleNormalUser(appUser, verifyLoginDTO);

        // device save or update
        List<UserDeviceEntity> websiteDevices = userDeviceDAO.listByUserIdAndChannel(appUser.getId(), UserChannelConst.WEBSITE);
        if (websiteDevices == null || websiteDevices.size() < 3) {
            createDevice(appUser.getId(), verifyLoginDTO.getDeviceId(), UserChannelConst.WEBSITE, timeZone);
        } else {
            updateWebsiteDevice(verifyLoginDTO.getDeviceId(), timeZone, websiteDevices);
        }

        // create shopify
        if (!existShopify) {
            createShopifyAccount(appUser, verifyLoginDTO.getEmail(), verifyLoginDTO.getPassword(), currency);
        }

        // 记录登录日志
        SpringContextHolder.publishEvent(new LoginLogEvent(new LoginLogDTO()
                .setLoginInfoDTO(verifyLoginDTO)
                .setAppUser(appUser)
                .setSuccess(1)
                .setChannel(UserChannelConst.APP)));
        // Klaviyo
        klaviyoProducer.userLogin(appUser, verifyLoginDTO.getOs());

        return getWebsiteToken(appUser, verifyLoginDTO, currency);
    }

    private void checkVerifyCode(AppUserEntity appUser, VerifyLoginDTO verifyLoginDTO) {
        // 校验verifyCode是否一致
        String paramVerifyCode = verifyLoginDTO.getVerifyCode();
        String cacheVerifyCode = cacheManager.getDeviceVerifyCode(appUser.getId());
        if (StringUtils.isBlank(cacheVerifyCode)) {
            throw new SsoException("Verify code has expired.");
        }
        if (!paramVerifyCode.equals(cacheVerifyCode)) {
            throw new SsoException("Incorrect code. Please try again.");
        }
    }

    private void createShopifyAccount(AppUserEntity appUser, String email, String password, String currency) {
        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(appUser.getId());
        UserRegisterDTO userRegisterDTO = new UserRegisterDTO();
        userRegisterDTO.setEmail(email);
        userRegisterDTO.setFirstName(appUserInfo.getFirstName());
        userRegisterDTO.setLastName(appUserInfo.getLastName());
        userRegisterDTO.setPassword(password);

        CustomerRegisterDTO customerRegisterDTO = BeanUtil.toBean(userRegisterDTO, CustomerRegisterDTO.class);
        customerRegisterDTO.setCurrency(currency);
        shopifyProvider.createAccount(customerRegisterDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void createDevice(Long userId, String deviceId, Integer channel, String timeZone) {
        UserDeviceEntity userDeviceEntity = new UserDeviceEntity();
        userDeviceEntity.setUserId(userId);
        userDeviceEntity.setDeviceId(deviceId);
        userDeviceEntity.setChannel(channel);
        userDeviceEntity.setTimeZone(timeZone);
        long localTime = System.currentTimeMillis();
        String localtimeStr = ZoneDateUtil.format(timeZone, localTime, DatePatternConst.DATE_TIME_PATTERN);
        userDeviceEntity.setCreateTime(localTime);
        userDeviceEntity.setCreateTimeStr(localtimeStr);
        userDeviceDAO.save(userDeviceEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAppDevice(String deviceId, String timeZone, UserDeviceEntity userDeviceEntity) {
        userDeviceEntity.setDeviceId(deviceId);
        userDeviceEntity.setTimeZone(timeZone);
        long localTime = System.currentTimeMillis();
        String localtimeStr = ZoneDateUtil.format(timeZone, localTime, DatePatternConst.DATE_TIME_PATTERN);
        userDeviceEntity.setCreateTime(localTime);
        userDeviceEntity.setCreateTimeStr(localtimeStr);
        userDeviceDAO.updateById(userDeviceEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateWebsiteDevice(String deviceId, String timeZone, List<UserDeviceEntity> websiteDevices) {
        UserDeviceEntity userDeviceEntity = websiteDevices.get(websiteDevices.size() - 1);
        userDeviceEntity.setDeviceId(deviceId);
        userDeviceEntity.setTimeZone(timeZone);
        long localTime = System.currentTimeMillis();
        String localtimeStr = ZoneDateUtil.format(timeZone, localTime, DatePatternConst.DATE_TIME_PATTERN);
        userDeviceEntity.setCreateTime(localTime);
        userDeviceEntity.setCreateTimeStr(localtimeStr);
        userDeviceDAO.updateById(userDeviceEntity);
    }
}
