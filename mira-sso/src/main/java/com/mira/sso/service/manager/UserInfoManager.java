package com.mira.sso.service.manager;

import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.DeviceUtil;
import com.mira.redis.cache.RedisComponent;
import com.mira.sso.dal.dao.AppBlackSnDAO;
import com.mira.sso.dal.dao.AppUserInfoDAO;
import com.mira.sso.dal.entity.AppBlackSnEntity;
import com.mira.sso.exception.SsoException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 用户信息 manager
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
public class UserInfoManager {
    private final AppUserInfoDAO appUserInfoDAO;
    private final AppBlackSnDAO appBlackSnDAO;
    private final RedisComponent redisComponent;

    public LoginUserInfoDTO loginUserInfo(Long userId) {
        // query cache
        String cacheKey = RedisCacheKeyConst.USER_LOGIN_INFO + userId;
        LoginUserInfoDTO loginUserInfoCache = redisComponent.get(cacheKey, LoginUserInfoDTO.class);
        if (loginUserInfoCache != null && loginUserInfoCache.getFresh() == 1) {
            return loginUserInfoCache;
        }
        // query db
        LoginUserInfoDTO loginUserInfoDTO = appUserInfoDAO.getBaseMapper().getLoginUser(userId);
        if (loginUserInfoDTO == null) {
            throw new SsoException("The account doesn't exist or has been deleted.");
        }
        Integer goalTrialFlag = loginUserInfoDTO.getGoalTrialFlag();
        if (UserGoalEnum.TTA.getValue().equals(goalTrialFlag)) {
            loginUserInfoDTO.setTtaSwitch(1);
        } else {
            loginUserInfoDTO.setTtaSwitch(0);
        }
        String bindDevice = loginUserInfoDTO.getBindDevice();
        if (StringUtils.isNotBlank(bindDevice)) {
            AppBlackSnEntity appBlackSn = appBlackSnDAO.getEnableOne(DeviceUtil.sn(bindDevice));
            loginUserInfoDTO.setBlackSn(Objects.nonNull(appBlackSn));
        }
        // save cache
        redisComponent.setEx(cacheKey, loginUserInfoDTO, 12, TimeUnit.HOURS);

        return loginUserInfoDTO;
    }

    public LoginUserInfoDTO loginUserInfo() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        return loginUserInfo(userId);
    }
}
