<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.sso.dal.mapper.AppUserInfoMapper">
    <select id="getLoginUser" resultType="com.mira.api.sso.dto.LoginUserInfoDTO">
        select i.user_id,
               i.nickname,
               i.first_name,
               i.last_name,
               i.avatar,
               i.birth_year,
               i.birth_month,
               i.birth_of_day         as 'birth_day',
               i.goal_status,
--                i.is_oft,
               i.conditions,
               i.hormonal_birth_control,
               i.bind_version,
               i.bind_device,
               i.push_token,
               i.platform,
               i.tracking_menopause,
               i.tracking_menopause_date,
               r.remind_flag,
               r.remind_time,
               r.testing_schedule_flag,
               p.avg_len_cycle,
               p.avg_len_period,
               p.period_flag,
               p.cycle_flag,
               p.switch_flag          as 'period_flag_switch',
               IFNULL(p.fresh, 0)     as 'fresh',
               p.period_data,
               upt.flag               as 'product_trial_flag',
               ugt.flag               as 'goal_trial_flag',
               IF(g.id is null, 0, 1) as 'good_user',
               u.time_zone,
               u.source,
               u.email,
               u.extra_info,
               u.transfer_flag,
               u.country_code,
               u.continent_code,
               u.current_currency,
               u.create_time_str      as 'registerTime',
               pt.email               as 'partner_email',
               pt.status              as 'partner_status',
               pt.deleted             as 'partner_deleted'
        from app_user_info i
                 LEFT JOIN app_user u on i.user_id = u.id
                 LEFT JOIN app_user_period p on i.user_id = p.user_id
                 LEFT JOIN user_product_trial upt on u.email = upt.email
                 LEFT JOIN user_goal_trial ugt on u.email = ugt.email
                 LEFT JOIN report_good_users g on i.user_id = g.user_id
                 LEFT JOIN app_user_reminder r on r.user_id = u.id
                 LEFT JOIN app_user_partner pt on pt.user_id = u.id
        where i.user_id = #{userId}
        and u.deleted = 0
    </select>
</mapper>
