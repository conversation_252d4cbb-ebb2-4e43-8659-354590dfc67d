package com.mira.third.party.client.klaviyo;

import cn.hutool.core.bean.BeanUtil;
import com.google.gson.Gson;
import com.mira.api.thirdparty.dto.klaviyo.KlaviyoProfileDTO;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.third.party.dto.klaviyo.KlaviyoListDTO;
import com.mira.third.party.enums.KlaviyoApiKeyEnum;
import com.mira.third.party.enums.KlaviyoListIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * klaviyo api
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class KlaviyoAPI extends AbstractKlaviyoAPI {
    @Resource
    private KlaviyoClient klaviyoClient;

    /**
     * Get Profile ID
     *
     * @param appUserDTO app user info
     * @param revision   version
     * @param apiKey     api key
     * @return Person ID
     */
    public String getProfileID(AppUserDTO appUserDTO, String revision, KlaviyoApiKeyEnum apiKey) {
        String filter = "equals(email,\"" + appUserDTO.getEmail() + "\")";

        KlaviyoListDTO klaviyoListDTO = klaviyoClient.getProfile("Klaviyo-API-Key " + apiKey.getValue(), revision, filter);
        if (CollectionUtils.isEmpty(klaviyoListDTO.getData())) {
            return null;
        }

        return klaviyoListDTO.getData().get(0).getId();
    }

    /**
     * Create Profile
     *
     * @param appUserDTO 用户信息
     * @param revision   接口版本号
     * @param paramMap   请求参数
     */
    public KlaviyoProfileDTO createProfile(AppUserDTO appUserDTO, String revision, Map<String, Object> paramMap) {
        KlaviyoApiKeyEnum apiKey = getApiKey();
        try {
            String profileID = getProfileID(appUserDTO, revision, apiKey);
            if (profileID == null) {
                return klaviyoClient.createProfile("Klaviyo-API-Key " + apiKey.getValue(), revision, paramMap);
            }
        } catch (Exception e) {
            log.error("create profile error", e);
        }
        return null;
    }

    /**
     * Update Profile
     *
     * @param appUserDTO 用户信息
     * @param revision   接口版本号
     * @param params     请求参数
     */
    public Object updateProfile(AppUserDTO appUserDTO, String revision, String params) {
        KlaviyoApiKeyEnum apiKey = getApiKey();
        try {
            String profileID = getProfileID(appUserDTO, revision, apiKey);
            if (profileID == null) {
                return null;
            }

            // 转成bean，并去掉值为null的字段
            KlaviyoProfileDTO klaviyoProfileDTO = new Gson().fromJson(params, KlaviyoProfileDTO.class);
            klaviyoProfileDTO.getData().setId(profileID);
            String newJson = new Gson().toJson(klaviyoProfileDTO);
            // convert map
            Map<String, Object> paramsMap = new Gson().fromJson(newJson, Map.class);
            return klaviyoClient.updateProfile("Klaviyo-API-Key " + apiKey.getValue(), revision,
                    profileID, paramsMap);
        } catch (Exception e) {
            log.error("update profile error", e);
        }
        return null;
    }

    /**
     * Add Profile to List
     *
     * @param appUserDTO 用户信息
     * @param revision   接口版本号
     */
    public Object addProfileToList(AppUserDTO appUserDTO, String revision) {
        KlaviyoApiKeyEnum apiKey = getApiKey();
        KlaviyoListDTO klaviyoListDTO = new KlaviyoListDTO(getProfileID(appUserDTO, revision, apiKey));
        Map<String, Object> paramMap = BeanUtil.beanToMap(klaviyoListDTO);
        try {
            return klaviyoClient.addProfileToList("Klaviyo-API-Key " + apiKey.getValue(), revision,
                    KlaviyoListIdEnum.MiraApp_List_US.getValue(), paramMap);
        } catch (Exception e) {
            log.error("add profile to list error", e);
        }
        return null;
    }

    /**
     * Create Metric Event
     *
     * @param revision 接口版本号
     * @param paramMap 请求参数
     */
    public Void createEvent(String revision, Map<String, Object> paramMap) {
        KlaviyoApiKeyEnum apiKey = getApiKey();
        try {
            return klaviyoClient.createEvent("Klaviyo-API-Key " + apiKey.getValue(), revision, paramMap);
        } catch (Exception e) {
            log.error("create event error", e);
        }
        return null;
    }
}
