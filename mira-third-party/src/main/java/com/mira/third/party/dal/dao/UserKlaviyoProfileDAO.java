package com.mira.third.party.dal.dao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.third.party.dal.entity.UserKlaviyoProfileEntity;
import com.mira.third.party.dal.mapper.UserKlaviyoProfileMapper;
import org.springframework.stereotype.Repository;

@Repository
public class User<PERSON>laviyoProfileDAO extends ServiceImpl<UserKlaviyoProfileMapper, UserKlaviyoProfileEntity> {
    public UserKlaviyoProfileEntity getByUserId(Long userId) {
        return lambdaQuery().eq(UserKlaviyoProfileEntity::getUserId, userId).one();
    }

    public UserKlaviyoProfileEntity getByEmail(String email) {
        return lambdaQuery().eq(UserKlaviyoProfileEntity::getEmail, email).one();
    }
}
