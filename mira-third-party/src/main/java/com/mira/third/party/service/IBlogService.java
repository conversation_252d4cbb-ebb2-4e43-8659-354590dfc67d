package com.mira.third.party.service;

import com.mira.api.thirdparty.dto.blog.Ip2CountryDTO;

import java.util.List;

/**
 * Blog service
 *
 * <AUTHOR>
 */
public interface IBlogService {
    /**
     * 根据 ip 获取国家编号
     *
     * @param ip     IP
     * @param userId user id
     * @return 国家编号
     */
    Ip2CountryDTO getCountryByIp(String ip, Long userId);

    /**
     * 根据博客id列表查询博客
     *
     * @param blogIdList 博客id列表
     * @return 博客列表
     */
    Object getBlogInfoList(List<Long> blogIdList);

    /**
     * 获取特色博文列表
     *
     * @return 特色博文列表
     */
    Object getFeaturedBlogList();

    /**
     * 根据关键字查询博文
     *
     * @param keyword 关键字
     * @param page    页码
     * @return 博文
     */
    Object searchBlog(String keyword, Integer page);

    /**
     * 获取Event页面列表
     *
     * @return Event页面列表
     */
    Object getTogether();

    /**
     * 获取博文详情
     *
     * @param blogId 博文id
     * @return 详情
     */
    Object getBlogDetail(Long blogId);

    /**
     * 特殊分类博文列表
     *
     * @param term story; fertility; course
     * @param page 页码
     * @return 博文列表
     */
    Object getTermBlogList(String term, Integer page);

    /**
     * 指定博文列表
     *
     * @param type news, blog
     * @param page 页码
     * @return 博文列表
     */
    Object getTypeBlogList(String type, Integer page);

    /**
     * 产品列表
     *
     * @param name     产品名称
     * @param currency USD, GBR, AUD, EUR, CAD
     * @return 列表
     */
    Object product(String name, String currency);

    /**
     * 课程列表
     *
     * @return 列表
     */
    Object course();

    /**
     * 课程详情
     *
     * @param chapterId id
     * @return 详情
     */
    Object chapter(Long chapterId);

    /**
     * 获取所有webinar
     *
     * @return
     */
    Object webinarAll();
}
