package com.mira.third.party.service;

import com.mira.api.thirdparty.dto.shopify.CustomerRegisterDTO;
import com.mira.api.thirdparty.dto.shopify.CustomerResponse;

import java.util.Map;

/**
 * shopify姐扩
 *
 * <AUTHOR>
 */
public interface IShopifyService {
    /**
     * 是否存在shopify账号
     *
     * @param email    邮箱
     * @param currency 币种
     * @return Boolean
     */
    Boolean checkAccountExist(String email, String currency);

    /**
     * 创建账号
     *
     * @param customerRegisterDTO 注册信息
     * @return CustomerResponse
     */
    CustomerResponse createAccount(CustomerRegisterDTO customerRegisterDTO);

    /**
     * 查询顾客的默认地址
     *
     * @param email    邮箱
     * @param currency 币种
     * @return Map<String, Object>
     */
    Map<String, Object> defaultAddress(String email, String currency);

    /**
     * 修改顾客的邮箱
     *
     * @param email    邮箱
     * @param currency 币种
     */
    void updateCustomerEmail(String email, String currency);

    /**
     * 修改顾客的密码
     *
     * @param email    邮箱
     * @param password 密码
     * @param currency 币种
     */
    void updateCustomerPassword(String email, String password, String currency);
}
