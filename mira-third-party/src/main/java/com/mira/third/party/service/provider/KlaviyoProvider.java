package com.mira.third.party.service.provider;

import com.google.gson.Gson;
import com.mira.api.thirdparty.provider.IKlaviyoProvider;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.core.response.CommonResult;
import com.mira.core.util.JsonUtil;
import com.mira.third.party.client.klaviyo.KlaviyoAPI;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Klaviyo 接口实现
 *
 * <AUTHOR>
 */
@RestController
public class KlaviyoProvider implements IKlaviyoProvider {
    @Resource
    private KlaviyoAPI klaviyoAPI;

    /**
     * Klaviyo API 版本号
     */
    private final static String REVISON = "2025-01-15";
    private final static String METRIC_EVENT_REVISON = "2025-01-15";

    @Override
    public CommonResult<String> appEvent(Map<String, Object> receiveMap) {
        String flag = (String) receiveMap.get("flag");
        AppUserDTO appUserDTO = JsonUtil.toObject((String) receiveMap.get("user"), AppUserDTO.class);
        String params = (String) receiveMap.get("params");

        if ("createProfile".equals(flag)) {
            Map<String, Object> paramMap = new Gson().fromJson((params), Map.class);
            klaviyoAPI.createProfile(appUserDTO, REVISON, paramMap);
            return CommonResult.OK();
        }

        if ("updateProfile".equals(flag)) {
            klaviyoAPI.updateProfile(appUserDTO, REVISON, params);
            return CommonResult.OK();
        }

        if ("addProfileToList".equals(flag)) {
            klaviyoAPI.addProfileToList(appUserDTO, REVISON);
            return CommonResult.OK();
        }

        if ("metricEvent".equals(flag)) {
            Map<String, Object> paramMap = new Gson().fromJson((params), Map.class);
            klaviyoAPI.createEvent(METRIC_EVENT_REVISON, paramMap);
            return CommonResult.OK();
        }

        return CommonResult.OK();
    }
}
