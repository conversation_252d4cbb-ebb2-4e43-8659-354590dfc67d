package com.mira.third.party.service.provider;

import com.mira.api.thirdparty.dto.xperiencify.XperiencifyDTO;
import com.mira.api.thirdparty.provider.IXperiencifyProvicer;
import com.mira.core.response.CommonResult;
import com.mira.third.party.client.xperiencify.XperiencifyClient;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * xperiencify api 实现
 *
 * <AUTHOR>
 */
@RestController
public class XperiencifyProvider implements IXperiencifyProvicer {
    @Resource
    private XperiencifyClient xperiencifyClient;

    @Override
    public CommonResult<Object> getToken(XperiencifyDTO xperiencifyDTO) {
        return CommonResult.OK(xperiencifyClient.getToken(xperiencifyDTO));
    }

    @Override
    public CommonResult<Object> getStudentInfo(String student_id, String authorization, String site_id) {
        Object result = xperiencifyClient.getStudent("Bearer " + authorization, student_id, site_id);
        return CommonResult.OK(result);
    }

    @Override
    public CommonResult<Object> getAllStudent(String authorization, String site_id) {
        Object result = xperiencifyClient.getAllStudent("Bearer " + authorization, site_id);
        return CommonResult.OK(result);
    }

    @Override
    public CommonResult<Object> getLoginInfo(String authorization) {
        Object result = xperiencifyClient.getLoginInfo("Bearer " + authorization);
        return CommonResult.OK(result);
    }
}
