spring:
  application:
    name: mira-third-party
  profiles:
    active: dev
  mvc:
    pathmatch:
      # spring boot 2.6 以上兼容 swagger3
      matching-strategy: ant_path_matcher
  cloud:
    loadbalancer:
      nacos:
        enabled: true
  sleuth:
    traceId128: true
    sampler:
      probability: 0.1

retrofit:
  global-retry:
    # 是否启用全局重试
    enable: true
    # 全局重试间隔时间
    interval-ms: 100
    # 全局最大重试次数
    max-retries: 2

feign:
  client:
    config:
      default:
        connectTimeout: 30000
        readTimeout: 60000
  httpclient:
    enabled: false
  okhttp:
    enabled: true

server:
  port: 8083
  shutdown: graceful