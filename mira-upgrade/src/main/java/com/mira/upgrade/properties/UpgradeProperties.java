package com.mira.upgrade.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * upgrade properties
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@RefreshScope
public class UpgradeProperties {
    /**
     * 指定升级开关
     */
    @Value("${firmware.upgrade-switch:0}")
    private int firmwareUpgradeSwitch;
}
