package com.mira.upgrade.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("APP版本")
public class AppVersionDTO {
    @ApiModelProperty("手机系统")
    private String os;

    @ApiModelProperty("app版本")
    private String appVersion;

    @ApiModelProperty("升级方式")
    private Integer upgrade;

    @ApiModelProperty("更新文案")
    private String content;
}
