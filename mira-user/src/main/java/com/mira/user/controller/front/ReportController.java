package com.mira.user.controller.front;

import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.user.enums.report.CycleSelectTypeEnum;
import com.mira.user.controller.vo.report.ReportVO;
import com.mira.user.service.front.IReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Report
 *
 * <AUTHOR>
 */
@Api(tags = "15.Report")
@RestController
@RequestMapping("/app/v4/report")
public class ReportController {
    @Resource
    private IReportService reportService;

    @ApiOperation("获取report的周期可选项")
    @GetMapping("/select-type")
    public List<CycleSelectTypeEnum.Result> selectType(HttpServletRequest request) {
        //区分请求是从H5过来还是Mira Desk过来的
        //"mira-access-token" or  "mira-check-token"
        String fromMiraCheckToken = request.getHeader("mira-check-token");
        String fromClinicCheckToken = request.getHeader("mira-clinic-check-token");
        String requestType = "user";
        if (StringUtils.isNotEmpty(fromMiraCheckToken) || StringUtils.isNotEmpty(fromClinicCheckToken)) {
            requestType = "mira-check";
        }
        return reportService.selectType(requestType);
    }

    @ApiOperation("result")
    @GetMapping("/result")
    public ReportVO report(@RequestParam(name = "selectType") CycleSelectTypeEnum cycleSelectTypeEnum) {
        return reportService.report(cycleSelectTypeEnum);
    }
}
