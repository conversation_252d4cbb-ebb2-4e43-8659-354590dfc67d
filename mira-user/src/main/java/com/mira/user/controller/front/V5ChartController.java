package com.mira.user.controller.front;

import com.mira.user.controller.vo.chart.V5ChartLineVO;
import com.mira.user.controller.vo.chart.aggregation.ChartAggregationVO;
import com.mira.user.service.front.IChartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * chart page controller
 *
 * <AUTHOR>
 */
@Api(tags = "11.图表页V5")
@RestController
@RequestMapping("/app/v5/chart")
public class V5ChartController {
    @Resource
    private IChartService chartService;

    @ApiOperation("图表曲线图")
    @GetMapping("/analysis-line")
    public V5ChartLineVO analysisLine(@RequestParam(value = "index", required = false) Integer index) {
        // 未传参数，默认取当前周期；指定index，取指定周期；index=-1，取全部周期
        if (index == null) {
            index = -2;
        }
        return chartService.analysisLine(index);
    }

    @ApiOperation("图表聚合内容")
    @GetMapping("/aggregation")
    public ChartAggregationVO aggregation(@RequestParam(value = "date", required = false) String date) {
        return chartService.aggregation(date);
    }

    @ApiOperation("获取周期Index")
    @GetMapping("/cycle/index")
    public int cycleIndex(@RequestParam("date") String date) {
        return chartService.cycleIndex(date);
    }
}
