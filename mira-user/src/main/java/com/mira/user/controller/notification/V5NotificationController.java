package com.mira.user.controller.notification;

import com.mira.core.annotation.Idempotent;
import com.mira.mybatis.response.PageResult;
import com.mira.user.controller.vo.user.NotificationRecordVO;
import com.mira.user.controller.vo.user.NotificationTypeVO;
import com.mira.user.dto.notification.NotificationDeleteDTO;
import com.mira.user.dto.notification.NotificationPageQueryDTO;
import com.mira.user.dto.notification.NotificationRateDTO;
import com.mira.user.service.notification.v5.IV5NotificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * notification controller
 *
 * <AUTHOR>
 */
@Api(tags = "08.系统通知记录v5")
@RestController
@RequestMapping("/app/v5")
public class V5NotificationController {
    @Resource
    private IV5NotificationService notificationService;

    @ApiOperation("获取类型列表")
    @GetMapping("/notification-record/aggregate/list")
    public List<NotificationTypeVO> aggregateTypeList() {
        return notificationService.aggregateTypeList();
    }

    @ApiOperation("系统通知记录列表")
    @PostMapping("/notification-record/page")
    public PageResult<NotificationRecordVO> page(@RequestBody NotificationPageQueryDTO notificationPageQueryDTO) {
        return notificationService.page(notificationPageQueryDTO);
    }

    @ApiOperation("静默通知最新一条")
    @GetMapping("/notification-record/silent")
    public NotificationRecordVO silent() {
        return notificationService.silent();
    }

    @Idempotent(expire = 1L)
    @ApiOperation("消息更新已读")
    @PostMapping("/notification-record/update/read/{id}")
    public void updateRead(@PathVariable("id") Long id) {
        notificationService.updateRead(id);
    }

    @Idempotent(expire = 1L)
    @ApiOperation("消息更新未读")
    @PostMapping("/notification-record/update/un-read/{id}")
    public void updateUnRead(@PathVariable("id") Long id) {
        notificationService.updateUnRead(id);
    }

    @Idempotent(expire = 1L, returnValue = "user.NotificationRecordVO")
    @ApiOperation("删除消息")
    @PostMapping("/notification-record/delete")
    public NotificationRecordVO delete(@RequestBody NotificationDeleteDTO notificationDeleteDTO) {
        return notificationService.delete(notificationDeleteDTO);
    }

    @Idempotent
    @ApiOperation("记录用户PayWall点击")
    @PostMapping("/notification-record/paywall")
    public void paywall() {
        notificationService.paywall();
    }

    @Idempotent
    @ApiOperation("用户从通知进入无经期模式")
    @PostMapping("/notification/enter-no-period-mode")
    public void enterNoPeriodMode() {
        notificationService.enterNoPeriodMode();
    }

    @Idempotent
    @ApiOperation("通知调研")
    @PostMapping("/notification/rate")
    public void rate(@RequestBody NotificationRateDTO notificationRateDTO) {
        notificationService.rate(notificationRateDTO);
    }
}
