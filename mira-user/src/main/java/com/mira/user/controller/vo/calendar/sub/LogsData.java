package com.mira.user.controller.vo.calendar.sub;

import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.diary.UserMedicineDTO;
import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import com.mira.user.controller.vo.diary.UserDiaryMoodsVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ApiModel("logsData")
public class LogsData {
    @ApiModelProperty("备注")
    private String notes;

    @ApiModelProperty("Sex")
    private String sex;

    @ApiModelProperty("温度")
    private List<TemperatureDTO> temperatures = new ArrayList<>();

    @ApiModelProperty("温度默认单位，默认值与国际化对应")
    private String tempUnit;

    @ApiModelProperty("体重")
    private BigDecimal weightValue;

    @ApiModelProperty("体重默认单位，默认值与国际化对应")
    private String weightUnit;

    @ApiModelProperty("身体状态列表")
    List<UserSymptomDTO> symptoms = new ArrayList<>();

    @ApiModelProperty("Mood & Well Being")
    UserDiaryMoodsVO appUserDiaryMoodsVO;

    @ApiModelProperty("Cervical Mucus -- 白带状态(粘液状态)，参考DailyStatusMucusTypeEnum")
    private String mucusType;

    @ApiModelProperty("Cervical Mucus -- 白带流量，参考DailyStatusMucusFlowEnum")
    private String mucusFlow;

    @ApiModelProperty("验孕记录，参考DailyStatusPregnantEnum")
    private Boolean pregnant;

    @ApiModelProperty("排卵测试记录，参考DailyStatusOPKEnum")
    private Boolean opk;

    @ApiModelProperty("出血状态，参考DailyStatusFlowAndSpottingEnum")
    private String flowAndSpotting;

    @ApiModelProperty("子宫位置，参考DailyCervicalPosition")
    private String cervicalPosition;

    @ApiModelProperty("Firmness，参考DailyCervicalTexture")
    private String cervicalFirmness;

    @ApiModelProperty("Openness，参考DailyCervicalOpenness")
    private String cervicalOpenness;

    @ApiModelProperty("药物")
    private List<UserMedicineDTO> medications = new ArrayList<>();

    @ApiModelProperty("Spotting，参考DailyStatusSpottingEnum")
    private String spotting;

    @ApiModelProperty("Period Other，参考DailyStatusPeriodOtherEnum")
    private String periodOther;

    @ApiModelProperty("Challenge with glucose control")
    private String glucoseControl;
}
