package com.mira.user.controller.vo.chart.aggregation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("Cycle Analysis")
public class CycleAnalysis {
    @ApiModelProperty("Phase")
    private Phase phase;

    @ApiModelProperty("Cycle day")
    private String cycleDay;

    @ApiModelProperty("Conception chances")
    private String fertilityScore;

    @ApiModelProperty("Fertile window")
    private String fertileWindow;

    @ApiModelProperty("Ovulation")
    private Ovulation ovulation;

    @ApiModelProperty("No Period，0-否，1-是")
    private Integer noPeriod = 0;

    @Getter
    @Setter
    @ApiModel("Phase")
    public static class Phase {
        @ApiModelProperty("1.Period - predicated, 2.Period - comfirmed, 3.Follicular - predicated, " +
                "4.Follicular - comfirmed, 5.Fertile window - predicated, 6.Fertile window - comfirmed, " +
                "7.Ovulation - predicated, 8.Ovulation - detected, 9.Ovulation - predicted & confirmed, 10.Ovulation - detected & confirmed, " +
                "11.Luteal - predicated, 12.Luteal - comfirmed")
        private Integer code;

        @ApiModelProperty("Value")
        private String value;
    }

    @Getter
    @Setter
    @ApiModel("Ovulation")
    public static class Ovulation {
        @ApiModelProperty("1.predicted, 2.detected, 13.predicted & confirmed, 23.detected & confirmed")
        private Integer code;

        @ApiModelProperty("Value")
        private String value;
    }
}
