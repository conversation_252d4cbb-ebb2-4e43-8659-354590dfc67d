package com.mira.user.controller.vo.cycle;

import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import com.mira.user.controller.vo.chart.PregnantRiskVO;
import com.mira.user.dto.chart.TemperatureChartDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@ApiModel("周期中每日数据")
public class CycleDataVO {
    @ApiModelProperty("主键")
    private Integer cycleIndex;

    @ApiModelProperty("周期状态，参考CycleStatusEnum")
    private Integer cycleStatus;

    @ApiModelProperty("周期长度")
    private Integer lenCycle;

    @ApiModelProperty("周期开始日")
    private String datePeriodStart;

    @ApiModelProperty("经期结束日（经期不包含这天")
    private String datePeriodEnd;

    @ApiModelProperty("易孕期开始日")
    private String dateFwStart;

    @ApiModelProperty("易孕期结束日 （易孕期不包含这一天）")
    private String dateFwEnd;

    @ApiModelProperty("排卵日 （预测 or LH 峰值日")
    private String dateOvulation;

    @ApiModelProperty("预留，可为 null （实际测量的最高值时间）")
    private String dateLhSurge;

    @ApiModelProperty("float, 实际峰值日 中 LH的对应最大值，可为 null")
    private Float valueLhSurge;

    @ApiModelProperty("float, LH 阈值,可为 null")
    private Float thresholdLh;

    @ApiModelProperty("float, E3G 阈值,可为 null")
    private Float thresholdE3g;

    @ApiModelProperty("float, pdG 动态均值,可为 null")
    private Float pdgDynamic;

    @ApiModelProperty("周期cd状态（当前chart页面仅在怀孕周期8和异常怀孕周期9返回）")
    private List<Map<String,String>> weeks;

    @ApiModelProperty("怀孕风险预测")
    private PregnantRiskVO pregnantRisk;

    @ApiModelProperty("ovulation, OvulationTypeEnum")
    private Integer ovulationType;

    @ApiModelProperty("lh测试数据")
    private List<TestDataVO> lhDatas = new ArrayList<>();

    @ApiModelProperty("e3g测试数据")
    private List<TestDataVO> e3gDatas = new ArrayList<>();

    @ApiModelProperty("hcg测试数据")
    private List<TestDataVO> hcgDatas = new ArrayList<>();

    @ApiModelProperty("pdg测试数据")
    private List<TestDataVO> pdgDatas = new ArrayList<>();

    @ApiModelProperty("hcg2测试数据")
    private List<TestDataVO> hcg2Datas = new ArrayList<>();

    @ApiModelProperty("fsh测试数据")
    private List<TestDataVO> fshDatas = new ArrayList<>();

    @ApiModelProperty("温度测试数据")
    private List<TemperatureChartDTO> temperatureDatas = new ArrayList<>();

    @ApiModelProperty("推荐的测试试剂和已测的试剂的数量")
    private List<DayTestWandCount> dayTestWandCounts = new ArrayList<>();
}
