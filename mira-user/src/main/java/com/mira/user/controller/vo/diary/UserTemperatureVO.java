package com.mira.user.controller.vo.diary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("用户温度")
public class UserTemperatureVO {
    @ApiModelProperty("温度数据ID")
    private Long id;

    @ApiModelProperty("温度单位：c、f")
    private String tempUnit;

    @ApiModelProperty("温度数值")
    private Float value;

    @ApiModelProperty("温度测试时间")
    private String testTime;

    @ApiModelProperty("温度异常码，默认00，T01,T02,T03,T04分别表示量测温度过高；量测温度过低；电池低电压；ERR错误")
    private String Ecode;

    @ApiModelProperty("默认0表示手动添加的温度，1表示GENIAL厂商的温度计")
    private Integer type;
}
