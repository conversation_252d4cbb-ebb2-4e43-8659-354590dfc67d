package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.api.user.dto.backend.BlackSnPageDTO;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.dal.entity.AppBlackSnEntity;
import com.mira.user.dal.mapper.AppBlackSnMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

/**
 * app_black_sn DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppBlackSnDAO extends ServiceImpl<AppBlackSnMapper, AppBlackSnEntity> {
    public AppBlackSnEntity getEnableOne(String sn) {
        return getOne(Wrappers.<AppBlackSnEntity>lambdaQuery()
                              .eq(AppBlackSnEntity::getSn, sn)
                              .eq(AppBlackSnEntity::getEnable, 1).last("limit 1"));
    }

    public AppBlackSnEntity getBySn(String sn) {
        QueryWrapper<AppBlackSnEntity> infoWrapper = new QueryWrapper<>();
        infoWrapper.eq("sn", sn);
        AppBlackSnEntity appBlackSnEntity = this.getOne(infoWrapper);
        return appBlackSnEntity;
    }

    public boolean checkExist(String sn) {
        QueryWrapper<AppBlackSnEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", sn);
        AppBlackSnEntity appBlackSnEntity = this.getOne(queryWrapper);
        if (ObjectUtils.isEmpty(appBlackSnEntity)) {
            return false;
        }
        return true;
    }


    /**
     * 创建sn黑名单
     */
    public Long saveBlackSn(Long adminId, String sn, Integer needReturn, String description, String email, Long userId) {
        String timeZone = "Asia/Shanghai";
        AppBlackSnEntity appBlackSnEntity = new AppBlackSnEntity();
        appBlackSnEntity.setSn(sn);
        appBlackSnEntity.setDescription(description);
        appBlackSnEntity.setEmail(email);
        appBlackSnEntity.setUserId(userId);
        appBlackSnEntity.setEnable(0);
        appBlackSnEntity.setNeedReturn(needReturn);
        appBlackSnEntity.setCreator(adminId);
        appBlackSnEntity.setModifier(adminId);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appBlackSnEntity);
        this.save(appBlackSnEntity);
        return appBlackSnEntity.getId();
    }

    /**
     * 将黑名单激活或者置为未激活
     *
     * @param sn
     * @param enable
     * @param adminId
     */
    public void enableBlackSn(String sn, Integer enable, Long adminId) {
        String timeZone = "Asia/Shanghai";
        AppBlackSnEntity blackSnEntity = this.getBySn(sn);
        blackSnEntity.setEnable(enable);
        blackSnEntity.setCreator(adminId);
        blackSnEntity.setModifier(adminId);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, blackSnEntity);
        this.updateById(blackSnEntity);
    }

    public Page<AppBlackSnEntity> page(BlackSnPageDTO blackSnPageDTO) {
        QueryWrapper<AppBlackSnEntity> queryWrapper = new QueryWrapper<AppBlackSnEntity>();
        if (StringUtils.isNotBlank(blackSnPageDTO.getSn())) {
            queryWrapper.like("sn", blackSnPageDTO.getSn());
        }
        if (StringUtils.isNotBlank(blackSnPageDTO.getStartTime())) {
            queryWrapper.ge("create_time_str", blackSnPageDTO.getStartTime());
        }
        if (StringUtils.isNotBlank(blackSnPageDTO.getEndTime())) {
            queryWrapper.le("create_time_str", blackSnPageDTO.getEndTime());
        }
        queryWrapper.orderByDesc("modify_time");
        Page<AppBlackSnEntity> page = (Page<AppBlackSnEntity>) page(new Page<>(blackSnPageDTO.getCurrent(), blackSnPageDTO.getSize()), queryWrapper);
        return page;
    }
}
