package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppOvulationManualEntity;
import com.mira.user.dal.mapper.AppOvulationManualMapper;
import org.springframework.stereotype.Repository;

@Repository
public class AppOvulationManualDAO extends ServiceImpl<AppOvulationManualMapper, AppOvulationManualEntity> {
    public AppOvulationManualEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppOvulationManualEntity>lambdaQuery()
                .eq(AppOvulationManualEntity::getUserId, userId));
    }
}
