package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.SysTipsTemplateEntity;
import com.mira.user.dal.mapper.SysTipsTemplateMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * sys_tips_template DAO
 *
 * <AUTHOR>
 */
@Repository
public class SysTipsTemplateDAO extends ServiceImpl<SysTipsTemplateMapper, SysTipsTemplateEntity> {
    public SysTipsTemplateEntity getByModelIdAndLanguage(Integer modelId, String language) {
        return getOne(Wrappers.<SysTipsTemplateEntity>lambdaQuery()
                .eq(SysTipsTemplateEntity::getModelId, modelId)
                .eq(SysTipsTemplateEntity::getLanguage, language));
    }

    public List<SysTipsTemplateEntity> getBatchByModelIdAndLanguage(List<Integer> modelIds) {
        return list(Wrappers.<SysTipsTemplateEntity>lambdaQuery()
                .in(SysTipsTemplateEntity::getModelId, modelIds));
    }
}
