package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.UserDataAnalyzerEntity;
import com.mira.user.dal.mapper.UserDataAnalyzerMapper;
import org.springframework.stereotype.Repository;

@Repository
public class UserDataAnalyzerDAO extends ServiceImpl<UserDataAnalyzerMapper, UserDataAnalyzerEntity> {
    public UserDataAnalyzerEntity getByUserId(Long userId) {
        return getOne(Wrappers.<UserDataAnalyzerEntity>lambdaQuery()
                .eq(UserDataAnalyzerEntity::getUserId, userId));
    }
}
