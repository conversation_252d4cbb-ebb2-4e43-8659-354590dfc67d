package com.mira.user.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * Chart页文章推荐
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_chart_articles")
public class AppChartArticlesEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String body;

    /**
     * 图片
     */
    private String picture;

    /**
     * 链接
     */
    private String link;

    /**
     * 目标类型，例如 0-CycleTracking
     */
    private Integer goalType;

    /**
     * 添加时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String modifyTime;

    /**
     * 状态 0 正常状态 1 删除状态
     */
    @TableLogic
    protected Integer deleted;
}
