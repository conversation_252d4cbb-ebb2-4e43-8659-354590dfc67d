package com.mira.user.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 怀孕模式移除记录表
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-10-11
 **/
@Getter
@Setter
@TableName("app_remove_pregnant_mode_history")
public class AppRemovePregnantModeHistoryEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 结束怀孕原因：1:delivered baby;2:miscarriage;3:abortion;4:skip;
     */
    private Integer endReason;
    /**
     * 实际生产时间（生产后结束怀孕模式）
     */
    private String deliveryDate;

    /**
     * 移除次数
     */
    private Integer removeTimes;

    /**
     * 当次怀孕编号，用于匹配数据
     */
    private Integer number;
}
