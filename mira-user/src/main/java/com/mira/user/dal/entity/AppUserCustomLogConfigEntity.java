package com.mira.user.dal.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户日记记录配置表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_custom_log_config")
public class AppUserCustomLogConfigEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 是否同房
     */
    private Boolean sex;

    /**
     * 是否日记
     */
    private Boolean notes;

    /**
     * 是否温度
     */
    private Boolean temperature;

    /**
     * 温度默认单位
     */
    private String tempUnit;

    /**
     * 是否体重
     */
    private Boolean weight;

    /**
     * 体重默认单位
     */
    private String weightUnit;

    /**
     * 是否心情
     */
    private Boolean mood;

    /**
     * 是否药物
     */
    private Boolean medications;

    /**
     * 是否怀孕测试
     */
    private Boolean pregnantTest;

    /**
     * 是否排卵测试
     */
    private Boolean ovulationTest;

    /**
     * 是否记录症状 (数组)
     */
    private Boolean symptoms;

    /**
     * 是否记录Cervical Mucus
     */
    private Boolean cervicalMucus;

    /**
     * 是否记录Cervical Position
     */
    private Boolean cervicalPosition;

    /**
     * 是否记录Flow & Spotting
     */
    private Boolean spotting;

    /**
     * 斑点
     */
    private Boolean singleSpotting;

    /**
     * 经期，其他
     */
    private Boolean periodOther;
}
