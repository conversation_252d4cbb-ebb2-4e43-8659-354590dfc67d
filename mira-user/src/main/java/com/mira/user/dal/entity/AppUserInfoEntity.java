package com.mira.user.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.enums.UserModeChangeEnum;
import com.mira.api.user.enums.OnboardingStatusEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户信息详情
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_info")
public class AppUserInfoEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 英文名
     */
    private String firstName;

    /**
     * 英文姓
     */
    private String lastName;

    /**
     * 生日
     *
     * @deprecated 1.4之后使用birthYear、birthMonth、birthOfDay
     */
    private String birthday;

    /**
     * 出生年份
     */
    private Integer birthYear;

    /**
     * 出生月份
     */
    private Integer birthMonth;

    /**
     * 出生日期
     */
    private Integer birthOfDay;

    /**
     * 目标
     *
     * @deprecated 1.4之后使用goalStatus
     */
    private String goal;

    /**
     * 目标
     *
     * @see UserGoalEnum
     */
    private Integer goalStatus;

    /**
     * 用户模式修改标记
     *
     * @see UserModeChangeEnum
     */
    private String modeChangeFlag;

    /**
     * 用来控制fsh的测试提醒，1表示有fsh测试日推荐，0代表无
     *
     * @deprecated
     */
    private Integer isOft;

    /**
     * 目标
     */
    private String conditions;

    /**
     * 节育方式:0:避孕药;1:避孕贴;2:避孕针;3:避孕环;4:避孕植入物;5:宫内节育器IUD
     */
    private Integer hormonalBirthControl;

    /**
     * 通知标示:0 no remind;1remind
     *
     * @deprecated
     */
    private Integer remindFlag;

    /**
     * 通知时间
     *
     * @deprecated
     */
    private Long remindTime;

    /**
     * 通知时间
     *
     * @deprecated
     */
    private String remindTimeStr;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 体重
     *
     * @deprecated 1.3之后不再使用
     */
    private Float heavey;

    /**
     * 推送Token
     */
    private String pushToken;

    /**
     * 平台: iOS:3;android:4
     */
    private Integer platform;

    /**
     * 是否跟踪更年期，null无标识;0 不跟踪;1跟踪
     */
    private Integer trackingMenopause;

    /**
     * 开始跟踪更年期（如果有退出，则去除这个时间，下次进入时再设置）
     */
    private String trackingMenopauseDate;

    /**
     * 是否被定义为不规则周期;null未判断;0否;1是
     */
    private Integer definedIrregularCycle;

    /**
     * 接收通知 "是_1", "否_0"
     *
     * @deprecated
     */
    private Integer notifications;

    /**
     * 绑定设备
     */
    private String bindDevice;

    /**
     * 绑定时间
     */
    private String bindTime;

    /**
     * 设备版本
     */
    private String bindVersion;

    /**
     * 用户输入的经期的所有日期 json 数据
     *
     * @deprecated 1.3之后不再使用
     */
    private String periods;

    /**
     * 绑定标示：0没有绑定过；1绑定过;2:wait shipping
     */
    private Integer bindFlag;

    /**
     * No Period 标记，0-Cycle Tracking，2-TTC
     */
    private Integer noPeriod;

    /**
     * @see OnboardingStatusEnum
     *
     * Onboarding状态，0-未完成，1-到连接仪器，2-到达首页
     */
    private Integer onboardingStatus;

    /**
     * 不规则周期，0-否，1-是
     */
    private Integer irregularCycle;
}
