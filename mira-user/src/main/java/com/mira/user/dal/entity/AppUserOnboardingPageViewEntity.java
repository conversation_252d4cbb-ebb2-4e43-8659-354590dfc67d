package com.mira.user.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * onboarding页面编码保存记录
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-12-19
 **/
@Getter
@Setter
@TableName("app_user_onboarding_page_view")
public class AppUserOnboardingPageViewEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 页面编码
     */
    private String pageView;
}
