package com.mira.user.dto.diary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("编辑用户日记配置请求参数")
public class UserCustomLogConfigDTO {
    @ApiModelProperty("可以选择：sex、notes、temperature、weight、mood、medications、pregnantTest、" +
            "ovulationTest、symptoms、cervicalMucus、cervicalPosition、spotting、singleSpotting、periodOther、" +
            "glucoseControl")
    private String key;

    @ApiModelProperty("true：展示；false:不展示")
    private Boolean value;
}
