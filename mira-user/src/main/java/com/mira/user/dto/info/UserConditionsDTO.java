package com.mira.user.dto.info;

import com.mira.user.enums.user.UserConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("编辑用户Conditions")
public class UserConditionsDTO {
    /**
     * @see UserConditionEnum
     */
    @ApiModelProperty("用户Conditions")
    private List<Integer> conditions;
}
