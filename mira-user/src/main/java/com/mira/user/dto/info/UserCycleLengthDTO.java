package com.mira.user.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("用户平均周期长度")
public class UserCycleLengthDTO {
    @ApiModelProperty("用户平均周期长度")
    private Integer avgLenCycle;

    @ApiModelProperty("周期标识：0：正常；-1：I don't know;-2 varies often ；-3  I don't know + varies often")
    private Integer cycleFlag;
}
