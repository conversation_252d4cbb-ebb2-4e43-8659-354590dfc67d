package com.mira.user.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("用户平均经期长度")
public class UserPeriodLengthDTO {
    @ApiModelProperty("用户平均经期长度")
    private Integer avgLenPeriod;

    @ApiModelProperty("经期标识：0：正常；-1：I don't know")
    private Integer periodFlag;

    @ApiModelProperty("无经期标识，0-否，1-是")
    private Integer noPeriodFlag;
}
