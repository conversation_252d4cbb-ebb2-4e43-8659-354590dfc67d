package com.mira.user.dto.info.v5;

import com.mira.api.user.enums.daily.DailySymptomLevelEnum;
import com.mira.user.enums.user.v5.MenopauseSymptomEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-06-11
 **/
@Getter
@Setter
@ApiModel("更年期的症状")
public class MenopauseSymptomDTO {

    /**
     * @see MenopauseSymptomEnum
     */
    @ApiModelProperty(value = "Symptom")
    private Integer value;

    /**
     * @see DailySymptomLevelEnum
     * By default “None” is chosen for every symptom
     */
    @ApiModelProperty(value = "SymptomLevel")
    private Integer level;
}
