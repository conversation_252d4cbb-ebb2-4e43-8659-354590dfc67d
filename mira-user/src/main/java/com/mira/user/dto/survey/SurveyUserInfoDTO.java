package com.mira.user.dto.survey;

import com.mira.api.user.enums.UserGoalEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-04-18
 **/
@Getter
@Setter
public class SurveyUserInfoDTO {
    private Long userId;

    private Integer birthYear;

    private Integer birthMonth;

    private Integer birthDay;
    /**
     * 目标
     *
     * @see UserGoalEnum
     */
    private Integer goalStatus;
    /**
     * 绑定设备
     */
    private String bindDevice;

    private String conditions;

    /**
     * 国家
     */
    private String countryCode;

    /**
     * 洲
     */
    private String continentCode;

    /**
     * 货币
     */
    private String currentCurrency;

    /**
     * 周期信息
     */
    private String cycleData;

    /**
     * 激素数据
     */
    private String hormoneData;

    /**
     * ip modify time
     */
    private String ipModifyTimeStr;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 是否订阅
     */
    private Integer subscription;

    /**
     * 是否有辅导课程
     */
    private Integer coaching;
    /**
     * 是否跟踪更年期，null无标识;0 不跟踪;1跟踪
     */
    private Integer trackingMenopause;

}
