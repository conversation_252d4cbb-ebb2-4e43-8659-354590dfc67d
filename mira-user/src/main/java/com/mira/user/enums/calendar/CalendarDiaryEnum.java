package com.mira.user.enums.calendar;

import com.mira.core.consts.enums.WeightUnitEnum;
import com.mira.core.util.UnitConvertUtil;
import com.mira.user.dal.entity.AppUserDiaryEntity;
import com.mira.user.dto.diary.UserCustomLogDTO;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.function.BiConsumer;

/**
 * calendar diary
 *
 * <AUTHOR>
 */
@Getter
public enum CalendarDiaryEnum {
    WEIGHT_UNIT("weightUnit", (entity, value) -> setWeightUnit(entity, (UserCustomLogDTO) value)),
    MOOD("mood", (entity, value) -> entity.setMood(value.getMood())),
    SEX("sex", (entity, value) -> entity.setSex(value.getSex())),
    MUCUS_TYPE("mucusType", (entity, value) -> entity.setMucusType(value.getMucusType())),
    MUCUS_FLOW("mucusFlow", (entity, value) -> entity.setMucusFlow(value.getMucusFlow())),
    PREGNANT("pregnant", (entity, value) -> entity.setPregnant(value.getPregnant())),
    OPK("opk", (entity, value) -> entity.setOpk(value.getOpk())),
    FLOW_AND_SPOTTING("flowAndSpotting", (entity, value) -> entity.setFlowAndSpotting(value.getFlowAndSpotting())),
    CERVICAL_POSITION("cervicalPosition", (entity, value) -> entity.setCervicalPosition(value.getCervicalPosition())),
    CERVICAL_FIRMNESS("cervicalFirmness", (entity, value) -> entity.setCervicalFirmness(value.getCervicalFirmness())),
    CERVICAL_OPENNESS("cervicalOpenness", (entity, value) -> entity.setCervicalOpenness(value.getCervicalOpenness())),
    EDIT_PERIOD("editPeriod", (entity, value) -> {}),
    MEDICATIONS("medications", (entity, value) -> {}),
    TEMPERATURES("temperatures", (entity, value) -> {}),
    SINGLE_SPOTTING("spotting", (entity, value) -> entity.setSpotting(value.getSpotting())),
    PERIOD_OTHER("periodOther", (entity, value) -> entity.setPeriodOther(value.getPeriodOther())),
    GLUCOSE_CONTROL("glucoseControl", (entity, value) -> entity.setGlucoseControl(value.getGlucoseControl()))
    ;

    private final String field;
    private final BiConsumer<AppUserDiaryEntity, UserCustomLogDTO> consumer;

    CalendarDiaryEnum(String field, BiConsumer<AppUserDiaryEntity, UserCustomLogDTO> consumer) {
        this.field = field;
        this.consumer = consumer;

    }

    public void execute(AppUserDiaryEntity entity, UserCustomLogDTO value) {
        consumer.accept(entity, value);
    }

    public static CalendarDiaryEnum get(String field) {
        for (CalendarDiaryEnum calendarDiaryEnum : values()) {
            if (calendarDiaryEnum.getField().equals(field)) {
                return calendarDiaryEnum;
            }
        }
        return null;
    }

    private static void setWeightUnit(AppUserDiaryEntity entity, UserCustomLogDTO userCustomLogDTO) {
        String weightUnit = userCustomLogDTO.getWeightUnit();
        String weightValue = userCustomLogDTO.getWeightValue();
        if (StringUtils.isBlank(weightUnit)) {
            entity.setWeightK(null);
            entity.setWeightL(null);
        } else {
            BigDecimal weight = (StringUtils.isBlank(weightValue) ? null : BigDecimal.valueOf(Double.parseDouble(weightValue)));
            if (WeightUnitEnum.K.getValue().equals(weightUnit)) {
                entity.setWeightK(weight);
                entity.setWeightL(UnitConvertUtil.getWeight(WeightUnitEnum.K.getValue(), weight));
            } else {
                entity.setWeightL(weight);
                entity.setWeightK(UnitConvertUtil.getWeight(WeightUnitEnum.L.getValue(), weight));
            }
        }
    }
}
