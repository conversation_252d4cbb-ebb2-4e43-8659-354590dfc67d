package com.mira.user.enums.user;

import lombok.Getter;

/**
 * 节育方式枚举
 */
@Getter
public enum HormonalBirthControlEnum {
    /**
     * 0:避孕药 Birth control pill
     * 1:避孕贴 Birth control patch
     * 2:避孕针 Birth control shot
     * 3:避孕环 Birth control vaginal ring
     * 4:避孕植入物 Birth control implant
     * 5:宫内节育器 IUD
     */
    ZERO(0, "Birth control pill"),
    ONE(1, "Birth control patch"),
    TWO(2, "Birth control shot"),
    THREE(3, "Birth control vaginal ring"),
    FOUR(4, "Birth control implant"),
    FIVE(5, "IUD");

    private final Integer value;
    private final String description;

    HormonalBirthControlEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
}
