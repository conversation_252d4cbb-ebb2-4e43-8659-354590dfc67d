package com.mira.user.enums.user;

import lombok.Getter;

/**
 * 用户 Condition 枚举
 *
 * <AUTHOR>
 */
@Getter
public enum UserConditionEnum {
    HORMONE_IMBALANCE(0, "Hormone imbalance"),
    IRREGULAR_CYCLE(1, "Irregular cycle"),
    PCOS(2, "PCOS"),
    MISCARRIAGE_HISTORY(3, "Miscarriage history"),
    RECENTLY_STOPPED_BIRTH_CONTROL(4, "Recently stopped birth control"),
    BREASTFEEDING(5, "Breastfeeding"),

    NONE(6, "None"),
    HORMONAL_BIRTH_CONTROL(7, "Hormonal birth control"),
    POSTPARTOM(8, "Postpartom(not breastfeeding)"),
    IMBALANCE_ISSUES(9, "Imbalance issues"),
    ENDOMETRIOSIS(10, "Endometriosis"),
    BLANK(11, ""),
    UTERINE_FIBROIDS(12, "Uterine fibroids"),
    ADENOMYOSIS(14, "Adenomyosis"),
    PREMATURE_OVARIAN_FAILURE(15, "Premature ovarian failure"),
    BREAST_CANCER(16, "Breast cancer"),
    GY<PERSON>COLOGICAL_CANCER(17, "Gynecological cancer"),
    ANXIETY_DISORDER(18, "Anxiety disorder"),
    DEPRESSIVE_DISORDER(19, "Depressive disorder"),
    THYROID_ISSUES(20, "Thyroid issues"),
    DIABETES(21, "Diabetes"),
    THROMBOSIS(22, "Thrombosis"),
    ARTERIAL_HYPERTENSION(23, "Arterial hypertension"),

    VAGINAL_DRYNESS(24, "Vaginal dryness"),
    VAGINAL_ITCHING_AND_BURNING(25, "Vaginal itching and burning"),
    PAINFUL_INTERCOURSE(26, "Painful intercourse"),
    INCREASED_NEED_TO_URINATE(27, "Increased need to urinate"),
    BLADDER_INCONTINENCE(28, "Bladder incontinence"),
    DIFFICULTY_IN_URINATING(29, "Difficulty in urinating"),
    OTHER(30, "Other")
    ;

    private final Integer value;
    private final String description;

    UserConditionEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
}
