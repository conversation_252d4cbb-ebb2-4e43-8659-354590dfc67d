package com.mira.user.enums.user.v5;

import lombok.Getter;

/**
 * What kind of HRT you are on?
 *
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2024-06-11 03:07
 **/
@Getter
public enum MenopauseHrtTypeEnum {
    // v4
    ESTROGEN_PROGESTIN_PILLS(0, "estrogen-progestin pills"),
    ESTROGEN_PROGESTIN_PATCHES(1, "estrogen - progestin patches"),
    VAGINAL_ESTROGEN(2, "vaginal estrogen"),
    ESTROGEN_TABLETS(3, "estrogen tablets"),
    ESTROGEN_PATCHES(4, "estrogen patches"),
    ESTROGEN_GEL(5, "estrogen gel"),
    ESTROGEN_SPRAY(6, "estrogen spray"),
    INTRAUTERINE(7, "intrauterine device"),
    TESTOSTERONE(8, "testosterone cream, gel"),
    PROGESTIN_PILLS(9, "progestin pills/implants"),
    <PERSON><PERSON><PERSON>(10, "other"),

    // v5
    ORAL_ESTRADIOL_SUCH_AS_ESTRACE(100, "Oral estradiol such as Estrace"),
    ORAL_CONJUGATED_EQUINE_ESTROGEN(101, "Oral conjugated equine estrogen (as Premarin)"),
    ESTRADIOL_PATCH(102, "Estradiol patch"),
    ESTRADIOL_GEL(103, "Estradiol gel"),
    VAGINAL_ESTROGEN_RING(104, "Vaginal estrogen ring (as Femring)"),
    ESTRADIOL_CREAM_SUCH_AS_ESTRACE(105, "Estradiol cream such as Estrace"),
    CONJUGATED_EQUINE_ESTROGEN(106, "Conjugated equine estrogen (as Premarin)"),
    ESTROGEN_TABLET(107, "Estrogen tablet (as Vagifem)"),
    ESTROGEN_CAPSULE(108, "Estrogen capsule (as Immvexxy)"),
    ESTROGEN_RING(109, "Estrogen ring (as Estring)"),
    ORAL_BIOIDENTICAL_PROGESTERONE(110, "Oral bioidentical progesterone"),
    TOPICAL_LOCAL_PROGESTERONE(111, "Topical/Local progesterone"),
    COMBIPATCH(112, "Combipatch (estradiol and norethindrone)"),
    CLIMARAPRO(113, "ClimaraPro (estradiol and levonorgestrel)"),
    DUAVEE(114, "Duavee (conjugated equine estrogen and bazedoxifene)"),
    BIJUVIA(115, "Bijuvia (bioidentical option of estradiol and progesterone)"),
    TESTOSTERONE_CREAM(116, "Testosterone cream"),
    TESTOSTERONE_PELLETS_INJECTIONS(117, "Testosterone pellets/injections"),
    OTHER_V5(118, "Other");

    private final Integer value;
    private final String description;

    MenopauseHrtTypeEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }


    }
