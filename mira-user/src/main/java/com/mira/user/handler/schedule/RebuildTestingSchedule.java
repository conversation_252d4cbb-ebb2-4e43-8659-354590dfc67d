package com.mira.user.handler.schedule;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestingProductDayDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.StringListUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.dal.dao.SysNotificationTestingStatisticsDAO;
import com.mira.user.dal.dao.UserProductTrialDAO;
import com.mira.user.dal.entity.SysNotificationTestingStatisticsEntity;
import com.mira.api.user.dto.user.TrialUserDTO;
import com.mira.user.service.manager.CacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Rule1：用户打开通知开关 <br>
 * Rule2：30天内有测试
 */
@Slf4j
@Component
public class RebuildTestingSchedule {
    @Resource
    private UserProductTrialDAO userProductTrialDAO;
    @Resource
    private SysNotificationTestingStatisticsDAO sysNotificationTestingStatisticsDAO;

    @Resource
    private CacheManager cacheManager;

    private final static int ACTIVE_DAY = 30;

    private BuildPushJobDTO getBuildPushJobDTO(Long currentTimeMillis, Long userId, String testingScheduleRemindTimeStr) {
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        BuildPushJobDTO buildPushJobDTO = new BuildPushJobDTO();
        buildPushJobDTO.setUserId(algorithmResultDTO.getUserId());
        // " 05:00:00"
        buildPushJobDTO.setReminderSetTime(testingScheduleRemindTimeStr.substring(10));

        String timeZone = algorithmResultDTO.getTimeZone();
        buildPushJobDTO.setTimeZone(timeZone);
        buildPushJobDTO.setToday(ZoneDateUtil.format(timeZone, currentTimeMillis, DatePatternConst.DATE_PATTERN));
        buildPushJobDTO.setHormoneDatas(JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class));
        buildPushJobDTO.setCycleDataDTOS(JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class));
        return buildPushJobDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void execute(Long userId, String testingScheduleRemindTimeStr) {
        Long now = System.currentTimeMillis();
        BuildPushJobDTO buildPushJobDTO = this.getBuildPushJobDTO(now, userId, testingScheduleRemindTimeStr);

        List<SysNotificationTestingStatisticsEntity> createTestingDayList = new ArrayList<>();
        List<SysNotificationTestingStatisticsEntity> updateTestingDayList = new ArrayList<>();

        // 1. get trialUserDTOS
        List<TrialUserDTO> trialUserDTOS = userProductTrialDAO.listTrialUsers();

        String reminderSetTime = buildPushJobDTO.getReminderSetTime();
        String timeZone = buildPushJobDTO.getTimeZone();
        String today = ZoneDateUtil.format(timeZone, now, DatePatternConst.DATE_PATTERN);
        String beforeDays = LocalDateUtil.plusDay(today, -ACTIVE_DAY, DatePatternConst.DATE_PATTERN);
        // [Rule1: has data in 30 days（active user)]
        List<HormoneDTO> hormoneDataList = buildPushJobDTO.getHormoneDatas();
        // 匹配测试时间
        boolean activeUser = hormoneDataList.stream()
                .anyMatch(hormoneDTO -> LocalDateUtil.minusToDay(hormoneDTO.getTest_time(), beforeDays) >= 0);
        if (!activeUser) {
            return;
        }

        List<CycleDataDTO> cycleDataDTOS = buildPushJobDTO.getCycleDataDTOS();
        ProductReminderTimesDTO productReminderTimesDTO = new ProductReminderTimesDTO();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            // 校验周期开始时间，去除周期开始时间在今天之后的
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            int subtractFromPeriodStart = LocalDateUtil.minusToDay(today, datePeriodStart);
            if (subtractFromPeriodStart < 0) {
                continue;
            }
            // 根据测试日数据构建推送时间
            productReminderTimesDTO = buildProductReminderTimes(now, reminderSetTime, timeZone, cycleDataDTO.getTesting_day_list());
        }

        // 如果用户没有参与HCG bata测试，需要将hcg的测试日推送剔除
        buildTrailUser(userId, trialUserDTOS, productReminderTimesDTO);
        // 首次测试时间
        FirstReminderTime firstReminderTime = new FirstReminderTime().create(timeZone, productReminderTimesDTO);
        // 当日测试提醒统计
        SysNotificationTestingStatisticsEntity testStatisticsEntity = sysNotificationTestingStatisticsDAO.getByUserId(userId);
        if (Objects.isNull(testStatisticsEntity)) {
            testStatisticsEntity = new SysNotificationTestingStatisticsEntity();
            testStatisticsEntity.setUserId(userId);
            buildStatistics(timeZone, testStatisticsEntity, createTestingDayList, productReminderTimesDTO, firstReminderTime);
        } else {
            buildStatistics(timeZone, testStatisticsEntity, updateTestingDayList, productReminderTimesDTO, firstReminderTime);
        }

        // 保存统计记录
        if (!createTestingDayList.isEmpty()) {
            sysNotificationTestingStatisticsDAO.saveBatch(createTestingDayList);
        }
        if (!updateTestingDayList.isEmpty()) {
            sysNotificationTestingStatisticsDAO.updateBatchById(updateTestingDayList);
        }
    }

    /**
     * 测试日日期 + 用户设置的通知时间
     */
    private ProductReminderTimesDTO buildProductReminderTimes(Long now, String reminderSetTime, String timeZone,
                                                              TestingProductDayDTO testingDayList) {
        ProductReminderTimesDTO productReminderTimesDTO = new ProductReminderTimesDTO();
        if (Objects.isNull(testingDayList)) {
            return productReminderTimesDTO;
        }
        addReminderTime(now, timeZone, reminderSetTime, testingDayList.getProduct02(), productReminderTimesDTO.getProduct02ReminderTimes());
        addReminderTime(now, timeZone, reminderSetTime, testingDayList.getProduct03(), productReminderTimesDTO.getProduct03ReminderTimes());
        addReminderTime(now, timeZone, reminderSetTime, testingDayList.getProduct09(), productReminderTimesDTO.getProduct09ReminderTimes());
        addReminderTime(now, timeZone, reminderSetTime, testingDayList.getProduct12(), productReminderTimesDTO.getProduct12ReminderTimes());
        addReminderTime(now, timeZone, reminderSetTime, testingDayList.getProduct14(), productReminderTimesDTO.getProduct14ReminderTimes());
        addReminderTime(now, timeZone, reminderSetTime, testingDayList.getProduct16(), productReminderTimesDTO.getProduct16ReminderTimes());
        addReminderTime(now, timeZone, reminderSetTime, testingDayList.getProduct18(), productReminderTimesDTO.getProduct18ReminderTimes());
        return productReminderTimesDTO;
    }

    /**
     * 添加通知时间
     */
    private void addReminderTime(Long now, String timeZone, String reminderSetTime,
                                 List<String> testingDayProductList, List<String> reminderTimeList) {
        if (CollectionUtils.isEmpty(testingDayProductList)) {
            return;
        }
        for (String date : testingDayProductList) {
            String reminderTime = date + reminderSetTime;
            if (ZoneDateUtil.timestamp(timeZone, reminderTime, DatePatternConst.DATE_TIME_PATTERN) > now) {
                reminderTimeList.add(reminderTime);
            }
        }
    }

    /**
     * 临床用户
     */
    private void buildTrailUser(Long userId,
                                List<TrialUserDTO> trialUserDTOS,
                                ProductReminderTimesDTO productReminderTimesDTO) {
        // TODO 关于临床测试用户，先保持这样，后续看需求变化
        if (Objects.isNull(trialUserDTOS)) {
            productReminderTimesDTO.getProduct02ReminderTimes().clear();
        } else {
            TrialUserDTO trialUserDTO = trialUserDTOS.stream()
                    .filter(trialUser -> (userId.equals(trialUser.getUserId())))
                    .findFirst()
                    .orElse(null);
            if (trialUserDTO == null) {
                productReminderTimesDTO.getProduct02ReminderTimes().clear();
            } else {
                if (trialUserDTO.getFlag() != 2) {
                    productReminderTimesDTO.getProduct02ReminderTimes().clear();
                }
            }
        }
    }

    /**
     * 构建用户当日测试提醒统计
     */
    private void buildStatistics(String timeZone,
                                 SysNotificationTestingStatisticsEntity testStatisticsEntity,
                                 List<SysNotificationTestingStatisticsEntity> testingList,
                                 ProductReminderTimesDTO productReminderTimesDTO,
                                 FirstReminderTime firstReminderTime) {
        testStatisticsEntity.setHcgFirstReminderTime(firstReminderTime.getProduct02FirstReminderTime());
        testStatisticsEntity.setHcgReminderTimes(StringListUtil.listToString(productReminderTimesDTO.getProduct02ReminderTimes(), ","));

        testStatisticsEntity.setE3gFirstReminderTime(firstReminderTime.getProduct03FirstReminderTime());
        testStatisticsEntity.setE3gReminderTimes(StringListUtil.listToString(productReminderTimesDTO.getProduct03ReminderTimes(), ","));

        testStatisticsEntity.setPdgFirstReminderTime(firstReminderTime.getProduct09FirstReminderTime());
        testStatisticsEntity.setPdgReminderTimes(StringListUtil.listToString(productReminderTimesDTO.getProduct09ReminderTimes(), ","));

        testStatisticsEntity.setProduct12FirstReminderTime(firstReminderTime.getProduct12FirstReminderTime());
        testStatisticsEntity.setProduct12ReminderTimes(StringListUtil.listToString(productReminderTimesDTO.getProduct12ReminderTimes(), ","));

        testStatisticsEntity.setProduct14FirstReminderTime(firstReminderTime.getProduct14FirstReminderTime());
        testStatisticsEntity.setProduct14ReminderTimes(StringListUtil.listToString(productReminderTimesDTO.getProduct14ReminderTimes(), ","));

        testStatisticsEntity.setProduct16FirstReminderTime(firstReminderTime.getProduct16FirstReminderTime());
        testStatisticsEntity.setProduct16ReminderTimes(StringListUtil.listToString(productReminderTimesDTO.getProduct16ReminderTimes(), ","));

        testStatisticsEntity.setProduct18FirstReminderTime(firstReminderTime.getProduct18FirstReminderTime());
        testStatisticsEntity.setProduct18ReminderTimes(StringListUtil.listToString(productReminderTimesDTO.getProduct18ReminderTimes(), ","));

        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, testStatisticsEntity);
        testingList.add(testStatisticsEntity);
    }

}
