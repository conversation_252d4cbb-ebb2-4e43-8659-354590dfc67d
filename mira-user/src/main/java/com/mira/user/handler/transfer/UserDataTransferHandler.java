package com.mira.user.handler.transfer;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.period.AlgorithmEditPeriodDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.LastCycleFlagEnum;
import com.mira.api.bluetooth.provider.IAlgorithmProvider;
import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import com.mira.api.user.enums.daily.DailyStatusFlowAndSpottingEnum;
import com.mira.api.user.enums.daily.DailyStatusSymptomsEnum;
import com.mira.api.user.enums.daily.DailySymptomLevelEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.StringListUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.dal.dao.*;
import com.mira.user.dal.entity.*;
import com.mira.user.enums.user.UserConditionEnum;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.bluetooth.util.PeriodUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 用户数据迁移
 *
 * <AUTHOR>
 */
@Deprecated
@Slf4j
@Component
public class UserDataTransferHandler {
    @Resource
    private TransferRequestClient transferRequestClient;
    @Resource
    private AppUserDAO appUserDAO;
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private AppUserDiaryDAO appUserDiaryDAO;
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;
    @Resource
    private AppUserDiarySymptomsDAO appUserDiarySymptomsDAO;
    @Resource
    private UserProductTrialDAO userProductTrialDAO;
    @Resource
    private IAlgorithmProvider algorithmProvider;

    /**
     * 迁移用户数据
     */
    public void checkTransfer(AppUserEntity appUser, String timeZone) {
        Integer source = appUser.getSource();
        Integer transferFlag = appUser.getTransferFlag();
        // transferMode:0:不执行数据迁移；1：v3 transfer to v4; 2: v2 transfer to v4
        Integer transferMode = 0;
        if (source == 0 && transferFlag == 1) {
            transferMode = 1;
        } else if (source == 1 || source == 2 || source == 3) {
            if (transferFlag == 0) {
                transferMode = 1;
            }
        } else if (source == 0 && transferFlag == 0) {
            transferMode = 2;
        }
        if (transferMode == 1) {
            // v3 transfer to v4
            log.info("user:{}, v3 transfer to v4");
            execute(appUser);
        } else if (transferMode == 2) {
            // v2 transfer to v4
            log.info("user:{}, v2 transfer to v4");
            Map<String, String> urlParamMap = new HashMap<>();
            urlParamMap.put("userId", Long.toString(appUser.getId()));
            urlParamMap.put("timeZone", timeZone);
            CompletableFuture.runAsync(() -> transferRequestClient.post(TransferRequestUrl.USER_JOB, urlParamMap));
            try {
                Thread.sleep(10000);
                // 1.2用户第一次登录，transfer时暂停10秒
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            execute(appUser);
        }
    }

    /**
     * 执行transfer逻辑
     */
    private void execute(AppUserEntity appUser) {
        updateUserInfo(appUser.getId());
        updateUserDiary(appUser.getId());
        editPeriod(appUser);
        // 修改transferFlag状态
        appUser.setTransferFlag(2);
        appUserDAO.updateById(appUser);
    }

    /**
     * 更新用户log记录
     */
    private void updateUserDiary(Long userId) {
        List<AppUserDiaryEntity> appUserDiaryEntityList = appUserDiaryDAO.listByUserId(userId);
        if (CollectionUtils.isEmpty(appUserDiaryEntityList)) {
            return;
        }

        List<AppUserDiaryEntity> updateAppUserDiaryEntityList = new ArrayList<>();
        for (AppUserDiaryEntity appUserDiaryEntity : appUserDiaryEntityList) {
            String timeZone = appUserDiaryEntity.getTimeZone();
            Long diaryDay = appUserDiaryEntity.getDiaryDay();
            String diaryDayStr = appUserDiaryEntity.getDiaryDayStr();
            String v3SymptomsStr = appUserDiaryEntity.getSymptoms();
            if (StringUtils.isNotBlank(v3SymptomsStr)) {
                List<String> v3Symptoms = StringListUtil.strToList(v3SymptomsStr, ",");
                List<UserSymptomDTO> symptoms = new ArrayList<>();
                for (String v3Symptom : v3Symptoms) {
                    if (DailyStatusSymptomsEnum.O.getValue().equals(v3Symptom)) {
                        appUserDiaryEntity.setFlowAndSpotting(DailyStatusFlowAndSpottingEnum.M.getValue());
                        continue;
                    }
                    UserSymptomDTO userSymptomDTO = new UserSymptomDTO();
                    userSymptomDTO.setLevel(DailySymptomLevelEnum.TWO.getValue());
                    String value = DailyStatusSymptomsEnum.getEnumByValue(v3Symptom).getValue();
                    userSymptomDTO.setValue(value);
                    symptoms.add(userSymptomDTO);
                }
                AppUserDiarySymptomsEntity userDiarySymptomsEntity = new AppUserDiarySymptomsEntity();
                userDiarySymptomsEntity.setUserId(userId);
                userDiarySymptomsEntity.setDiaryDayStr(diaryDayStr);
                userDiarySymptomsEntity.setDiaryDay(diaryDay);
                String symptomsStr = JsonUtil.toJson(symptoms);
                userDiarySymptomsEntity.setSymptoms(symptomsStr);
                UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userDiarySymptomsEntity);
                appUserDiarySymptomsDAO.save(userDiarySymptomsEntity);
            }

            BigDecimal tempC = appUserDiaryEntity.getTempC();
            if (tempC != null) {
                String tempTime = ZoneDateUtil.format(timeZone, appUserDiaryEntity.getDiaryDay(), DatePatternConst.DATE_TIME_PATTERN);
                appUserDiaryEntity.setTempTime(tempTime);
                updateAppUserDiaryEntityList.add(appUserDiaryEntity);
            }
        }
        if (!CollectionUtils.isEmpty(updateAppUserDiaryEntityList)) {
            appUserDiaryDAO.updateBatchById(updateAppUserDiaryEntityList);
        }
    }

    /**
     * 更新userInfo
     */
    private void updateUserInfo(Long userId) {
        AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(userId);
        String birthday = appUserInfoEntity.getBirthday();
        if (StringUtils.isNotBlank(birthday) && Integer.valueOf(birthday) >= 1960) {
            appUserInfoEntity.setBirthYear(Integer.valueOf(birthday));
        }
        String goal = appUserInfoEntity.getGoal();
        if (StringUtils.isNotBlank(goal)) {
            if ("Avoid Pregnancy".equals(goal)) {
                appUserInfoEntity.setGoalStatus(UserGoalEnum.TTA.getValue());
            } else if ("Log period".equals(goal)) {
                appUserInfoEntity.setGoalStatus(UserGoalEnum.CYCLE_TRACKING.getValue());
            } else if (goal.contains("Get Pregnant")) {
                appUserInfoEntity.setGoalStatus(UserGoalEnum.TTC.getValue());
            }
        }
        appUserInfoEntity.setConditions(String.valueOf(UserConditionEnum.NONE.getValue()));
        appUserInfoEntity.setRemindFlag(1);
        appUserInfoDAO.updateById(appUserInfoEntity);
    }

    /**
     * 执行编辑经期逻辑
     */
    private void editPeriod(AppUserEntity user) {
        Long userId = user.getId();
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            return;
        }

        // 调算法计算
        appUserPeriodEntity.setFresh(1);
        // 编辑经期后变成老用户
        appUserPeriodDAO.updateById(appUserPeriodEntity);
        String periods = appUserPeriodEntity.getPeriods();
        List<Long> dbPeriodList = StringListUtil.strToLongList(periods, ",");
        // 以数据库的时间戳+用户当前时区来转换成用户本地时间
        String timeZone = appUserPeriodEntity.getTimeZone();
        List<String> periodStrList = PeriodUtil.periodsLong2String(dbPeriodList, timeZone);
        List<UserPeriodDataDTO> userPeriodDataList = PeriodUtil.buildPeriodList(appUserPeriodEntity.getAvgLenPeriod(), periodStrList);
        UserPeriodParamDTO userPeriodParam = new UserPeriodParamDTO();
        BeanUtil.copyProperties(appUserPeriodEntity, userPeriodParam);
        userPeriodParam.setUserMode(UserGoalEnum.TTC.getValue());
        userPeriodParam.setConditions(String.valueOf(UserConditionEnum.NONE.getValue()));
        // beta测试
        UserProductTrialEntity userProductTrial = userProductTrialDAO.getByEmail(user.getEmail());

        // algorithm api
        algorithmProvider.algorithmEditPeriod(new AlgorithmEditPeriodDTO()
                .setTimeZone(timeZone)
                .setTrialFlag(userProductTrial != null ? userProductTrial.getFlag() : null)
                .setLastCycleFlag(LastCycleFlagEnum.ZERO.getFlag())
                .setUserPeriodDataList(userPeriodDataList)
                .setUserPeriodParam(userPeriodParam)
                .setAlgorithmRequestTypeEnum(AlgorithmRequestTypeEnum.TRANSFER));
    }
}
