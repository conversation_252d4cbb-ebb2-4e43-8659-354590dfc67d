package com.mira.user.service.blog;

import com.mira.api.thirdparty.dto.blog.BlogCollectDTO;
import com.mira.api.thirdparty.dto.blog.BlogViewTimeDTO;
import com.mira.user.controller.vo.blog.BlogFeaturedListVO;
import com.mira.user.controller.vo.blog.BlogListVO;
import com.mira.user.controller.vo.blog.RecommendBlogVO;

import java.util.List;

/**
 * H5，博客信息接口
 *
 * <AUTHOR>
 */
public interface IBlogService {
    /**
     * 保存blog的点击记录
     *
     * @param blogViewTimeDTO log参数
     */
    void saveLog(BlogViewTimeDTO blogViewTimeDTO);

    /**
     * 获取用户的blog收藏列表
     *
     * @return Object
     */
    Object blogList();

    /**
     * 获取用户的blog关键词收藏列表
     *
     * @return List
     */
    List<String> blogKeywordList();

    /**
     * blog的收藏与取消收藏
     *
     * @param blogCollectDTO 请求参数
     */
    void blogCollect(BlogCollectDTO blogCollectDTO);

    /**
     * 特色博文列表
     *
     * @return BlogFeaturedListVO
     */
    BlogFeaturedListVO blogFeaturedList();

    /**
     * 搜索框搜索
     *
     * @param keyword 关键字
     * @param page    页码
     * @return BlogListVO
     */
    BlogListVO blogSearch(String keyword, Integer page);

    /**
     * Event页面列表
     *
     * @return Object
     */
    Object togetherList();

    /**
     * 获取博文详情
     *
     * @param blogId 博文id
     * @return BlogListVO
     */
    BlogListVO blogDetail(Long blogId);

    /**
     * 特殊分类博文列表
     *
     * @param term story; fertility; course
     * @param page 页码
     * @return BlogListVO
     */
    BlogListVO blogTermList(String term, Integer page);

    /**
     * 指定博文列表
     *
     * @param type news, blog
     * @param page 页码
     * @return BlogListVO
     */
    BlogListVO blogTypeList(String type, Integer page);

    /**
     * 产品列表
     *
     * @param name     产品名称
     * @param currency USD, GBR, AUD, EUR, CAD
     * @return 列表
     */
    Object product(String name, String currency);

    /**
     * 课程列表
     *
     * @return BlogListVO
     */
    BlogListVO course();

    /**
     * 课程详情
     *
     * @param chapterId id
     * @return BlogListVO
     */
    BlogListVO chapter(Long chapterId);

    /**
     * 推荐博文列表
     *
     * @param start 起始索引
     * @param end   结束索引
     * @return RecommendBlogVO
     */
    RecommendBlogVO blogRecommendList(Integer start, Integer end);

    /**
     * 获取所有webinar
     *
     * @return
     */
    Object webinarAll();
}
