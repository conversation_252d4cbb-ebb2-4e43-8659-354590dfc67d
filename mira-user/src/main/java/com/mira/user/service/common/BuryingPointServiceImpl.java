package com.mira.user.service.common;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.mongo.dto.burying.AccessInInfoDTO;
import com.mira.api.mongo.dto.burying.AccessInInfoMongoDTO;
import com.mira.api.mongo.dto.burying.AccessOutInfoDTO;
import com.mira.api.mongo.dto.burying.AccessOutInfoMongoDTO;
import com.mira.api.mongo.provider.IAccessLogProvider;
import com.mira.api.thirdparty.dto.blog.Ip2CountryDTO;
import com.mira.api.thirdparty.provider.IBlogProvider;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.user.async.KlaviyoProducer;
import com.mira.user.dal.dao.AppUserDAO;
import com.mira.user.dal.dao.AppUserInfoDAO;
import com.mira.user.dal.entity.AppUserEntity;
import com.mira.user.dal.entity.AppUserInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * 埋点接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BuryingPointServiceImpl implements IBuryingPointService {
    @Resource
    private AppUserDAO appUserDAO;

    @Resource
    private KlaviyoProducer klaviyoProducer;

    @Resource
    private IBlogProvider blogProvider;
    @Resource
    private IAccessLogProvider accessLogProvider;
    @Resource
    private AppUserInfoDAO appUserInfoDAO;

    @Override
    public void timeSpendAccessLog(AccessInInfoDTO accessInInfoDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String ip = ContextHolder.get(HeaderConst.IP);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        UserTypeEnum userTypeEnum = UserTypeEnum.get(loginInfo.getUserType());
        if (UserTypeEnum.APP_USER != userTypeEnum) {
            return;
        }

        // access in log, recent access log
        AccessInInfoMongoDTO accessInInfoMongoDTO = BeanUtil.toBean(accessInInfoDTO, AccessInInfoMongoDTO.class);
        accessInInfoMongoDTO.setUserId(userId);
        accessInInfoMongoDTO.setIp(ip);
        accessInInfoMongoDTO.setTimeZone(timeZone);
        CompletableFuture.runAsync(() -> {
            try {
                accessLogProvider.accessInLog(accessInInfoMongoDTO);
                accessLogProvider.recentAccessLog(accessInInfoMongoDTO);
            } catch (Exception e) {
                log.error("user:{} save access log error", userId, e);
            }
            // update ip
            updateIp(userId, ip, timeZone);
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("user:{} update ip error", userId, ex);
            return null;
        });

        // klaviyo
        String createTime = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
        klaviyoProducer.appAccessIn(userId, accessInInfoMongoDTO, createTime);
    }

    private void updateIp(Long userId, String ip, String timeZone) {
        // app user
        AppUserEntity appUser = appUserDAO.getById(userId);
        Ip2CountryDTO ip2CountryDTO = blogProvider.getCountryByIp(ip, userId).getData();
        appUser.setCurrentIp(ip);
        appUser.setCountryCode(ip2CountryDTO.getCountryCode());
        appUser.setContinentCode(ip2CountryDTO.getContinentCode());
        appUser.setIpModifyTime(System.currentTimeMillis());
        appUser.setTimeZone(timeZone);
        appUser.setIpModifyTimeStr(ZoneDateUtil
                .format(appUser.getTimeZone(), appUser.getIpModifyTime(), DatePatternConst.DATE_TIME_PATTERN));
        appUserDAO.updateById(appUser);
        // app user info
        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(userId);
        if (appUserInfo != null) {
            appUserInfo.setTimeZone(timeZone);
            appUserInfoDAO.updateById(appUserInfo);
        }
    }

    @Override
    public void timeSpendLog(AccessOutInfoDTO accessOutInfoDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String ip = loginInfo.getIp();
        String timeZone = loginInfo.getTimeZone();

        UserTypeEnum userTypeEnum = UserTypeEnum.get(loginInfo.getUserType());
        if (UserTypeEnum.APP_USER != userTypeEnum) {
            return;
        }

        CompletableFuture.runAsync(() -> {
            AccessOutInfoMongoDTO accessOutInfoMongoDTO = BeanUtil.toBean(accessOutInfoDTO, AccessOutInfoMongoDTO.class);
            accessOutInfoMongoDTO.setUserId(userId);
            accessOutInfoMongoDTO.setIp(ip);
            accessOutInfoMongoDTO.setTimeZone(timeZone);
            accessLogProvider.accessLog(accessOutInfoMongoDTO);
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("user{}, save access log error", userId, ex);
            return null;
        });
    }
}
