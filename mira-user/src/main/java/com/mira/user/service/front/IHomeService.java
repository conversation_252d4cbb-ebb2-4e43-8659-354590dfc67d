package com.mira.user.service.front;

import com.mira.user.controller.vo.home.HomeBannerVO;
import com.mira.user.controller.vo.home.HomeDataVO;

import java.util.List;

/**
 * APP首页接口
 *
 * <AUTHOR>
 */
public interface IHomeService {
    /**
     * 首页Banner列表
     *
     * @return List<HomeBannerVO>
     */
    List<HomeBannerVO> list();

    /**
     * 首页数据
     *
     * @return HomeDataVO
     */
    @Deprecated
    HomeDataVO homeData();

    /**
     * 延长最近的实周期
     */
    void extendPeriod();

    /**
     * 确认今天是实际周期
     */
    void confirmPeriod();

    /**
     * 长周期修改
     */
    void longerCycleChange();
}
