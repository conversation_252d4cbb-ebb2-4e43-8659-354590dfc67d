package com.mira.user.service.front.impl;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.util.HormoneDataUtil;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.util.UserGoalUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.controller.vo.calendar.CalendarDataVO;
import com.mira.user.controller.vo.calendar.CalendarDayLogVO;
import com.mira.user.controller.vo.calendar.DayDataVO;
import com.mira.user.dal.dao.AppUserSexConfigDAO;
import com.mira.user.dal.entity.AppUserSexConfigEntity;
import com.mira.user.service.manager.CacheManager;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 日历页接口实现
 *
 * <AUTHOR>
 */
@Service("calendarService")
public class CalendarServiceImpl extends AbstractCalendarService {
    @Resource
    private AppUserSexConfigDAO appUserSexConfigDAO;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private ISsoProvider ssoProvider;

    @Override
    public CalendarDataVO calendarData(String startDate, String endDate) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);

        // 获取sex日期
        List<String> sexDateList = buildSexList(userId);

        // 周期数据、激素数据
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);

        // 测试时间
        Set<String> testDates = HormoneDataUtil.buildTestDates(hormoneDatas);

        // result vo
        CalendarDataVO calendarDataVO = new CalendarDataVO();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        calendarDataVO.setUserMode(UserGoalUtil.getUserGoalEnum(loginUserInfoDTO).getValue());

        List<DayDataVO> dayDataVOS = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            List<String> cycleCdIndex = cycleDataDTO.getCycle_cd_index();
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            String datePeriodStart = cycleDataDTO.getDate_period_start();

            if (!buildDayDataVOCheck(startDate, endDate, cycleDataDTO)) {
                continue;
            }

            for (int i = 1; i <= lenCycle; i++) {
                String day = LocalDateUtil.plusDay(datePeriodStart, i - 1, DatePatternConst.DATE_PATTERN);
                if (LocalDateUtil.minusToDay(day, startDate) >= 0 && LocalDateUtil.minusToDay(day, endDate) < 0) {
                    DayDataVO dayDataVO = buildDayDataVO(i, day, cycleCdIndex, sexDateList, testDates);
                    dayDataVO.setDayProperty(buildDayProperty(calendarDataVO.getUserMode(), cycleDataDTO, day, i));
                    buildDayDataVO(day, today, cycleDataDTOS, dayDataVO);
                    dayDataVOS.add(dayDataVO);
                }
            }

            calendarDataVO.setDayDataVOs(dayDataVOS);
        }

        return calendarDataVO;
    }

    @Override
    public CalendarDayLogVO calendarDayLog(String dateStr) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // vo
        CalendarDayLogVO calendarDayLogVO = new CalendarDayLogVO();
        // algorithm
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
        // today
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        if (LocalDateUtil.minusToDay(dateStr, today) <= 0) {
            buildPastTimDayLog(userId, today, dateStr, calendarDayLogVO, cycleDataDTOS, hormoneDatas);
        } else {
            buildFutureTimeDayLog(dateStr, calendarDayLogVO, cycleDataDTOS);
        }
        return calendarDayLogVO;
    }

    @Override
    public Integer sexConfig() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        Integer display = 1;
        AppUserSexConfigEntity sexConfigEntity = appUserSexConfigDAO.getByUserId(userId);
        if (!org.springframework.util.ObjectUtils.isEmpty(sexConfigEntity)) {
            display = sexConfigEntity.getDisplay();
        }
        return display;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer sexConfigEdit(Integer display) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        AppUserSexConfigEntity sexConfigEntity = appUserSexConfigDAO.getByUserId(userId);
        if (ObjectUtils.isEmpty(sexConfigEntity)) {
            sexConfigEntity = new AppUserSexConfigEntity();
            sexConfigEntity.setUserId(userId);
            sexConfigEntity.setDisplay(display);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, sexConfigEntity);
            appUserSexConfigDAO.save(sexConfigEntity);
        } else {
            sexConfigEntity.setDisplay(display);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, sexConfigEntity);
            appUserSexConfigDAO.updateById(sexConfigEntity);
        }
        return display;
    }
}
