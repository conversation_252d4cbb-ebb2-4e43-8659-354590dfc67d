package com.mira.user.service.front.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import com.mira.api.bluetooth.dto.tips.GetTipsReturnDTO;
import com.mira.api.bluetooth.dto.tips.Tip;
import com.mira.api.bluetooth.dto.tips.TipModel;
import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.bluetooth.enums.AlgorithmTipsTypeEnum;
import com.mira.api.bluetooth.enums.BBTModeErrorCodeEnum;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.OvulationTypeEnum;
import com.mira.api.bluetooth.provider.IAlgorithmProvider;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.consts.PhaseThresholdConst;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.diary.CustomLogConfigDTO;
import com.mira.api.user.dto.user.diary.UserDiaryMoodsDTO;
import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import com.mira.api.user.enums.PhaseEnum;
import com.mira.api.user.enums.TipsEmbedTypeEnum;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.enums.daily.DailyStatusSexEnum;
import com.mira.api.user.util.UserGoalUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BiomarkerEnum;
import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.consts.enums.WeightUnitEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.DaySuffixUtil;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.user.controller.vo.algorithm.AlgorithmExampleDataVO;
import com.mira.user.controller.vo.calendar.sub.LogsData;
import com.mira.user.controller.vo.chart.*;
import com.mira.user.controller.vo.chart.aggregation.ChartAggregationVO;
import com.mira.user.controller.vo.chart.aggregation.CycleAnalysis;
import com.mira.user.controller.vo.chart.aggregation.HormonesFlowAnalysis;
import com.mira.user.controller.vo.chart.aggregation.Recommendations;
import com.mira.user.controller.vo.cycle.CycleDataVO;
import com.mira.user.controller.vo.diary.UserDiaryMoodsVO;
import com.mira.user.dal.dao.*;
import com.mira.user.dal.entity.*;
import com.mira.user.dto.chart.TemperatureChartDTO;
import com.mira.user.dto.common.ManualHormoneDTO;
import com.mira.user.enums.chart.DayPhaseEnum;
import com.mira.user.exception.UserException;
import com.mira.user.handler.chart.IChartHormoneHandler;
import com.mira.user.handler.chart.hormone.ChartHormoneHandler;
import com.mira.user.service.front.IChartService;
import com.mira.user.service.manager.*;
import com.mira.user.service.util.ComparisonDataUtil;
import com.mira.web.properties.SysDictProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 图表接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ChartServiceImpl implements IChartService {
    private final AppUserTemperatureDAO appUserTemperatureDAO;
    private final AppUserPeriodDAO appUserPeriodDAO;
    private final AppUserDiaryDAO appUserDiaryDAO;
    private final AppUserDAO appUserDAO;
    private final AppUserInfoDAO appUserInfoDAO;
    private final UserProductTrialDAO userProductTrialDAO;
    private final SysTipsTemplateDAO sysTipsTemplateDAO;
    private final AppChartArticlesDAO appChartArticlesDAO;
    private final AppUserBindLogDAO appUserBindLogDAO;

    private final UserCustomLogManager userCustomLogManager;
    private final UserDiaryLogManager userDiaryLogManager;
    private final ChartManager chartManager;
    private final CacheManager cacheManager;
    private final AlgorithmCallManager algorithmCallManager;
    private final ManualDataManager manualDataManager;
    private final SysDictProperties sysDictProperties;

    private final IAlgorithmProvider algorithmProvider;
    private final IBluetoothProvider bluetoothProvider;
    private final ISsoProvider ssoProvider;

    @Override
    public AlgorithmExampleDataVO algorithmExampleData() {
        return BeanUtil.toBean(algorithmProvider.algorithmExampleData().getData(), AlgorithmExampleDataVO.class);
    }

    @Override
    public ChartLineVO analysisLine() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        return buildChartLineVO(userId, -1, "v4", loginUserInfoDTO);
    }

    @Override
    public V5ChartLineVO analysisLine(Integer index) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        return buildChartLineVO(userId, index, "v5", loginUserInfoDTO);
    }

    private V5ChartLineVO buildChartLineVO(Long userId, Integer index, String version,
                                           LoginUserInfoDTO loginUserInfoDTO) {
        // single and all
        boolean isSingle = -1 != index;
        // custom log config
        CustomLogConfigDTO customLogConfigDTO = userCustomLogManager.configInfo(userId);
        String tempUnit = customLogConfigDTO.getTempUnit();
        // get algorithm result
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);

        // 周期数据
        List<CycleDataDTO> cycleDataDTOList = new ArrayList<>();
        // 测试数据
        List<HormoneDTO> hormoneDTOList = new ArrayList<>();
        if (Objects.nonNull(algorithmResultDTO)) {
            cycleDataDTOList = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
            hormoneDTOList = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
        }

        List<ManualHormoneDTO> pendingDatas = manualDataManager.getPendingDatas(userId);

        // 温度数据
        List<TemperatureChartDTO> temperatureChartDTOList = buildChartTemperatureList(userId, tempUnit);
        // 移除错误数据和特定数据
        handleChartHormone(userId, hormoneDTOList);

        //这里需要把手动数据加进去

        // result vo
        V5ChartLineVO chartLineVO = new V5ChartLineVO();
        chartLineVO.setTempUnit(tempUnit);
        chartLineVO.setUserMode(UserGoalUtil.getUserGoalEnum(loginUserInfoDTO).getValue());
        chartLineVO.setBindLogFlag(buildBindLogFlag(userId, hormoneDTOList));
        // 周期数组
        buildCycleArray(cycleDataDTOList, chartLineVO);
        // today
        String nowDate = ZoneDateUtil.format(loginUserInfoDTO.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        // 曲线类型
        List<ChartLineVO.Legend> legendList = chartManager.buildLegend(hormoneDTOList, temperatureChartDTOList, pendingDatas);
        List<String> exampleLegendList = legendList.stream()
                                                   .filter(legend -> legend.getType() == 0)
                                                   .map(ChartLineVO.Legend::getCode)
                                                   .collect(Collectors.toList());
        chartLineVO.setLegend(legendList);
        // 当前周期的index
        CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(nowDate, cycleDataDTOList);
        Integer currentCycleIndex = currentCycleData.getCycle_index();
        chartLineVO.setCurrentCycleIndex(currentCycleIndex);
        // 返回所有周期数据，返回特定周期数据
        if (isSingle) {
            // 取当前周期
            if (index == -2) {
                index = currentCycleIndex;
            }
            // 指定的index前后是否有周期
            if (index == 0) {
                chartLineVO.setNextCycleIndex(cycleDataDTOList.size() > 1 ? index + 1 : null);
            } else {
                chartLineVO.setPrevCycleIndex(index - 1);
                chartLineVO.setNextCycleIndex(cycleDataDTOList.size() - 1 > index ? index + 1 : null);
            }
            // 指定index的周期数据
            final Integer cycleIndex = index;
            cycleDataDTOList = cycleDataDTOList.stream()
                                               .filter(cycleDataDTO -> Objects.equals(cycleDataDTO.getCycle_index(), cycleIndex))
                                               .collect(Collectors.toList());
            CycleDataDTO selectCycleDataDTO = cycleDataDTOList.get(0);
            // 该周期内的测试数据
            hormoneDTOList = CycleDataUtil.hormoneBySpecialCycle(selectCycleDataDTO, hormoneDTOList);
        }
        // example数据
        Map<String, List<TestDataDTO>> exampleDatasMap = new HashMap<>();
        if (version.equals("v4")) {
            exampleDatasMap = chartManager.buildExampleDatasMap(tempUnit, exampleLegendList);
        }
        // 周期和测试数据
        List<CycleDataVO> cycleDataVOList = chartManager.buildDataInCycle(nowDate, cycleDataDTOList, hormoneDTOList,
                temperatureChartDTOList, exampleLegendList, exampleDatasMap, pendingDatas);
        if (!isSingle) {
            chartManager.appendExtraTemperatureCycle(temperatureChartDTOList, cycleDataVOList, cycleDataDTOList.get(cycleDataDTOList.size() - 1));
        }
        chartLineVO.setDataInCycle(cycleDataVOList);
        chartLineVO.setSexDates(getSexDateList(userId));

        return chartLineVO;
    }

    private void handleChartHormone(Long userId, List<HormoneDTO> hormoneDTOList) {
        // 移除错误的数据
        hormoneDTOList.removeIf(hormoneDTO -> StringUtils.isNotBlank(hormoneDTO.getTest_results().getEcode())
                && !hormoneDTO.getTest_results().getEcode().startsWith("B"));
        // 部分用户屏蔽hcg测试结果
        List<Long> hideHcgValueUserIds = sysDictProperties.getHideHcgValueUserIds();
        if (hideHcgValueUserIds != null && hideHcgValueUserIds.contains(userId)) {
            hormoneDTOList.removeIf(
                    hormoneDTO -> hormoneDTO.getTest_results().getWand_type().equals(WandTypeEnum.HCG.getInteger())
            );
        }
    }

    private List<TemperatureChartDTO> buildChartTemperatureList(Long userId, String tempUnit) {
        List<TemperatureChartDTO> temperatureChartDTOList = new ArrayList<>();
        List<AppUserTemperatureEntity> userTemperatureEntityList = appUserTemperatureDAO.listByUserId(userId);

        if (CollectionUtils.isNotEmpty(userTemperatureEntityList)) {
            for (AppUserTemperatureEntity appUserTemperatureEntity : userTemperatureEntityList) {
                String modeError = appUserTemperatureEntity.getModeError();
                if (!BBTModeErrorCodeEnum.NORMAL.getCode().equals(modeError)) {
                    continue;
                }
                TemperatureChartDTO temperatureChartDTO = new TemperatureChartDTO();
                if (TempUnitEnum.C.getValue().equals(tempUnit)) {
                    temperatureChartDTO.setValue(appUserTemperatureEntity.getTempC().floatValue());
                } else {
                    temperatureChartDTO.setValue(appUserTemperatureEntity.getTempF().floatValue());
                }
                BeanUtil.copyProperties(appUserTemperatureEntity, temperatureChartDTO);
                temperatureChartDTO.setTestTime(appUserTemperatureEntity.getTempTime());
                temperatureChartDTOList.add(temperatureChartDTO);
            }
        }
        return temperatureChartDTOList;
    }

    private void buildCycleArray(List<CycleDataDTO> cycleDataDTOList, V5ChartLineVO chartLineVO) {
        List<V5ChartLineVO.Cycle> cycles = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOList) {
            V5ChartLineVO.Cycle cycle = new V5ChartLineVO.Cycle();
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle - 1, DatePatternConst.DATE_PATTERN);
            cycle.setCycleIndex(cycleDataDTO.getCycle_index());
            cycle.setDatePeriodStart(datePeriodStart);
            cycle.setDateCycleEnd(dateCycleEnd);
            cycles.add(cycle);
        }
        chartLineVO.setCycles(cycles);
    }

    private List<String> getSexDateList(Long userId) {
        List<AppUserDiaryEntity> appUserDiaryEntityList = appUserDiaryDAO.listSexRecordsByUserId(userId);
        List<String> sexDateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(appUserDiaryEntityList)) {
            appUserDiaryEntityList.forEach(appUserDiaryEntity -> {
                if (DailyStatusSexEnum.C.getValue().equals(appUserDiaryEntity.getSex())
                        || DailyStatusSexEnum.S.getValue().equals(appUserDiaryEntity.getSex())) {
                    sexDateList.add(appUserDiaryEntity.getDiaryDayStr());
                }
            });
        }
        return sexDateList;
    }

    private Integer buildBindLogFlag(Long userId, List<HormoneDTO> hormoneDTOLis) {
        if (CollectionUtils.isNotEmpty(hormoneDTOLis)) {
            return 1;
        }
        List<AppUserBindLogEntity> bindLogEntities = appUserBindLogDAO.list10BindLog(userId);
        if (CollectionUtils.isEmpty(bindLogEntities)) {
            return 0;
        }
        return 1;
    }

    @Override
    public SysTipsVO tips(Integer tipsType) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String email = loginInfo.getUsername();
        String nowDate = ZoneDateUtil.format(loginInfo.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);

        if (isQcUser(email)) {
            return new SysTipsVO();
        }

        // beta 信息
        UserProductTrialEntity userProductTrial = userProductTrialDAO.getByEmail(email);
        Integer trialFlag = userProductTrial != null ? userProductTrial.getFlag() : null;

        return buildSystipsDTO(getTipsReturnDTO(userId, trialFlag, tipsType), trialFlag, nowDate);
    }

    private boolean isQcUser(String email) {
        String qcEmails = sysDictProperties.getQcEmails();
        if (qcEmails.contains(email)) {
            log.info("qc user:{}, no tips", email);
            return true;
        }
        return false;
    }

    @Override
    public SysTipsVO tips(Long userId) {
        AppUserEntity appUser = appUserDAO.getById(userId);
        if (appUser == null) {
            return new SysTipsVO();
        }
        if (isQcUser(appUser.getEmail())) {
            return new SysTipsVO();
        }

        String nowDate = ZoneDateUtil.format(appUser.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);

        // beta 信息
        UserProductTrialEntity userProductTrial = userProductTrialDAO.getByEmail(appUser.getEmail());
        Integer trialFlag = userProductTrial != null ? userProductTrial.getFlag() : null;

        return buildSystipsDTO(getTipsReturnDTO(userId, trialFlag, 1), trialFlag, nowDate);
    }

    private GetTipsReturnDTO getTipsReturnDTO(Long userId, Integer trialFlag, Integer tipsType) {
        // tips 算法结果
        GetTipsReturnDTO getTipsReturnData = cacheManager.getTipsAlgorithmData(userId);
        if (getTipsReturnData == null) {
            AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
            if (appUserPeriodEntity == null) {
                throw new UserException("user period not exist");
            }
            // bbt 数据
            List<AppUserTemperatureEntity> temperatureEntityList = appUserTemperatureDAO.listByUserId(userId);
            temperatureEntityList.removeIf(temperature -> 1 == temperature.getDeleted());
            getTipsReturnData = algorithmCallManager.tips(appUserPeriodEntity, temperatureEntityList,
                    ssoProvider.getUserLoginInfo(userId).getData(), trialFlag, tipsType);
        }
        return getTipsReturnData;
    }

    private SysTipsVO buildSystipsDTO(GetTipsReturnDTO getTipsReturnData,
                                      Integer trialFlag,
                                      String nowDate) {
        // result vo
        SysTipsVO sysTipsVO = new SysTipsVO();
        List<Tip> tips = getTipsReturnData.getTips();
        List<SysTipsVO.Tip> tipVOs = new ArrayList<>();

        // 取当前周期7天内数据
        AlgorithmResultDTO algorithmResult = cacheManager.getCacheAlgorithmResult(getTipsReturnData.getUser_id());
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResult.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDTOS = JsonUtil.toArray(algorithmResult.getHormoneData(), HormoneDTO.class);
        CycleDataDTO currentCycle = CycleDataUtil.getCurrentCycleData(nowDate, cycleDataDTOS);
        CycleDataVO cycleDataVO = ComparisonDataUtil.hormoneByCurrentCycle(currentCycle, hormoneDTOS);

        // tips template
        List<Integer> modelIds = tips.stream().map(Tip::getModel_id).collect(Collectors.toList());
        Map<Long, SysTipsTemplateEntity> sysTipsTemplateEntityMap = sysTipsTemplateDAO.getBatchByModelIdAndLanguage(modelIds)
                                                                                      .stream().collect(Collectors.toMap(SysTipsTemplateEntity::getModelId, Function.identity()));

        for (Tip tip : tips) {
            SysTipsTemplateEntity sysTipsTemplateEntity = sysTipsTemplateEntityMap.get(tip.getModel_id().longValue());
            if (sysTipsTemplateEntity == null) {
                continue;
            }
            SysTipsTemplateVO sysTipsTemplate = BeanUtil.toBean(sysTipsTemplateEntity, SysTipsTemplateVO.class);
            SysTipsVO.Tip tipVO = new SysTipsVO.Tip();
            tipVO.setBiomarker(tip.getBiomarker());
            tipVO.setModel_id(tip.getModel_id());
            tipVO.setButton(getTipsReturnData.getButton());

            String template = buildTemplate(tip, sysTipsTemplate.getTemplate(), trialFlag);
            if (template.contains("#title")) {
                sysTipsVO.setType(1);
                tipVO.setTitle(getMatchString(template, "(?s)#title (.*?)#"));
                tipVO.setTemplate(getMatchString(template, "(?s)#content (.*?)#"));
                tipVO.setImage(getMatchString(template, "(?s)#icon (.*?)#"));
                tipVO.setNote(getMatchString(template, "(?s)#note (.*?)#"));
            } else {
                tipVO.setTemplate(template);
            }

            if (template.contains("youtube")) {
                tipVO.setEmbedType(TipsEmbedTypeEnum.VIDEO.getType());
            }
            setEmbedType(tipVO, cycleDataVO);

            tipVOs.add(tipVO);
        }
        tipVOs.sort(Comparator.comparing(SysTipsVO.Tip::getBiomarker, (a, b) -> {
            if (a >= 0 && b >= 0) {
                // if both numbers are non-negative, asc
                return Integer.compare(a, b);
            } else if (a < 0 && b < 0) {
                // if both numbers are negative, desc
                return Integer.compare(b, a);
            } else {
                // if one is negative and another is non-negative, the negative number should come first
                return a < 0 ? -1 : 1;
            }
        }));
        sysTipsVO.setTips(tipVOs);

        SysTipsVO.Disclaimer disclaimer = new SysTipsVO.Disclaimer();
        disclaimer.setType(1);
        disclaimer.setContent(sysDictProperties.getTipsDisclaimer());
        sysTipsVO.setDisclaimer(disclaimer);

        return sysTipsVO;
    }

    private String buildTemplate(Tip tip, String template, Integer trialFlag) {
        // 匹配大括号
        String braceRegex = "\\{([^}]*)\\}";
        Matcher matcher = Pattern.compile(braceRegex).matcher(template);
        List<String> matchedStr = new ArrayList<>();
        while (matcher.find()) {
            String group = matcher.group();
            matchedStr.add(group);
        }

        List<String> replaceStr = new ArrayList<>();

        for (TipModel tipModelData : tip.getModel_data()) {
            String dataType = tipModelData.getData_type();
            String dataValue = tipModelData.getData_value();
            AlgorithmTipsTypeEnum algorithmTipsTypeEnum = AlgorithmTipsTypeEnum.get(dataType);
            switch (Objects.requireNonNull(algorithmTipsTypeEnum)) {
                case INT:
                case FLOAT:
                case STRING:
                    replaceStr.add(dataValue);
                    break;
                case WAND_TYPE_MAP:
                case WAND_LINK_MAP:
                case CHANGE_MAP:
                case LEVEL_MAP:
                    replaceStr.add(algorithmTipsTypeEnum.getMap().get(dataValue));
                    break;
                case WAND_INFO_LINK_MAP:
                    replaceStr.add(algorithmTipsTypeEnum.getMap().get("0"));
                    break;
                case DATA_ID:
                    String dataView = getDataView(dataValue, trialFlag);
                    replaceStr.add(dataView);
                    break;
                default:
                    replaceStr.add(" ");
                    break;
            }
        }

        if (matchedStr.size() != replaceStr.size()) {
            throw new UserException("template error:" + tip.getModel_id());
        }

        for (int i = 0; i < matchedStr.size(); i++) {
            template = template.replace(matchedStr.get(i), replaceStr.get(i));
        }

        if (template.contains("logSymptoms.html")) {
            template = template.replaceAll("logSymptoms.html",
                    "logSymptoms.html?date=".concat(ZoneDateUtil.format(ContextHolder.get(HeaderConst.TIME_ZONE), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN)));
        }

        return template;
    }

    private String getMatchString(String template, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(template);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }

    private String getDataView(String dataValue, Integer trialFlag) {
        String[] split = dataValue.split(":");
        String dataId = split[0];
        String bioMarker = split[1];

        // bbt
        if (bioMarker.equalsIgnoreCase("BBT")) {
            AppUserTemperatureEntity userTemperatureEntity = appUserTemperatureDAO.getOneBySql(Long.valueOf(dataId));
            return userTemperatureEntity.getTempF().setScale(2, RoundingMode.HALF_UP).toString();
        }

        // fertility device
        WandTestBiomarkerDTO biomarkerDTO = bluetoothProvider.getWandTestBiomarkerData(Long.valueOf(dataId), bioMarker, trialFlag).getData();
        String dataView = "";
        if (biomarkerDTO != null) {
            dataView = biomarkerDTO.getTestValue();
        }
        return dataView;
    }

    private void setEmbedType(SysTipsVO.Tip tipVO, CycleDataVO cycleDataVO) {
        // 存在2条及以上测试数据，embedType=3
        int showChartCount = 2;

        BiomarkerEnum biomarkerEnum = BiomarkerEnum.get(tipVO.getBiomarker());
        if (biomarkerEnum == null) {
            return;
        }

        boolean showChart = false;
        switch (biomarkerEnum) {
            case LH:
                if (cycleDataVO.getLhDatas().size() >= showChartCount) {
                    showChart = true;
                }
                break;
            //            case HCG:
            //                if (cycleDataVO.getHcgDatas().size() >= showChartCount) {
            //                    showChart = true;
            //                }
            //                break;
            case E3G:
                if (cycleDataVO.getE3gDatas().size() >= showChartCount) {
                    showChart = true;
                }
                break;
            case PDG:
                if (cycleDataVO.getPdgDatas().size() >= showChartCount) {
                    showChart = true;
                }
                break;
            //            case HCG_QUALITATIVE:
            //                if (cycleDataVO.getHcg2Datas().size() >= showChartCount) {
            //                    showChart = true;
            //                }
            //                break;
            //            case FSH:
            //                if (cycleDataVO.getFshDatas().size() >= showChartCount) {
            //                    showChart = true;
            //                }
            //                break;
        }

        if (showChart) {
            tipVO.setEmbedType(TipsEmbedTypeEnum.CHART.getType());
        }
    }

    @Override
    public SysTipsTemplateVO tipsTemplate(Integer modelId) {
        String localLanguage = ContextHolder.get(HeaderConst.LOCAL_LANGUAGE);

        SysTipsTemplateEntity sysTipsTemplateEntity = sysTipsTemplateDAO.getByModelIdAndLanguage(modelId, localLanguage);
        return BeanUtil.toBean(sysTipsTemplateEntity, SysTipsTemplateVO.class);
    }

    @Override
    public GetTipsReturnVO tipsResult() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        return BeanUtil.toBean(cacheManager.getTipsAlgorithmData(userId), GetTipsReturnVO.class);
    }

    @Override
    public void setTipsCache(GetTipsReturnDTO getTipsReturnDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        cacheManager.cacheTipsAlgorithmData(userId, getTipsReturnDTO);
    }

    @Override
    public ChartAggregationVO aggregation(String day) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        // check param
        if (StringUtils.isBlank(day)) {
            day = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        }

        // result
        ChartAggregationVO chartAggregationVO = new ChartAggregationVO();
        // select cycle and hormones
        List<CycleDataDTO> cycleDataList = new ArrayList<>();
        List<HormoneDTO> hormoneList = new ArrayList<>();
        AlgorithmResultDTO algorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        if (Objects.nonNull(algorithmResult)) {
            cycleDataList = JsonUtil.toArray(algorithmResult.getCycleData(), CycleDataDTO.class);
            hormoneList = JsonUtil.toArray(algorithmResult.getHormoneData(), HormoneDTO.class);
        }
        CycleDataDTO selectCycleData = CycleDataUtil.getCurrentCycleData(day, cycleDataList);
        List<HormoneDTO> selectHormoneList = CycleDataUtil.hormoneBySpecialCycle(selectCycleData, hormoneList);
        // Cycle Analysis
        Integer cycleStatus = selectCycleData.getCycle_status();
        if (CycleStatusEnum.REAL_CYCLE.getStatus() == cycleStatus
                || CycleStatusEnum.FORECAST_CYCLE.getStatus() == cycleStatus) {
            chartAggregationVO.setCycleAnalysis(aggregationCycleAnalysis(day, selectCycleData));
        }
        // Hormones Flow Anaylysis
        chartAggregationVO.setHormonesFlowAnalysis(aggregationHormonesAnalysis(userId, day, selectHormoneList, selectCycleData));
        // Logs
        chartAggregationVO.setLogs(buildDailyLog(userId, day));
        // Recommendations
        chartAggregationVO.setRecommendations(aggregationRecommendations(userId));

        return chartAggregationVO;
    }

    private CycleAnalysis aggregationCycleAnalysis(String day, CycleDataDTO selectCycleData) {
        CycleAnalysis cycleAnalysis = new CycleAnalysis();

        // Phase
        buildCyclePhase(day, selectCycleData, cycleAnalysis);
        // Cycle day
        int dayCD = CycleDataUtil.getCDByCycle(day, selectCycleData);
        if (dayCD == -1) {
            return cycleAnalysis;
        }
        dayCD += 1;
        String daySuffix = DaySuffixUtil.getDaySuffix(dayCD);
        cycleAnalysis.setCycleDay(dayCD + "-" + daySuffix);
        // Conception chances
        List<Float> fertilityScoreList = selectCycleData.getFertility_score_list();
        if (CollectionUtils.isNotEmpty(fertilityScoreList)) {
            int fertilityScore = fertilityScoreList.get(dayCD - 1).intValue();
            String scoreDesc = fertilityScore + "/10" + (fertilityScore < 6 ? " Low" : " High");
            cycleAnalysis.setFertilityScore(scoreDesc);
        }
        // Fertile window
        buildFertileWindow(day, selectCycleData, cycleAnalysis);
        // Ovulation
        buildOvulation(day, selectCycleData, cycleAnalysis);

        return cycleAnalysis;
    }

    private void buildCyclePhase(String day, CycleDataDTO selectCycleData,
                                 CycleAnalysis cycleAnalysis) {
        Integer cycleStatus = selectCycleData.getCycle_status();
        CycleAnalysis.Phase phase = new CycleAnalysis.Phase();
        PhaseEnum phaseEnum = CycleDataUtil.getPhase(day, selectCycleData);
        DayPhaseEnum dayPhaseEnum = null;
        boolean futureCycle = LocalDateUtil.minusToDay(day, selectCycleData.getDate_period_start()) < 0;
        boolean forecast = CycleStatusEnum.FORECAST_CYCLE.getStatus() == cycleStatus;
        switch (phaseEnum) {
            case FOLLICULAR_PHASE:
                if (CycleDataUtil.inPeriod(day, selectCycleData)) {
                    dayPhaseEnum = forecast ? DayPhaseEnum.PERIOD_PREDICATED : DayPhaseEnum.PERIOD_COMFIRMED;
                } else {
                    dayPhaseEnum = futureCycle ? DayPhaseEnum.FOLLICULAR_PREDICATED : DayPhaseEnum.FOLLICULAR_COMFIRMED;
                }
                break;
            case FERTILE_WINDOW:
                OvulationTypeEnum dayOvulationType = CycleDataUtil.getOvulationType(day, selectCycleData);
                // 当天是Ovulation显示Ovulation逻辑，不是，显示FW逻辑
                if (dayOvulationType == null) {
                    OvulationTypeEnum cycleOvulationType = OvulationTypeEnum.get(selectCycleData.getOvulation_type());
                    if (cycleOvulationType == null) {
                        dayPhaseEnum = forecast ? DayPhaseEnum.FERTILE_WINDOW_PREDICATED : DayPhaseEnum.FERTILE_WINDOW_COMFIRMED;
                        break;
                    }
                    dayPhaseEnum = cycleOvulationType == OvulationTypeEnum.PREDICTED
                            ? DayPhaseEnum.FERTILE_WINDOW_PREDICATED : DayPhaseEnum.FERTILE_WINDOW_COMFIRMED;
                } else {
                    switch (dayOvulationType) {
                        case PREDICTED:
                            dayPhaseEnum = DayPhaseEnum.OVULATION_PREDICATED;
                            break;
                        case DETECTED:
                            dayPhaseEnum = DayPhaseEnum.OVULATION_DETECTED;
                            break;
                        case PREDICTED_CONFIRMED:
                            dayPhaseEnum = DayPhaseEnum.OVULATION_PREDICTED_CONFIRMED;
                            break;
                        case DETECTED_CONFIRMED:
                            dayPhaseEnum = DayPhaseEnum.OVULATION_DETECTED_CONFIRMED;
                            break;
                        case CUSTOM:
                            dayPhaseEnum = DayPhaseEnum.OVULATION_MANUAL;
                    }
                }
                break;
            case LUTEAL_PHASE:
                dayPhaseEnum = futureCycle ? DayPhaseEnum.LUTEAL_PREDICATED : DayPhaseEnum.LUTEAL_CONFIRMED;
        }

        if (dayPhaseEnum != null) {
            phase.setCode(dayPhaseEnum.getCode());
            phase.setValue(dayPhaseEnum.getValue());
            cycleAnalysis.setPhase(phase);
        }
    }

    private void buildFertileWindow(String day, CycleDataDTO selectCycleData,
                                    CycleAnalysis cycleAnalysis) {
        String fertileWindowDesc;
        int minusToDay = LocalDateUtil.minusToDay(day, selectCycleData.getDate_FW_start());
        if (CycleDataUtil.inFertileWindow(day, selectCycleData)) {
            fertileWindowDesc = "day " + (minusToDay + 1);
        } else {
            if (minusToDay < 0) {
                fertileWindowDesc = "in " + Math.abs(minusToDay) + " days";
            } else {
                int passedDays = LocalDateUtil.minusToDay(day, selectCycleData.getDate_FW_end());
                fertileWindowDesc = (passedDays + 1) + " days have passed";
            }
        }
        cycleAnalysis.setFertileWindow(fertileWindowDesc);
    }

    private void buildOvulation(String day, CycleDataDTO selectCycleData,
                                CycleAnalysis cycleAnalysis) {
        CycleAnalysis.Ovulation ovulation = new CycleAnalysis.Ovulation();
        int ovulationMinusDay = LocalDateUtil.minusToDay(day, selectCycleData.getDate_ovulation());
        // future
        if (ovulationMinusDay < 0) {
            ovulation.setCode(OvulationTypeEnum.PREDICTED.getCode());
            ovulation.setValue("In " + Math.abs(ovulationMinusDay) + " days - predicted");

        } else if (ovulationMinusDay == 0) { // current day
            String value = "On this day - ";
            OvulationTypeEnum ovulationType = CycleDataUtil.getOvulationType(day, selectCycleData);
            if (ovulationType != null) {
                switch (ovulationType) {
                    case DETECTED:
                        value = value + "detected";
                        break;
                    case PREDICTED_CONFIRMED:
                        value = value + "predicted & confirmed";
                        break;
                    case DETECTED_CONFIRMED:
                        value = value + "detected & confirmed";
                        break;
                    case CUSTOM:
                        value = value + "manually selected";
                        break;
                    default:
                        value = value + "predicted";
                }
                ovulation.setCode(ovulationType.getCode());
                ovulation.setValue(value);
            }

        } else { // old day
            String value = ovulationMinusDay + " days ago - ";
            OvulationTypeEnum ovulationType = OvulationTypeEnum.get(selectCycleData.getOvulation_type());
            if (ovulationType != null) {
                switch (ovulationType) {
                    case DETECTED:
                        value = value + "detected";
                        break;
                    case PREDICTED_CONFIRMED:
                        value = value + "predicted & confirmed";
                        break;
                    case DETECTED_CONFIRMED:
                        value = value + "detected & confirmed";
                        break;
                    default:
                        value = value + "predicted";
                }
                ovulation.setCode(ovulationType.getCode());
                ovulation.setValue(value);
            } else {
                ovulation.setCode(OvulationTypeEnum.PREDICTED.getCode());
                ovulation.setValue(value + "predicted");
            }
        }
        cycleAnalysis.setOvulation(ovulation);
    }

    private HormonesFlowAnalysis aggregationHormonesAnalysis(Long userId, String day,
                                                             List<HormoneDTO> hormoneDTOList,
                                                             CycleDataDTO cycleDataDTO) {
        // 移除错误数据和特定数据
        handleChartHormone(userId, hormoneDTOList);
        // 筛选day的数据
        List<HormoneDTO> filterHormoneList = hormoneDTOList.stream()
                                                           .filter(hormone -> day.equals(hormone.getTest_time().substring(0, 10)))
                                                           .collect(Collectors.toList());
        // 实际数据
        CycleDataVO cycleDataVO = new CycleDataVO();
        for (HormoneDTO hormoneDTO : filterHormoneList) {
            HormoneDTO.TestResult testResults = hormoneDTO.getTest_results();
            IChartHormoneHandler chartHormoneHandler = ChartHormoneHandler.get(testResults.getWand_type());
            if (Objects.nonNull(chartHormoneHandler)) {
                chartHormoneHandler.handle(hormoneDTO, cycleDataVO, null);
            }
        }
        // 温度单位
        CustomLogConfigDTO customLogConfigDTO = userCustomLogManager.configInfo(userId);
        String tempUnit = customLogConfigDTO.getTempUnit();
        // 温度数据
        List<TemperatureChartDTO> temperatureChartDTOList = buildChartTemperatureList(userId, tempUnit);
        temperatureChartDTOList = temperatureChartDTOList.stream()
                                                         .filter(temperature -> day.equals(temperature.getTestTime().substring(0, 10)))
                                                         .collect(Collectors.toList());
        // set
        HormonesFlowAnalysis hormonesFlowAnalysis = new HormonesFlowAnalysis();

        hormonesFlowAnalysis.setLhDatas(cycleDataVO.getLhDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()));
        hormonesFlowAnalysis.setE3gDatas(cycleDataVO.getE3gDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()));
        hormonesFlowAnalysis.setHcgDatas(cycleDataVO.getHcgDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()));
        hormonesFlowAnalysis.setPdgDatas(cycleDataVO.getPdgDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()));
        hormonesFlowAnalysis.setHcg2Datas(cycleDataVO.getHcg2Datas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()));
        hormonesFlowAnalysis.setFshDatas(cycleDataVO.getFshDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()));
        hormonesFlowAnalysis.setTemperatureDatas(temperatureChartDTOList);
        hormonesFlowAnalysis.setTempUnit(tempUnit);
        // 排序
        cycleDataVO.getLhDatas().sort(Comparator.comparing(TestDataDTO::getTestTime).reversed());
        cycleDataVO.getE3gDatas().sort(Comparator.comparing(TestDataDTO::getTestTime).reversed());
        cycleDataVO.getHcgDatas().sort(Comparator.comparing(TestDataDTO::getTestTime).reversed());
        cycleDataVO.getPdgDatas().sort(Comparator.comparing(TestDataDTO::getTestTime).reversed());
        cycleDataVO.getHcg2Datas().sort(Comparator.comparing(TestDataDTO::getTestTime).reversed());
        cycleDataVO.getFshDatas().sort(Comparator.comparing(TestDataDTO::getTestTime).reversed());
        cycleDataVO.getTemperatureDatas().sort(Comparator.comparing(TemperatureChartDTO::getTestTime).reversed());
        // 阈值上下限
        buildWandUpperLowerLimit(hormonesFlowAnalysis, day, cycleDataDTO);
        // lh peak
        String dateLhSurge = cycleDataDTO.getDate_LH_surge();
        hormonesFlowAnalysis.setLhPeak("Not yet detected");
        if (StringUtils.isNotBlank(dateLhSurge)) {
            int minusToDay = LocalDateUtil.minusToDay(day, dateLhSurge);
            hormonesFlowAnalysis.setLhPeak(minusToDay == 0 ? "Detected today"
                    : (minusToDay < 0 ? "Not yet detected" : "Detected " + minusToDay + " days ago"));
        }
        // pdg rise
        List<String> datePdgRise = cycleDataDTO.getDate_PDG_rise();
        if (CollectionUtils.isNotEmpty(datePdgRise)) {
            String riseDate = datePdgRise.get(0);
            int minusToDay = LocalDateUtil.minusToDay(day, riseDate);
            hormonesFlowAnalysis.setPdgRise(minusToDay >= 0 ? "Confirmed" : "Not yet confirmed");
        } else {
            hormonesFlowAnalysis.setPdgRise("Not yet confirmed");
        }

        return hormonesFlowAnalysis;
    }

    private void buildWandUpperLowerLimit(HormonesFlowAnalysis hormonesFlowAnalysis,
                                          String day, CycleDataDTO selectCycleData) {
        PhaseEnum phaseEnum = CycleDataUtil.getPhase(day, selectCycleData);
        switch (phaseEnum) {
            case FOLLICULAR_PHASE:
                hormonesFlowAnalysis.setLhLimit(
                        new Float[]{PhaseThresholdConst.LH_FOLLICULAR_PHASE_LOWER, PhaseThresholdConst.LH_FOLLICULAR_PHASE_UPPER});
                hormonesFlowAnalysis.setE3gLimit(
                        new Float[]{PhaseThresholdConst.E3G_FOLLICULAR_PHASE_LOWER, PhaseThresholdConst.E3G_FOLLICULAR_PHASE_UPPER});
                hormonesFlowAnalysis.setPdgLimit(
                        new Float[]{PhaseThresholdConst.PDG_FOLLICULAR_PHASE_LOWER, PhaseThresholdConst.PDG_FOLLICULAR_PHASE_UPPER});
                hormonesFlowAnalysis.setFshLimit(
                        new Float[]{PhaseThresholdConst.FSH_FOLLICULAR_PHASE_LOWER, PhaseThresholdConst.FSH_FOLLICULAR_PHASE_UPPER});
                break;
            case FERTILE_WINDOW:
                hormonesFlowAnalysis.setLhLimit(
                        new Float[]{PhaseThresholdConst.LH_OVULATORY_PHASE_LOWER, PhaseThresholdConst.LH_OVULATORY_PHASE_UPPER});
                hormonesFlowAnalysis.setE3gLimit(
                        new Float[]{PhaseThresholdConst.E3G_OVULATORY_PHASE_LOWER, PhaseThresholdConst.E3G_OVULATORY_PHASE_UPPER});
                hormonesFlowAnalysis.setPdgLimit(
                        new Float[]{PhaseThresholdConst.PDG_OVULATORY_PHASE_LOWER, PhaseThresholdConst.PDG_OVULATORY_PHASE_UPPER});
                hormonesFlowAnalysis.setFshLimit(
                        new Float[]{PhaseThresholdConst.FSH_OVULATORY_PHASE_LOWER, PhaseThresholdConst.FSH_OVULATORY_PHASE_UPPER});
                break;
            case LUTEAL_PHASE:
                hormonesFlowAnalysis.setLhLimit(
                        new Float[]{PhaseThresholdConst.LH_LUTEAL_PHASE_LOWER, PhaseThresholdConst.LH_LUTEAL_PHASE_UPPER});
                hormonesFlowAnalysis.setE3gLimit(
                        new Float[]{PhaseThresholdConst.E3G_LUTEAL_PHASE_LOWER, PhaseThresholdConst.E3G_LUTEAL_PHASE_UPPER});
                hormonesFlowAnalysis.setPdgLimit(
                        new Float[]{PhaseThresholdConst.PDG_LUTEAL_PHASE_LOWER, PhaseThresholdConst.PDG_LUTEAL_PHASE_UPPER});
                hormonesFlowAnalysis.setFshLimit(
                        new Float[]{PhaseThresholdConst.FSH_LUTEAL_PHASE_LOWER, PhaseThresholdConst.FSH_LUTEAL_PHASE_UPPER});
        }
    }

    protected LogsData buildDailyLog(Long userId, String dateStr) {
        LogsData logsData = new LogsData();

        AppUserDiaryEntity appUserDiaryEntity = appUserDiaryDAO.getByUserIdAndDayStr(userId, dateStr);
        List<UserSymptomDTO> userSymptomDTOS = userDiaryLogManager.listUserSymptomDTO(userId, dateStr);
        List<TemperatureDTO> temperatureDTOS = userDiaryLogManager.listTemperatureDTO(userId, dateStr);
        UserDiaryMoodsDTO userDiaryMoodsDTO = userDiaryLogManager.getUserDiaryMoodsDTO(userId, dateStr);

        if (ObjectUtils.isEmpty(appUserDiaryEntity)
                && CollectionUtils.isEmpty(userSymptomDTOS)
                && CollectionUtils.isEmpty(temperatureDTOS)
                && ObjectUtils.isEmpty(userDiaryMoodsDTO)) {
            return null;
        }

        CustomLogConfigDTO customLogConfigDTO = userCustomLogManager.configInfo(userId);
        String weightUnit = customLogConfigDTO.getWeightUnit();
        logsData.setWeightUnit(weightUnit);
        String tempUnit = customLogConfigDTO.getTempUnit();
        logsData.setTempUnit(tempUnit);
        logsData.setTemperatures(temperatureDTOS);

        if (ObjectUtils.isNotEmpty(appUserDiaryEntity)) {
            String sex = appUserDiaryEntity.getSex();
            logsData.setSex(sex);
            logsData.setNotes(appUserDiaryEntity.getNotes());
            logsData.setMucusType(appUserDiaryEntity.getMucusType());
            logsData.setMucusFlow(appUserDiaryEntity.getMucusFlow());
            logsData.setPregnant(appUserDiaryEntity.getPregnant());
            logsData.setOpk(appUserDiaryEntity.getOpk());
            logsData.setFlowAndSpotting(appUserDiaryEntity.getFlowAndSpotting());
            logsData.setCervicalPosition(appUserDiaryEntity.getCervicalPosition());
            logsData.setCervicalFirmness(appUserDiaryEntity.getCervicalFirmness());
            logsData.setCervicalOpenness(appUserDiaryEntity.getCervicalOpenness());

            BigDecimal weightK = appUserDiaryEntity.getWeightK();
            if (weightK != null) {
                if (WeightUnitEnum.K.getValue().equals(weightUnit)) {
                    logsData.setWeightValue(appUserDiaryEntity.getWeightK());
                } else {
                    logsData.setWeightValue(appUserDiaryEntity.getWeightL());
                }
            }
        }

        Boolean medicationsConfig = customLogConfigDTO.getMedications();
        if (medicationsConfig) {
            logsData.setMedications(userDiaryLogManager.listUserMedicine(userId, dateStr));
        }
        logsData.setSymptoms(userSymptomDTOS);
        logsData.setAppUserDiaryMoodsVO(BeanUtil.toBean(userDiaryMoodsDTO, UserDiaryMoodsVO.class));

        return logsData;
    }

    private Recommendations aggregationRecommendations(Long userId) {
        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(userId);
        Integer goalStatus = appUserInfo.getGoalStatus();
        UserGoalEnum userGoalEnum = UserGoalEnum.get(goalStatus);
        if (userGoalEnum == null) {
            throw new UserException("The 'Fertility goal' data does not exist.");
        }
        if (UserGoalEnum.TTC != userGoalEnum) {
            goalStatus = -1;
        }
        AppChartArticlesEntity appChartArticlesEntity = appChartArticlesDAO.getByGoalStatus(goalStatus);
        return BeanUtil.toBean(appChartArticlesEntity, Recommendations.class);
    }

    @Override
    public int cycleIndex(String date) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        // check param
        if (StringUtils.isBlank(date)) {
            date = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        }

        try {
            AlgorithmResultDTO algorithmResult = cacheManager.getCacheAlgorithmResult(userId);
            if (Objects.nonNull(algorithmResult)) {
                List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResult.getCycleData(), CycleDataDTO.class);
                CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(date, cycleDataDTOS);
                return currentCycleData.getCycle_index();
            }
        } catch (Exception e) {
            log.error("get cycle index error, user:{}", userId, e);
        }
        return -1;
    }
}
