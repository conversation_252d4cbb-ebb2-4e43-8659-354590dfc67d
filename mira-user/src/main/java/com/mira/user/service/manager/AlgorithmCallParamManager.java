package com.mira.user.service.manager;

import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.StringListUtil;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import com.mira.user.dal.entity.AppUserPeriodEntity;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import com.mira.user.service.util.CallEditPeriodUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2025-01-09
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class AlgorithmCallParamManager {
    private final AppUserPeriodDAO appUserPeriodDAO;
    private final CacheManager cacheManager;
    private final AlgorithmCallManager algorithmCallManager;
    private final ISsoProvider ssoProvider;

    public void callAlgorithm(String email, Long userId,LoginUserInfoDTO loginUserInfoDTO,
                              AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        // 编辑经期后变成老用户
        appUserPeriodEntity.setFresh(1);
        appUserPeriodDAO.updateById(appUserPeriodEntity);
        cacheManager.deleteUserDetailCache(userId);
        List<Long> dbPeriodList = StringListUtil.strToLongList(appUserPeriodEntity.getPeriods(), ",");

        // build user period data
        List<UserPeriodDataDTO> userPeriodDataDTOList = CallEditPeriodUtil.buildUserPeriodDataDTOS(loginInfo.getTimeZone(),
                dbPeriodList, appUserPeriodEntity.getCutPoints(), appUserPeriodEntity.getAvgLenPeriod());

        // save last period data, context
        ContextHolder.put("lastPeriodData", CollectionUtils.isEmpty(userPeriodDataDTOList) ? null : userPeriodDataDTOList.get(userPeriodDataDTOList.size() - 1));

        // build period info

        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO,
                appUserPeriodEntity, userPeriodDataDTOList);

        // call algorithm
        PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(loginInfo.getTimeZone(),
                email, appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
        algorithmCallManager.editPeriod(prepareEditPeriodDTO, algorithmRequestTypeEnum);
    }
}
