package com.mira.user.service.manager;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.tips.GetTipsReturnDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.provider.IAlgorithmProvider;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.redis.cache.RedisComponent;
import com.mira.user.dal.dao.AppUserReminderDAO;
import com.mira.user.dal.entity.AppUserReminderEntity;
import com.mira.user.enums.home.HomeActionButtonCodeEnum;
import com.mira.user.exception.UserException;
import com.mira.user.properties.CacheExpireProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * Cache manager
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CacheManager {
    @Resource
    private AppUserReminderDAO appUserReminderDAO;
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private CacheExpireProperties cacheExpireProperties;
    @Resource
    private IAlgorithmProvider algorithmProvider;
    @Resource
    private ISsoProvider ssoProvider;
    @Resource
    private ExtendPeriodManager extendPeriodManager;

    /**
     * 缓存tips算法结果
     *
     * @param userId           用户编号
     * @param getTipsReturnDTO tips算法结果
     */
    public void cacheTipsAlgorithmData(Long userId, GetTipsReturnDTO getTipsReturnDTO) {
        String cacheKey = RedisCacheKeyConst.SYS_TIPS_ALGORITHM_RESULT + userId;
        redisComponent.setEx(cacheKey, getTipsReturnDTO, cacheExpireProperties.getTipsResult(), TimeUnit.MINUTES);
    }

    /**
     * 获取tips算法结果
     *
     * @param userId 用户编号
     * @return GetTipsReturnDTO
     */
    public GetTipsReturnDTO getTipsAlgorithmData(Long userId) {
        String cacheKey = RedisCacheKeyConst.SYS_TIPS_ALGORITHM_RESULT + userId;
        try {
            GetTipsReturnDTO getTipsReturnDTO = redisComponent.get(cacheKey, GetTipsReturnDTO.class);
            if (ObjectUtils.isEmpty(getTipsReturnDTO)) {
                return null;
            }
            return getTipsReturnDTO;
        } catch (Exception e) {
            log.error("get tips algorithm cache error", e);
            return null;
        }
    }

    /**
     * 获取算法结果表缓存
     *
     * @param userId 用户编号
     * @return AlgorithmResultDTO
     */
    public AlgorithmResultDTO getAlgorithmResultCheckPeriod(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        AlgorithmResultDTO algorithmResultDTO = null;
        try {
            algorithmResultDTO = redisComponent.get(cacheKey, AlgorithmResultDTO.class);
        } catch (Exception e) {
            log.error("get algorithm result cache error", e);
        }
        if (ObjectUtils.isEmpty(algorithmResultDTO)) {
            algorithmResultDTO = algorithmProvider.getAlgorithmResultCache(userId).getData();
            if (algorithmResultDTO == null) {
                throw new UserException("user algorithmResult not exist");
            }
        }

        return checkAndCallExtendPeriod(userId, algorithmResultDTO);
    }

    /**
     * 获取算法结果表缓存
     *
     * @param userId 用户编号
     * @return AlgorithmResultDTO
     */
    public AlgorithmResultDTO getCacheAlgorithmResult(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        AlgorithmResultDTO algorithmResultDTO = null;
        try {
            algorithmResultDTO = redisComponent.get(cacheKey, AlgorithmResultDTO.class);
        } catch (Exception e) {
            log.error("get algorithm result cache error", e);
        }
        if (ObjectUtils.isEmpty(algorithmResultDTO)) {
            algorithmResultDTO = algorithmProvider.getAlgorithmResultCache(userId).getData();
            if (algorithmResultDTO == null) {
                throw new UserException("user algorithmResult not exist");
            }
        }

        return algorithmResultDTO;
    }

    private AlgorithmResultDTO checkAndCallExtendPeriod(Long userId, AlgorithmResultDTO algorithmResultDTO) {
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
            return algorithmResultDTO;
        }

        String timeZone = algorithmResultDTO.getTimeZone();
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
        if (currentCycleData.getCycle_status() == CycleStatusEnum.FORECAST_CYCLE.getStatus()) {
            //延长经期
            extendPeriodManager.extendPeriod(timeZone, userId, algorithmResultDTO);
            algorithmResultDTO = algorithmProvider.getAlgorithmResultCache(userId).getData();
            if (algorithmResultDTO == null) {
                throw new UserException("user algorithmResult not exist");
            }
        }
        return algorithmResultDTO;
    }

    /**
     * 删除算法结果表缓存
     *
     * @param userId 用户编号
     */
    public void delCacheAlgorithmResult(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        redisComponent.delete(cacheKey);
    }

    /**
     * 获取reminder信息
     * <p>
     * 随机缓存25到30天
     *
     * @param userId 用户id
     * @return 没有数据返回null
     */
    public UserReminderInfoDTO getUserReminderInfo(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_REMIND + userId;

        UserReminderInfoDTO cache;
        try {
            cache = redisComponent.get(cacheKey, UserReminderInfoDTO.class);
        } catch (Exception e) {
            cache = null;
        }
        if (ObjectUtils.isNotEmpty(cache)) {
            return cache;
        }

        AppUserReminderEntity appUserReminderEntity = appUserReminderDAO.getByUserId(userId);
        if (appUserReminderEntity == null) {
            return null;
        }
        UserReminderInfoDTO userReminderInfoDTO = new UserReminderInfoDTO();
        BeanUtil.copyProperties(appUserReminderEntity, userReminderInfoDTO);
        if (ObjectUtils.isEmpty(userReminderInfoDTO.getTestingScheduleRemindTime())) {
            userReminderInfoDTO.setTestingScheduleRemindTime(ZoneDateUtil.timestamp(appUserReminderEntity.getTimeZone(),
                    "2023-03-03 05:00:00", DatePatternConst.DATE_TIME_PATTERN));
        }
        if (ObjectUtils.isEmpty(userReminderInfoDTO.getBbtTestingRemindTime())) {
            userReminderInfoDTO.setBbtTestingRemindTime(ZoneDateUtil.timestamp(appUserReminderEntity.getTimeZone(),
                    "2023-03-03 05:00:00", DatePatternConst.DATE_TIME_PATTERN));
        }
        redisComponent.setEx(cacheKey, userReminderInfoDTO, ThreadLocalRandom.current().nextInt(25, 30), TimeUnit.DAYS);

        return userReminderInfoDTO;
    }

    /**
     * 删除reminder信息缓存
     *
     * @param userId 用户id
     */
    public void delCacheReminderInfo(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_REMIND + userId;
        redisComponent.delete(cacheKey);
    }

    /**
     * 更新push token
     *
     * @param userId    用户id
     * @param pushToken token
     * @param platform  平台
     */
    public void updatePushToken(Long userId, String pushToken, Integer platform) {
        String cacheKey = RedisCacheKeyConst.USER_FIREBASE_TOKEN + userId;

        PushTokenDTO pushTokenDTO = new PushTokenDTO();
        pushTokenDTO.setPushToken(pushToken);
        pushTokenDTO.setPlatform(platform);
        redisComponent.setEx(cacheKey, pushTokenDTO, cacheExpireProperties.getPushToken(), TimeUnit.DAYS);
    }

    /**
     * 缓存action button
     *
     * @param actionButtonCodeEnum enum
     */
    public void cacheActionButton(HomeActionButtonCodeEnum actionButtonCodeEnum) {
        String cacheKey = "action_button:";
        redisComponent.setEx(cacheKey, actionButtonCodeEnum.getCode(), 6, TimeUnit.HOURS);
    }

    /**
     * 删除用户详情缓存
     *
     * @param userId 用户id
     */
    public void deleteUserDetailCache(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_LOGIN_INFO + userId;
        redisComponent.delete(cacheKey);
    }
}
