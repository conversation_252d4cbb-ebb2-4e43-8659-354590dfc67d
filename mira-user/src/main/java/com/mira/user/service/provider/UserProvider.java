package com.mira.user.service.provider;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.period.AlgorithmEditPeriodDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.user.dto.user.*;
import com.mira.api.user.dto.user.diary.CustomLogConfigDTO;
import com.mira.api.user.dto.user.diary.UpdatePregnantDiaryDTO;
import com.mira.api.user.dto.user.diary.UserDiaryDTO;
import com.mira.api.user.dto.user.diary.UserDiaryIntegrationDTO;
import com.mira.api.user.dto.user.diary.excel.ExportUserDiaryIntegrationDTO;
import com.mira.api.user.enums.UserBindTypeEnum;
import com.mira.api.user.provider.IUserProvider;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.response.CommonResult;
import com.mira.user.controller.vo.chart.SysTipsVO;
import com.mira.user.dal.dao.AppUserBindLogDAO;
import com.mira.user.dal.entity.AppUserBindLogEntity;
import com.mira.user.dal.entity.UserProductTrialEntity;
import com.mira.user.service.beta.IBetaUserService;
import com.mira.user.service.front.IChartService;
import com.mira.user.service.manager.UserCustomLogManager;
import com.mira.user.service.manager.UserDiaryLogManager;
import com.mira.user.service.user.IUserCustomLogService;
import com.mira.user.service.user.IUserService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户信息获取实现类
 *
 * <AUTHOR>
 */
@RestController
public class UserProvider implements IUserProvider {
    @Resource
    private UserDiaryLogManager userDiaryLogManager;
    @Resource
    private UserCustomLogManager userCustomLogManager;

    @Resource
    private IUserService userService;
    @Resource
    private IBetaUserService betaUserService;
    @Resource
    private IUserCustomLogService userCustomLogService;
    @Resource
    private AppUserBindLogDAO appUserBindLogDAO;

    @Resource
    private IChartService chartService;

    @Override
    public CommonResult<AppUserDTO> getUserByEmail(String email) {
        return CommonResult.OK(userService.getUserByEmail(email));
    }

    @Override
    public CommonResult<AppUserDTO> getUserById(Long userId) {
        return CommonResult.OK(userService.getUserById(userId));
    }

    @Override
    public CommonResult<AppUserDTO> saveUser(AppUserSaveDTO appUserSaveDTO) {
        return CommonResult.OK(userService.saveUser(appUserSaveDTO));
    }

    @Override
    public CommonResult<AppPartnerDTO> getPartnerById(Long partnerId) {
        return CommonResult.OK(userService.getPartnerById(partnerId));
    }

    @Override
    public CommonResult<AppPartnerDTO> getPartnerByEmail(String email) {
        return CommonResult.OK(userService.getPartnerByEmail(email));
    }

    @Override
    public CommonResult<AppUserInfoDTO> getUserInfoById(Long userId) {
        return CommonResult.OK(userService.getUserInfoByUserId(userId));
    }

    @Override
    public CommonResult<WandTypeEnum> getUserProductTrial(Long userId) {
        AppUserDTO appUserDTO = userService.getUserById(userId);
        UserProductTrialEntity userProductTrial = betaUserService.getProductTrialInfo(appUserDTO);
        if (userProductTrial == null) {
            return CommonResult.OK();
        }
        return CommonResult.OK(WandTypeEnum.get(userProductTrial.getFlag()));
    }

    @Override
    public CommonResult<List<TemperatureDTO>> getTemperatureList(Long userId, String dateStr) {
        return CommonResult.OK(userDiaryLogManager.listTemperatureDTO(userId, dateStr));
    }

    @Override
    public CommonResult<CustomLogConfigDTO> getCustomLogConfig(Long userId) {
        return CommonResult.OK(userCustomLogManager.configInfo(userId));
    }

    @Override
    public CommonResult<Integer> bindUnbindDevice(Long userId, UserBindDTO userBindDTO) {
        return CommonResult.OK(userService.bindUnbindDevice(userId, userBindDTO));
    }

    @Override
    public CommonResult<String> editBindVersion(Long userId, UserBindVersionDTO userBindVersionDTO) {
        return CommonResult.OK(userService.editBindVersion(userId, userBindVersionDTO));
    }

    @Override
    public CommonResult<TemperatureResultDTO> addTemperature(Long userId, UserTemperatureDTO userTemperatureDTO) {
        return CommonResult.OK(userService.saveTemperature(userId, userTemperatureDTO));
    }

    @Override
    public CommonResult<Void> editTemperatureUnit(Long userId, String tempUnit) {
        userCustomLogService.editTemperatureUnit(userId, tempUnit);
        return CommonResult.OK();
    }

    @Deprecated
    @Override
    public CommonResult<TestingScheduleDTO> getTestingSchedule(Long userId) {
        return CommonResult.OK(userService.getTestingSchedule(userId));
    }

    @Override
    public CommonResult<List<UserDiaryDTO>> listUserDiary(Long userId, String startDate, String endDate) {
        return CommonResult.OK(userService.listUserDiary(userId, startDate, endDate));
    }

    @Override
    public CommonResult<UserDiaryIntegrationDTO> getUserDiaryIntegration(Long userId, String dateStr) {
        return CommonResult.OK(userService.getUserDiaryIntegration(userId, dateStr));
    }

    @Override
    public CommonResult<Map<String, UserDiaryIntegrationDTO>> listUserDiaryIntegration(Long userId, List<String> dates) {
        return CommonResult.OK(userService.listUserDiaryIntegration(userId, dates));
    }

    @Override
    public CommonResult<Map<String, ExportUserDiaryIntegrationDTO>> listUserAllDiaryIntegration(Long userId) {
        return CommonResult.OK(userService.listUserAllDiaryIntegration(userId));
    }

    @Override
    public CommonResult<UserPeriodDTO> getUserPeriod(Long userId) {
        return CommonResult.OK(userService.getUserPeriod(userId));
    }

    @Override
    public CommonResult<String> wandChange(Long userId, String wandType) {
        userService.wandChange(userId, wandType);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> updatePregnantDiary(UpdatePregnantDiaryDTO updatePregnantDiaryDTO) {
        userService.updatePregnantDiary(updatePregnantDiaryDTO);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<Map<Long, String>> listEmailByIds(Set<Long> userIds) {
        Map<Long, String> map = userService.listEmailByIds(userIds);
        return CommonResult.OK(map);
    }

    @Override
    public CommonResult<List<UserPeriodDTO>> listPeriodByUserIds(Set<Long> userIds) {
        return CommonResult.OK(userService.listPeriodByUserIds(userIds));
    }

    @Override
    public CommonResult<List<UserBindLogDTO>> list10BindLog(Long userId) {
        List<AppUserBindLogEntity> appUserBindLogEntities = appUserBindLogDAO.list10BindLog(userId);
        List<UserBindLogDTO> userBindLogDTOS = new ArrayList<>();
        for (AppUserBindLogEntity appUserBindLogEntity : appUserBindLogEntities) {
            UserBindLogDTO userBindLogDTO = new UserBindLogDTO();
            userBindLogDTO.setBindTime(appUserBindLogEntity.getBindTime());
            userBindLogDTO.setBindVersion(appUserBindLogEntity.getBindVersion());
            userBindLogDTO.setSn(appUserBindLogEntity.getSn());
            userBindLogDTO.setBindType(UserBindTypeEnum.getEnumByValue(appUserBindLogEntity.getType()).getDescription());
            userBindLogDTOS.add(userBindLogDTO);
        }
        return CommonResult.OK(userBindLogDTOS);
    }

    @Override
    public CommonResult<List<DeskSearchUserDTO>> searchDeskUser(String keyword) {
        List<DeskSearchUserDTO> deskSearchUserDTOS = userService.searchDeskUserInfos(keyword);
        return CommonResult.OK(deskSearchUserDTOS);
    }

    @Override
    public CommonResult<DeskUserInfoDTO> getDeskUserInfoDTO(Long userId) {
        DeskUserInfoDTO deskUserInfoDTO = userService.getDeskUserInfoDTO(userId);
        return CommonResult.OK(deskUserInfoDTO);
    }

    @Override
    public CommonResult<Void> systemEditPeriod(Long userId) {
        userService.deskEditPeriod(userId);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> payWall(UserPaywallDTO userPaywallDTO) {
        userService.payWall(userPaywallDTO);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<SysTipsDTO> buildTipsResult(Long userId) {
        SysTipsVO sysTipsVO = chartService.tips(userId);
        return CommonResult.OK(BeanUtil.toBean(sysTipsVO, SysTipsDTO.class));
    }

    @Deprecated
    @Override
    public CommonResult<AlgorithmEditPeriodDTO> buildAlgorithmEditPeriodDTO(Long userId, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        return CommonResult.OK(userService.buildAlgorithmEditPeriodDTO(userId, algorithmRequestTypeEnum));
    }

    @Override
    public CommonResult<AlgorithmEditPeriodDTO> openMax2ScheduleAndBuildAlgorithmEditPeriodDTO(Long userId, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        return CommonResult.OK(userService.openMax2ScheduleAndBuildAlgorithmEditPeriodDTO(userId, algorithmRequestTypeEnum));
    }

    @Override
    public CommonResult<Void> waitShipping(Long userId) {
        userService.waitShipping(userId);
        return CommonResult.OK();
    }
}
