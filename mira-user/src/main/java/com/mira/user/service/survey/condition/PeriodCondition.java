package com.mira.user.service.survey.condition;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.mongo.dto.SurveyCondition;
import com.mira.core.util.LocalDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <p>
 * Period
 * <br/>
 * 经期，1:Predicted, 2:logged beginning,logged end, 3:predicted end
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-04-16
 **/
@Slf4j
public class PeriodCondition {

    /**
     * 预测经期中
     */
    private final static int PREDICATED = 1;
    /**
     * 实际经期开始
     */
    private final static int REAL_PERIOD_START = 2;
    /**
     * 实际经期结束
     */
    private final static int REAL_PERIOD_END = 3;
    /**
     * 预测经期结束
     */
    private final static int PREDICATED_END = 4;

    public static boolean checkPeriods(Long userId, String today, List<CycleDataDTO> cycleDataDTOS,
                                       SurveyCondition surveyCondition) {
        List<Integer> periodsConditionList = surveyCondition.getPeriod();
        if (periodsConditionList.isEmpty()) {
            return true;
        }
        if (cycleDataDTOS.isEmpty()) {
            return false;
        }
        boolean isInCondition = false;
        try {
            for (Integer periodCondition : periodsConditionList) {
                CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
                if (StringUtils.isBlank(currentCycleData.getDate_period_start())) {
                    isInCondition = false;
                    continue;
                }
                // 是否在下列的条件中
                if (PREDICATED == periodCondition && currentCycleData.getCycle_status() == CycleStatusEnum.FORECAST_CYCLE.getStatus()) {
                    boolean inPredicated = LocalDateUtil.isBetweenDateAndEqualLeft(today,
                            currentCycleData.getDate_period_start(), currentCycleData.getDate_period_end());
                    if (inPredicated) {
                        isInCondition = true;
                    }
                    continue;
                }
                if (REAL_PERIOD_START == periodCondition
                        && currentCycleData.getCycle_status() == CycleStatusEnum.REAL_CYCLE.getStatus()) {
                    int minusToDay = LocalDateUtil.minusToDay(today, currentCycleData.getDate_period_start());
                    if (minusToDay == 0) {
                        isInCondition = true;
                    }
                    continue;
                }
                if (REAL_PERIOD_END == periodCondition
                        && currentCycleData.getCycle_status() == CycleStatusEnum.REAL_CYCLE.getStatus()) {
                    int minusToDay = LocalDateUtil.minusToDay(today, currentCycleData.getDate_period_end());
                    if (minusToDay == -1) { // 前闭后开
                        isInCondition = true;
                    }
                    continue;
                }
                if (PREDICATED_END == periodCondition
                        && (currentCycleData.getCycle_status() == CycleStatusEnum.FORECAST_CYCLE.getStatus()
                        || currentCycleData.getCycle_status() == CycleStatusEnum.DEFAULT_LAST_REAL_CYCLE_EXTEND_PERIOD.getStatus())) {
                    int minusToDay = LocalDateUtil.minusToDay(today, currentCycleData.getDate_period_end());
                    if (minusToDay == -1) { // 前闭后开
                        isInCondition = true;
                    }
                }
            }
            if (!isInCondition) {
                return false;
            }
        } catch (Exception e) {
            log.error("【SurveyCondition】user:{} period handler error.", userId, e);
            return false;
        }

        return true;
    }

}
