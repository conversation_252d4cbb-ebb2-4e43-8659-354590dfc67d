package com.mira.user.service.user;

import com.mira.api.user.dto.user.diary.UserMedicineDTO;
import com.mira.user.controller.vo.menopause.MenopauseSurveyResultVO;
import com.mira.user.dto.info.v5.*;

import java.util.List;

/**
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2024-06-11 02:17
 **/
public interface IUserInfoV5Service {

    void checkTrackingMenopause(Boolean select, Integer update);

    void menopauseProcedure(MenopauseProcedureDTO menopauseProcedureDTO, Integer update);

    void menopauseTakeMedication(Integer select, Integer update);

    void menopauseMedications(List<UserMedicineDTO> medications, Integer update);

    void menopauseRegularPeriod(MenopauseRegularPeriodDTO menopauseRegularPeriodDTO, Integer update);

    void menopauseMotherEnter(Integer age, Integer update);

    void checkHrt(Integer select, Integer update);

    void menopauseHrtType(MenopauseHrtTypeDTO menopauseHrtTypeDTO, Integer update);

    void menopauseEverPregnant(Integer value, Integer update);

    void menopausePhysicallyActive(Integer value, Integer update);

    void menopausePhysicallyActiveType(MenopausePhysicallyActiveTypeDTO menopausePhysicallyActiveTypeDTO, Integer update);

    void menopauseGoal(MenopauseGoalDTO menopauseGoalDTO, Integer update);

    void menopauseSymptom(List<MenopauseSymptomDTO> menopauseSymptoms, Integer update);

    Integer menopausePageCode();

    MenopauseSurveyResultVO surveyResult();
}