package com.mira.user.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.util.PeriodUtil;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.thirdparty.enums.AmplitudeEventTypeEnum;
import com.mira.api.user.dto.user.TestingScheduleDTO;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.api.user.enums.RemindStatusConst;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.StringListUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.async.AmplitudeProducer;
import com.mira.user.async.KlaviyoProducer;
import com.mira.user.controller.vo.user.UserGoalScheduleOptionVO;
import com.mira.user.dal.dao.*;
import com.mira.user.dal.entity.*;
import com.mira.user.dto.info.*;
import com.mira.user.dto.schedule.GoalTestingScheduleDTO;
import com.mira.user.enums.user.EditGoalFlagEnum;
import com.mira.user.enums.user.UserConditionEnum;
import com.mira.user.exception.UserException;
import com.mira.user.handler.schedule.RebuildTestingSchedule;
import com.mira.user.service.manager.AlgorithmCallManager;
import com.mira.user.service.manager.AlgorithmCallParamManager;
import com.mira.user.service.manager.CacheManager;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import com.mira.user.service.user.IUserInfoService;
import com.mira.user.service.util.CallEditPeriodUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户信息接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserInfoServiceImpl implements IUserInfoService {
    private final AppUserInfoDAO appUserInfoDAO;
    private final AppUserPeriodDAO appUserPeriodDAO;
    private final AppUserPartnerDAO appUserPartnerDAO;
    private final AppUserReminderDAO appUserReminderDAO;
    private final UserReminderComplaintDAO userReminderComplaintDAO;
    private final AppUserTestingScheduleDAO appUserTestingScheduleDAO;
    private final AppPregnantModeInfoV2DAO appPregnantModeInfoV2DAO;
    private final CacheManager cacheManager;
    private final AlgorithmCallParamManager algorithmCallParamManager;
    private final AlgorithmCallManager algorithmCallManager;
    private final KlaviyoProducer klaviyoProducer;
    private final AmplitudeProducer amplitudeProducer;
    private final ISsoProvider ssoProvider;
    private final RebuildTestingSchedule rebuildTestingSchedule;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editName(UserNameDTO userNameDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        userInfoEntity.setFirstName(userNameDTO.getFirstName());
        userInfoEntity.setLastName(userNameDTO.getLastName());
        String nickName = (StringUtils.isBlank(userNameDTO.getFirstName()) ? "" : userNameDTO.getFirstName()) + " " + (StringUtils.isBlank(userNameDTO.getLastName()) ? "" : userNameDTO.getLastName());
        userInfoEntity.setNickname(nickName);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);
        cacheManager.deleteUserDetailCache(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBirthday(UserBirthdayDTO userBirthdayDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        if (Objects.isNull(userInfoEntity)) {
            userInfoEntity = new AppUserInfoEntity();
            userInfoEntity.setUserId(userId);
            userInfoEntity.setBirthYear(userBirthdayDTO.getYear());
            userInfoEntity.setBirthMonth(userBirthdayDTO.getMonth());
            userInfoEntity.setBirthOfDay(userBirthdayDTO.getDay());
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userInfoEntity);
            appUserInfoDAO.save(userInfoEntity);
        } else {
            userInfoEntity.setBirthYear(userBirthdayDTO.getYear());
            userInfoEntity.setBirthMonth(userBirthdayDTO.getMonth());
            userInfoEntity.setBirthOfDay(userBirthdayDTO.getDay());
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
            appUserInfoDAO.updateById(userInfoEntity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savePeriodLength(UserPeriodLengthDTO userPeriodLengthDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (Objects.isNull(appUserPeriodEntity)) {
            appUserPeriodEntity = new AppUserPeriodEntity();
            appUserPeriodEntity.setUserId(userId);
            appUserPeriodEntity.setCreator(userId);
            appUserPeriodEntity.setModifier(userId);
            appUserPeriodEntity.setAvgLenPeriod(userPeriodLengthDTO.getAvgLenPeriod());
            appUserPeriodEntity.setPeriodFlag(userPeriodLengthDTO.getPeriodFlag());
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appUserPeriodEntity);
            appUserPeriodDAO.save(appUserPeriodEntity);
        } else {
            appUserPeriodEntity.setCreator(userId);
            appUserPeriodEntity.setModifier(userId);
            appUserPeriodEntity.setAvgLenPeriod(userPeriodLengthDTO.getAvgLenPeriod());
            appUserPeriodEntity.setPeriodFlag(userPeriodLengthDTO.getPeriodFlag());
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUserPeriodEntity);
            appUserPeriodDAO.updateById(appUserPeriodEntity);
        }
        cacheManager.deleteUserDetailCache(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savePeriod(List<String> dates) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String email = loginInfo.getUsername();
        String timeZone = loginInfo.getTimeZone();

        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            throw new UserException("user period not exist");
        }

        if (dates.size() == 1 && dates.get(0).equals("default")) {
            // 保存空周期
            appUserPeriodEntity.setPeriods("");
            appUserPeriodEntity.setPeriodData("[]");
            appUserPeriodDAO.updateById(appUserPeriodEntity);
            cacheManager.deleteUserDetailCache(userId);
            log.info("user:{}, save empty period", userId);
            return;
        }

        List<Long> periods = new ArrayList<>();
        for (String date : dates) {
            periods.add(ZoneDateUtil.timestamp(timeZone, date, DatePatternConst.DATE_PATTERN));
        }
        log.info("periods: " + StringListUtil.listToString(periods, ","));

        // 排序
        List<Long> sortedPeriodList = periods;
        if (CollectionUtils.isNotEmpty(periods)) {
            sortedPeriodList = periods.stream().sorted(Long::compareTo).collect(Collectors.toList());
        }

        String dbPeriods = appUserPeriodEntity.getPeriods();
        String periodsStr = StringListUtil.listToString(sortedPeriodList, ",");
        // update
        if (StringUtils.isBlank(periodsStr) && StringUtils.isBlank(dbPeriods)) {
            // 说明都为空
            log.info("user:{}, email:{} periods empty", userId, email);
            return;
        }
        if (StringUtils.isNotBlank(periodsStr) && periodsStr.equals(dbPeriods)) {
            // 说明没有发生修改
            log.info("user:{}, email:{} periods not changed", userId, email);
            return;
        }

        appUserPeriodEntity.setModifier(userId);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUserPeriodEntity);

        List<UserPeriodDataDTO> periodList = PeriodUtil.buildPeriodList(appUserPeriodEntity.getAvgLenPeriod(), PeriodUtil.periodsLong2String(sortedPeriodList, timeZone));
        String periodListJson = JsonUtil.toJson(periodList);
        if (StringUtils.isBlank(periodsStr)) {
            appUserPeriodEntity.setPeriods("");
        } else {
            appUserPeriodEntity.setPeriods(periodsStr);
        }
        appUserPeriodEntity.setPeriodData(periodListJson);

        appUserPeriodDAO.updateById(appUserPeriodEntity);
        cacheManager.deleteUserDetailCache(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCycleLength(UserCycleLengthDTO userCycleLengthDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        // app user period
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        appUserPeriodEntity.setUserId(userId);
        appUserPeriodEntity.setCreator(userId);
        appUserPeriodEntity.setModifier(userId);
        appUserPeriodEntity.setAvgLenCycle(userCycleLengthDTO.getAvgLenCycle());
        appUserPeriodEntity.setCycleFlag(userCycleLengthDTO.getCycleFlag());
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUserPeriodEntity);
        appUserPeriodDAO.updateById(appUserPeriodEntity);
        // app user info
        AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(userId);
        appUserInfoEntity.setIrregularCycle(userCycleLengthDTO.getIrregularCycle());
        appUserInfoDAO.updateById(appUserInfoEntity);

        cacheManager.deleteUserDetailCache(userId);

        // sync klaviyo
        klaviyoProducer.editCycleLength(userId, userCycleLengthDTO.getAvgLenCycle());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveGoal(UserGoalDTO userGoalDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        userInfoEntity.setGoalStatus(userGoalDTO.getGoalStatus());
        userInfoEntity.setModeChangeFlag(userGoalDTO.getGoalStatus().toString());
        if (Objects.equals(true, userGoalDTO.getMenopause())) {
            userInfoEntity.setTrackingMenopause(1);
        } else {
            userInfoEntity.setTrackingMenopause(0);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);

        // general app_user_testing_schedule
        defaultSchedule(userId, timeZone, userGoalDTO.getGoalStatus());

        // 设置默认推送时间
        saveDefaultRemindTime(userId, timeZone);

        // klavyio
        klaviyoProducer.changeMode(userId, userGoalDTO.getGoalStatus(), null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkConceive(Boolean select) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        // 用户Goal
        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        Integer goalStatus;
        if (select) {
            goalStatus = UserGoalEnum.TTC.getValue();
        } else {
            goalStatus = UserGoalEnum.CYCLE_TRACKING.getValue();
        }

        // udpate
        userInfoEntity.setGoalStatus(goalStatus);
        userInfoEntity.setModeChangeFlag(goalStatus.toString());
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);

        // general app_user_testing_schedule
        defaultSchedule(userId, timeZone, goalStatus);

        // 设置默认推送时间
        saveDefaultRemindTime(userId, timeZone);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveConditions(UserConditionsDTO userConditionsDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        List<Integer> paramConditions = userConditionsDTO.getConditions();
        String conditions = StringListUtil.listToString(paramConditions, ",");
        userInfoEntity.setConditions(conditions);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);

        if (!paramConditions.contains(UserConditionEnum.HORMONAL_BIRTH_CONTROL.getValue())) {
            // 注册用户时算法计算（只会在注册时触发一次）
            algorithmCallParamManager.callAlgorithm(userId, loginUserInfoDTO, AlgorithmRequestTypeEnum.REGISTER);
            // klavyio，这里执行了，step7就不会执行，两处只会调用一次
            klaviyoProducer.onBoardingComplete(userId);
        }

        cacheManager.deleteUserDetailCache(userId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveHormonalBirthControl(UserHormonalBirthControlDTO userHormonalBirthControlDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        userInfoEntity.setHormonalBirthControl(userHormonalBirthControlDTO.getHormonalBirthControl());
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);

        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();

        // 注册用户时算法计算（只会在注册时触发一次）
        algorithmCallParamManager.callAlgorithm(userId, loginUserInfoDTO, AlgorithmRequestTypeEnum.REGISTER);
        // kalvyio
        klaviyoProducer.onBoardingComplete(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UserGoalScheduleOptionVO getGoalScheduleOption() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        Integer goalStatus = loginUserInfoDTO.getGoalStatus();
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();

        UserGoalScheduleOptionVO appUserGoalScheduleOptionVO = new UserGoalScheduleOptionVO();
        appUserGoalScheduleOptionVO.setGoalStatus(goalStatus);
        Boolean inTestingScheduleExchange = userReminderComplaintDAO.getCountByUserId(userId) > 0;
        if (Integer.valueOf(1).equals(trackingMenopause)) {
            inTestingScheduleExchange = false;
        }
        appUserGoalScheduleOptionVO.setInTestingScheduleExchange(inTestingScheduleExchange);
        AppUserTestingScheduleEntity userTestingScheduleEntity = appUserTestingScheduleDAO.getByUserId(userId);

        // general app_user_testing_schedule
        if (ObjectUtils.isEmpty(userTestingScheduleEntity)) {
            userTestingScheduleEntity = defaultSchedule(userId, timeZone, goalStatus);
        }

        Integer max = userTestingScheduleEntity.getMax();
        Integer ovum = userTestingScheduleEntity.getOvum();
        Integer plus = RemindStatusConst.SCHEDULE_NOT_DISPLAY;
        Integer confirm = RemindStatusConst.SCHEDULE_NOT_DISPLAY;

        switch (UserGoalEnum.get(goalStatus)) {
            case TTC:
                max = RemindStatusConst.SCHEDULE_ALWAYS_ON;
                break;
            case CYCLE_TRACKING:
                max = RemindStatusConst.SCHEDULE_ALWAYS_ON;
                break;
            case OFT:
                ovum = RemindStatusConst.SCHEDULE_ALWAYS_ON;
                break;
            case PREGNANCY_TRACKING:
                break;
            default:
                max = RemindStatusConst.SCHEDULE_ALWAYS_ON;
                break;
        }
        if (inTestingScheduleExchange) {
            max = userTestingScheduleEntity.getMax();
            plus = userTestingScheduleEntity.getPlus();
            confirm = userTestingScheduleEntity.getConfirm();
        }

        if (Integer.valueOf(1).equals(trackingMenopause)) {
            ovum = RemindStatusConst.SCHEDULE_ALWAYS_ON;
            max = RemindStatusConst.SCHEDULE_ALWAYS_ON;
            plus = RemindStatusConst.SCHEDULE_NOT_DISPLAY;
            confirm = RemindStatusConst.SCHEDULE_NOT_DISPLAY;
        }

        appUserGoalScheduleOptionVO.setMax(max);
        appUserGoalScheduleOptionVO.setPlus(plus);
        appUserGoalScheduleOptionVO.setConfirm(confirm);
        appUserGoalScheduleOptionVO.setOvum(ovum);

        List<Integer> goalStatusOptions = Lists.newArrayList(UserGoalEnum.CYCLE_TRACKING.getValue(), UserGoalEnum.TTC.getValue());
        if (UserGoalEnum.TTA.getValue().equals(goalStatus)) {
            goalStatusOptions.add(UserGoalEnum.TTA.getValue());
        }
        //todo 临时屏蔽
        if (UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(goalStatus)) {
            goalStatusOptions.add(UserGoalEnum.PREGNANCY_TRACKING.getValue());
        }
        Integer goalTrialFlag = loginUserInfoDTO.getGoalTrialFlag();
        if (goalTrialFlag != null) {
            if (!goalStatusOptions.contains(goalTrialFlag)) {
                goalStatusOptions.add(goalTrialFlag);
            }
        }
        List<UserGoalScheduleOptionVO.Option> options = buildOptions(inTestingScheduleExchange, goalStatusOptions, trackingMenopause);
        appUserGoalScheduleOptionVO.setOptions(options);
        return appUserGoalScheduleOptionVO;
    }


    @Override
    public void editGoal(GoalTestingScheduleDTO goalTestingScheduleDTO, Integer removePregnantInfo,
                         EditGoalFlagEnum editGoalFlagEnum) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        Integer goalStatus = goalTestingScheduleDTO.getGoalStatus();//target goal
        Integer dbGoalStatus = userInfoEntity.getGoalStatus();//existed goal
        Integer trackingMenopause = userInfoEntity.getTrackingMenopause();

        //如果是退出怀孕模式，并且选了移除怀孕信息，则删除pregnant info v2中当前怀孕信息记录
        if (UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(dbGoalStatus) &&
                !UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(goalStatus) &&
                removePregnantInfo != null && removePregnantInfo.equals(1)) {
            editGoalFlagEnum = EditGoalFlagEnum.REMOVE_PREGNANT_FROM_EDIT_GOAL_REMOVE_INFO;
        }

        if (!goalStatus.equals(dbGoalStatus)) {
            userInfoEntity.setModeChangeFlag(dbGoalStatus + "-" + goalStatus);


        }
        userInfoEntity.setGoalStatus(goalStatus);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);

        //让子方法事务立即提交
        UserInfoServiceImpl proxyUserInfoServide = (UserInfoServiceImpl) AopContext.currentProxy();
        //进入怀孕模式或者退出怀孕模式需要向pregnant_info写入数据
        proxyUserInfoServide.setPregnantInfo(editGoalFlagEnum, userId, timeZone);

        changeCyclePhaseFlag(userId, goalStatus, dbGoalStatus, timeZone);

        //change schedule
        proxyUserInfoServide.changeSchedule(userId, timeZone, goalTestingScheduleDTO, trackingMenopause);

        // 编辑经期： schedule发生变化，会造成算法推荐的测试试剂发生变化
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);

        if (StringUtils.isNotBlank(appUserPeriodEntity.getPeriods())) {
            // 构建经期信息
            UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity, null);
            BeanUtil.copyProperties(appUserPeriodEntity, userPeriodParamDTO);
            UserGoalEnum userGoalEnum = UserGoalEnum.get(goalStatus);
            if (userGoalEnum == null) {
                userGoalEnum = UserGoalEnum.TTC;
            }
            userPeriodParamDTO.setUserMode(userGoalEnum.getValue());
            // 调用算法
            PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(timeZone,
                    loginUserInfoDTO.getEmail(), appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
            algorithmCallManager.editPeriod(prepareEditPeriodDTO, AlgorithmRequestTypeEnum.CHANGE_SCHEDULE);
        }

        // rebuild testing day schedule
        UserReminderInfoDTO reminderInfoDTO = cacheManager.getUserReminderInfo(userId);
        if (reminderInfoDTO != null) {
            rebuildTestingSchedule.execute(userId, ZoneDateUtil.format(timeZone, reminderInfoDTO.getTestingScheduleRemindTime(), DatePatternConst.DATE_TIME_PATTERN));
        }

        // klavyio
        klaviyoProducer.changeMode(userId, goalStatus, dbGoalStatus);
        // cache
        cacheManager.deleteUserDetailCache(userId);

        //amplitude event
        String pregnantFlag = null;
        if (UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(dbGoalStatus) &&
                !UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(goalStatus)) {
            pregnantFlag = "End";
        } else if (UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(goalStatus) &&
                !UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(dbGoalStatus)) {
            pregnantFlag = "Start";
        }
        if (StringUtils.isNotBlank(pregnantFlag)) {
            HashMap<String, String> params = new HashMap<>();
            String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
            params.put(pregnantFlag, today);
            amplitudeProducer.amplitudeEventLog(userId, AmplitudeEventTypeEnum.PREGNANT_MODE, params.toString());
        }

    }

    /**
     * 进入怀孕模式或者退出怀孕模式需要向pregnant_info写入数据
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void setPregnantInfo(EditGoalFlagEnum editGoalFlagEnum, Long userId, String timeZone) {
        AppPregnantModeInfoV2Entity pregnantModeInfoV2Entity =
                appPregnantModeInfoV2DAO.getRecentPregnantModeInfoByUserId(userId);
        switch (editGoalFlagEnum) {
            case CHANGE_MODE_TO_PREGNANT:
                //进入怀孕模式
                if (pregnantModeInfoV2Entity == null || pregnantModeInfoV2Entity.getIsEnd() == 1) {
                    pregnantModeInfoV2Entity = new AppPregnantModeInfoV2Entity();
                    String lastPeriodDate = appUserPeriodDAO.getLastPeriodDate(userId, timeZone);
                    pregnantModeInfoV2Entity.setUserId(userId);
                    pregnantModeInfoV2Entity.setLastPeriodDate(lastPeriodDate);
                    UpdateEntityTimeUtil.setBaseEntityTime(timeZone, pregnantModeInfoV2Entity);
                    appPregnantModeInfoV2DAO.save(pregnantModeInfoV2Entity);
                } else {
                    String lastPeriodDate = appUserPeriodDAO.getLastPeriodDate(userId, timeZone);
                    pregnantModeInfoV2Entity.setLastPeriodDate(lastPeriodDate);
                    pregnantModeInfoV2Entity.setConceptionDate(null);
                    pregnantModeInfoV2Entity.setDueDate(null);
                    UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, pregnantModeInfoV2Entity);
                    appPregnantModeInfoV2DAO.updateById(pregnantModeInfoV2Entity);
                }
                break;
            case REMOVE_PREGNANT_FROM_DAILY_LOG:
                String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);

                if (pregnantModeInfoV2Entity == null || pregnantModeInfoV2Entity.getIsEnd() == 1) {
                    //create
                    pregnantModeInfoV2Entity = new AppPregnantModeInfoV2Entity();
                    String lastPeriodDate = appUserPeriodDAO.getLastPeriodDate(userId, timeZone);
                    pregnantModeInfoV2Entity.setUserId(userId);
                    pregnantModeInfoV2Entity.setLastPeriodDate(lastPeriodDate);
                    pregnantModeInfoV2Entity.setIsEnd(1);
                    pregnantModeInfoV2Entity.setEndReason(4);

                    pregnantModeInfoV2Entity.setDueDate(today);

                    UpdateEntityTimeUtil.setBaseEntityTime(timeZone, pregnantModeInfoV2Entity);
                    appPregnantModeInfoV2DAO.save(pregnantModeInfoV2Entity);
                    return;
                }
                //update
                pregnantModeInfoV2Entity.setIsEnd(1);
                pregnantModeInfoV2Entity.setEndReason(4);
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, pregnantModeInfoV2Entity);

                String dueDate = pregnantModeInfoV2Entity.getDueDate();
                if (StringUtils.isBlank(dueDate)) {
                    pregnantModeInfoV2Entity.setDueDate(today);
                }

                appPregnantModeInfoV2DAO.updateById(pregnantModeInfoV2Entity);
                break;
            case REMOVE_PREGNANT_FROM_EDIT_GOAL_REMOVE_INFO:
                //如果当前有怀孕信息，并且不是end状态，则删除这条状态
                if (pregnantModeInfoV2Entity != null && pregnantModeInfoV2Entity.getIsEnd() == 0) {
                    appPregnantModeInfoV2DAO.removeById(pregnantModeInfoV2Entity.getId());
                }
                break;
            default:
                break;
        }

    }

    /**
     * 怀孕模式切到非怀孕模式，cyclePhaseFlag打开；非怀孕模式切到怀孕模式，cyclePhaseFlag关闭
     */
    private void changeCyclePhaseFlag(Long userId, Integer goalStatus, Integer dbGoalStatus, String timeZone) {
        Integer changeFlag = null;
        AppUserReminderEntity userReminderEntity = appUserReminderDAO.getByUserId(userId);
        if (userReminderEntity == null) {
            return;
        }
        if (UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(goalStatus) && !UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(dbGoalStatus)) {
            //当前为非怀孕模式，切换到怀孕模式，cyclePhaseFlag关闭
            boolean cyclePhaseFlag = RemindStatusConst.OPEN.equals(userReminderEntity.getCyclePhaseFlag());
            if (!cyclePhaseFlag) {
                return;
            }
            changeFlag = RemindStatusConst.CLOSE;
        } else if (!UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(goalStatus) && UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(dbGoalStatus)) {
            //当前为怀孕模式，切换到非怀孕模式，cyclePhaseFlag打开
            boolean cyclePhaseFlag = RemindStatusConst.OPEN.equals(userReminderEntity.getCyclePhaseFlag());
            if (cyclePhaseFlag) {
                return;
            }
            changeFlag = RemindStatusConst.OPEN;
        }
        if (changeFlag == null) {
            return;
        }
        userReminderEntity.setCyclePhaseFlag(changeFlag);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userReminderEntity);
        appUserReminderDAO.updateById(userReminderEntity);
        // 删除缓存
        cacheManager.deleteUserDetailCache(userId);
        cacheManager.delCacheReminderInfo(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeModePregnant(EditGoalFlagEnum editGoalFlagEnum) {
        GoalTestingScheduleDTO goalTestingScheduleDTO = new GoalTestingScheduleDTO();
        goalTestingScheduleDTO.setGoalStatus(UserGoalEnum.PREGNANCY_TRACKING.getValue());
        goalTestingScheduleDTO.setOvum(RemindStatusConst.SCHEDULE_OFF);
        goalTestingScheduleDTO.setMax(RemindStatusConst.SCHEDULE_OFF);
        goalTestingScheduleDTO.setConfirm(RemindStatusConst.SCHEDULE_OFF);
        goalTestingScheduleDTO.setPlus(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
        this.editGoal(goalTestingScheduleDTO, null, editGoalFlagEnum);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void changeModeLastMode(EditGoalFlagEnum editGoalFlagEnum, GoalTestingScheduleDTO goalTestingScheduleDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        //Integer goalStatus = userInfoEntity.getGoalStatus();
        String modeChangeFlag = userInfoEntity.getModeChangeFlag();
        if (StringUtils.isBlank(modeChangeFlag) || !modeChangeFlag.contains("-")) {
            log.error("changeModeLastMode error, userId:{}, modeChangeFlag:{}", userId, modeChangeFlag);
            return;
        }
        Integer lastGoalStatus = Integer.valueOf(modeChangeFlag.split("-")[0]);
        if (goalTestingScheduleDTO == null) {
            goalTestingScheduleDTO = new GoalTestingScheduleDTO();
            goalTestingScheduleDTO.setGoalStatus(lastGoalStatus);
            UserGoalScheduleOptionVO goalScheduleOption = this.getGoalScheduleOption();
            List<UserGoalScheduleOptionVO.Option> options = goalScheduleOption.getOptions();
            UserGoalScheduleOptionVO.Option selectOption = options.stream()
                    .filter(option -> option.getGoalStatus().equals(lastGoalStatus))
                    .findFirst()
                    .orElse(null);
            if (selectOption == null) {
                //有可能在异常情况下，golastatus为3
                goalTestingScheduleDTO.setMax(RemindStatusConst.SCHEDULE_ON);
                goalTestingScheduleDTO.setPlus(RemindStatusConst.SCHEDULE_OFF);
                goalTestingScheduleDTO.setOvum(RemindStatusConst.SCHEDULE_ON);
                goalTestingScheduleDTO.setConfirm(RemindStatusConst.SCHEDULE_OFF);
            } else {
                goalTestingScheduleDTO.setMax(selectOption.getMax());
                goalTestingScheduleDTO.setPlus(selectOption.getPlus());
                goalTestingScheduleDTO.setOvum(selectOption.getOvum());
                goalTestingScheduleDTO.setConfirm(selectOption.getConfirm());
            }
        } else {
            // 说明来自 PregnantModeInfoServiceV2Impl.removePregnanty
        }

        this.editGoal(goalTestingScheduleDTO, null, editGoalFlagEnum);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void changeSchedule(Long userId, String timeZone, GoalTestingScheduleDTO goalTestingScheduleDTO, Integer trackingMenopause) {
        AppUserTestingScheduleEntity userTestingScheduleEntity = appUserTestingScheduleDAO.getByUserId(userId);
        Integer ovum = getSchedule(goalTestingScheduleDTO.getOvum());
        Integer max = getSchedule(goalTestingScheduleDTO.getMax());
        Integer confirm = getSchedule(goalTestingScheduleDTO.getConfirm());
        Integer plus = getSchedule(goalTestingScheduleDTO.getPlus());

        userTestingScheduleEntity.setOvum(ovum);
        userTestingScheduleEntity.setConfirm(confirm);
        userTestingScheduleEntity.setPlus(plus);

        // 跟客户端约定：二选一以plus为准
        if (RemindStatusConst.SCHEDULE_ON.equals(plus)) {
            userTestingScheduleEntity.setMax(RemindStatusConst.SCHEDULE_OFF);
            userTestingScheduleEntity.setConfirm(RemindStatusConst.SCHEDULE_ON);
        } else {
            userTestingScheduleEntity.setMax(max);
            userTestingScheduleEntity.setConfirm(RemindStatusConst.SCHEDULE_OFF);
        }

        if (Integer.valueOf(1).equals(trackingMenopause)) {
            userTestingScheduleEntity.setMax(RemindStatusConst.SCHEDULE_ON);
            userTestingScheduleEntity.setPlus(RemindStatusConst.SCHEDULE_OFF);
            userTestingScheduleEntity.setConfirm(RemindStatusConst.SCHEDULE_OFF);
        }

        // update
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userTestingScheduleEntity);
        appUserTestingScheduleDAO.updateById(userTestingScheduleEntity);
    }

    private Integer getSchedule(Integer schedule) {
        if (RemindStatusConst.SCHEDULE_ALWAYS_ON.equals(schedule)) {
            schedule = RemindStatusConst.SCHEDULE_ON;
        } else if (RemindStatusConst.SCHEDULE_NOT_DISPLAY.equals(schedule)) {
            schedule = RemindStatusConst.SCHEDULE_OFF;
        }
        return schedule;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editBirthday(UserBirthdayDTO userBirthdayDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        userInfoEntity.setBirthYear(userBirthdayDTO.getYear());
        userInfoEntity.setBirthMonth(userBirthdayDTO.getMonth());
        userInfoEntity.setBirthOfDay(userBirthdayDTO.getDay());
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);
        cacheManager.deleteUserDetailCache(userId);
    }

    @Override
    public void editConditions(UserConditionsDTO userConditionsDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        List<Integer> paramConditions = userConditionsDTO.getConditions();
        String paramConditionStr = StringListUtil.listToString(paramConditions, ",");
        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        String dbConditions = userInfoEntity.getConditions();
        log.info("user:{}, new conditions:{}", userId, paramConditionStr);

        if (StringUtils.isNotBlank(paramConditionStr)) {
            userInfoEntity.setConditions(paramConditionStr);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
            appUserInfoDAO.updateById(userInfoEntity);
            cacheManager.deleteUserDetailCache(userId);
            AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
            if (appUserPeriodEntity == null) {
                throw new UserException("user period not exist");
            }

            if (!paramConditionStr.equals(dbConditions)) {
                // 调用算法
                LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
                algorithmCallParamManager.callAlgorithm(userId, loginUserInfoDTO, AlgorithmRequestTypeEnum.EDIT_CONDITIONS);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editHormonalBirthControl(UserHormonalBirthControlDTO userHormonalBirthControlDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        userInfoEntity.setHormonalBirthControl(userHormonalBirthControlDTO.getHormonalBirthControl());
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);
        cacheManager.deleteUserDetailCache(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editPushToken(PushTokenDTO pushTokenDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();

        if (pushTokenDTO.getPlatform() == null || StringUtils.isBlank(pushTokenDTO.getPushToken())) {
            log.info("user:{} input param edit push-token is null", userId);
        }

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        userInfoEntity.setPushToken(pushTokenDTO.getPushToken());
        userInfoEntity.setPlatform(pushTokenDTO.getPlatform());
        appUserInfoDAO.updateById(userInfoEntity);

        // save cache
        cacheManager.updatePushToken(userId, pushTokenDTO.getPushToken(), pushTokenDTO.getPlatform());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editAvatar(UserAvatarDTO userAvatarDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        String timeZone = loginInfo.getTimeZone();

        // 用户类型
        UserTypeEnum userTypeEnum = UserTypeEnum.get(loginInfo.getUserType());
        switch (userTypeEnum) {
            case APP_USER:
                AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(loginInfo.getId());
                userInfoEntity.setAvatar(userAvatarDTO.getAvatar());
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
                appUserInfoDAO.updateById(userInfoEntity);
                cacheManager.deleteUserDetailCache(userInfoEntity.getUserId());
                break;
            case PARTNER_USER:
                AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getById(loginInfo.getPartnerId());
                partnerEntity.setAvatar(userAvatarDTO.getAvatar());
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, partnerEntity);
                appUserPartnerDAO.updateById(partnerEntity);
                cacheManager.deleteUserDetailCache(partnerEntity.getUserId());
                break;
        }
    }

    private List<UserGoalScheduleOptionVO.Option> buildOptions(Boolean inTestingScheduleExchange,
                                                               List<Integer> goalStatusOptions,
                                                               Integer trackingMenopause) {
        List<UserGoalScheduleOptionVO.Option> options = new ArrayList<>();
        for (Integer goal : goalStatusOptions) {
            UserGoalScheduleOptionVO.Option option = new UserGoalScheduleOptionVO.Option();
            option.setGoalStatus(goal);
            TestingScheduleDTO testingScheduleDTO = new TestingScheduleDTO();
            switch (UserGoalEnum.get(goal)) {
                case TTC:
                    testingScheduleDTO.setOvum(RemindStatusConst.SCHEDULE_ON);
                    testingScheduleDTO.setMax(RemindStatusConst.SCHEDULE_ALWAYS_ON);
                    testingScheduleDTO.setConfirm(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                    testingScheduleDTO.setPlus(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                    break;
                case CYCLE_TRACKING:
                    testingScheduleDTO.setOvum(RemindStatusConst.SCHEDULE_OFF);
                    testingScheduleDTO.setMax(RemindStatusConst.SCHEDULE_ALWAYS_ON);
                    testingScheduleDTO.setConfirm(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                    testingScheduleDTO.setPlus(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                    break;
                case OFT:
                    testingScheduleDTO.setOvum(RemindStatusConst.SCHEDULE_ALWAYS_ON);
                    testingScheduleDTO.setMax(RemindStatusConst.SCHEDULE_ON);
                    testingScheduleDTO.setConfirm(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                    testingScheduleDTO.setPlus(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                    break;
                case PREGNANCY_TRACKING:
                    testingScheduleDTO.setOvum(RemindStatusConst.SCHEDULE_OFF);
                    testingScheduleDTO.setMax(RemindStatusConst.SCHEDULE_OFF);
                    testingScheduleDTO.setConfirm(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                    testingScheduleDTO.setPlus(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                    break;
                default:
                    testingScheduleDTO.setOvum(RemindStatusConst.SCHEDULE_ON);
                    testingScheduleDTO.setMax(RemindStatusConst.SCHEDULE_ALWAYS_ON);
                    testingScheduleDTO.setConfirm(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                    testingScheduleDTO.setPlus(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                    break;
            }
            if (inTestingScheduleExchange) {
                testingScheduleDTO.setMax(RemindStatusConst.SCHEDULE_ON);
                testingScheduleDTO.setPlus(RemindStatusConst.SCHEDULE_OFF);
                testingScheduleDTO.setConfirm(RemindStatusConst.SCHEDULE_OFF);
            }

            if (Integer.valueOf(1).equals(trackingMenopause)) {
                testingScheduleDTO.setOvum(RemindStatusConst.SCHEDULE_ALWAYS_ON);
                testingScheduleDTO.setMax(RemindStatusConst.SCHEDULE_ALWAYS_ON);
                testingScheduleDTO.setConfirm(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
                testingScheduleDTO.setPlus(RemindStatusConst.SCHEDULE_NOT_DISPLAY);
            }

            BeanUtil.copyProperties(testingScheduleDTO, option);
            options.add(option);
        }
        return options;
    }

    /**
     * 注册用户流程中添加remindTime的默认设置，默认推送时间为用户本地时间的上午7点
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDefaultRemindTime(Long userId, String timeZone) {
        AppUserReminderEntity userReminderEntity = appUserReminderDAO.getByUserId(userId);
        if (ObjectUtils.isNotEmpty(userReminderEntity)) {
            return;
        }

        userReminderEntity = new AppUserReminderEntity();
        userReminderEntity.setUserId(userId);
        userReminderEntity.setRemindFlag(1);
        userReminderEntity.setCyclePhaseFlag(1);
        userReminderEntity.setHideContentFlag(1);
        userReminderEntity.setTestingScheduleFlag(1);
        // Purchase 购买建议通知标识，默认0关闭，1开启
        userReminderEntity.setPurchaseFlag(0);
        // 隐藏购买建议通知开关，0-关闭，1-打开,默认关闭
        userReminderEntity.setHidePurchaseFlag(0);
        // 温度测试推送总开关，0-关闭，1-打开(默认0)
        userReminderEntity.setBbtTestingFlag(0);
        // 隐藏温度测试推送开关，0-关闭，1-打开,默认关闭
        userReminderEntity.setHideBbtFlag(0);
        String remindTimeStr = "2023-03-03 07:00:00";
        userReminderEntity.setRemindTimeStr(remindTimeStr);
        userReminderEntity.setRemindTime(ZoneDateUtil.timestamp(timeZone, remindTimeStr, DatePatternConst.DATE_TIME_PATTERN));
        userReminderEntity.setTestingScheduleRemindTimeStr(remindTimeStr);
        userReminderEntity.setTestingScheduleRemindTime(ZoneDateUtil.timestamp(timeZone, remindTimeStr, DatePatternConst.DATE_TIME_PATTERN));
        userReminderEntity.setBbtTestingRemindTimeStr(remindTimeStr.substring(11));
        userReminderEntity.setBbtTestingRemindTime(ZoneDateUtil.timestamp(timeZone, remindTimeStr, DatePatternConst.DATE_TIME_PATTERN));
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userReminderEntity);
        appUserReminderDAO.save(userReminderEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public AppUserTestingScheduleEntity defaultSchedule(Long userId, String timeZone, Integer goalStatus) {
        AppUserTestingScheduleEntity userTestingScheduleEntity = appUserTestingScheduleDAO.getByUserId(userId);
        boolean inTestingScheduleExchange = userReminderComplaintDAO.getCountByUserId(userId) > 0;
        if (ObjectUtils.isEmpty(userTestingScheduleEntity)) {
            // create
            userTestingScheduleEntity = new AppUserTestingScheduleEntity();
            userTestingScheduleEntity.setUserId(userId);
            handlerDefaultScheduleGoalStatus(userId, goalStatus, inTestingScheduleExchange, userTestingScheduleEntity);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userTestingScheduleEntity);
            appUserTestingScheduleDAO.save(userTestingScheduleEntity);
        } else {
            // update
            handlerDefaultScheduleGoalStatus(userId, goalStatus, inTestingScheduleExchange, userTestingScheduleEntity);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userTestingScheduleEntity);
            appUserTestingScheduleDAO.updateById(userTestingScheduleEntity);
        }
        return userTestingScheduleEntity;
    }

    private void handlerDefaultScheduleGoalStatus(Long userId, Integer goalStatus, Boolean inTestingScheduleExchange, AppUserTestingScheduleEntity userTestingScheduleEntity) {
        userTestingScheduleEntity.setMax(RemindStatusConst.OPEN);
        switch (UserGoalEnum.get(goalStatus)) {
            case CYCLE_TRACKING:
                userTestingScheduleEntity.setOvum(RemindStatusConst.SCHEDULE_OFF);
                break;
            case TTC:
            case OFT:
            default:
                userTestingScheduleEntity.setOvum(RemindStatusConst.SCHEDULE_ON);
                break;
        }
        if (inTestingScheduleExchange) {
            AppUserReminderEntity appUserReminderEntity = appUserReminderDAO.getByUserId(userId);
            Integer confirm = appUserReminderEntity.getFertilityConfirmWands();
            userTestingScheduleEntity.setConfirm(confirm);
            userTestingScheduleEntity.setPlus(confirm);
            if (RemindStatusConst.SCHEDULE_ON.equals(confirm)) {
                userTestingScheduleEntity.setMax(RemindStatusConst.SCHEDULE_OFF);
            } else {
                userTestingScheduleEntity.setMax(appUserReminderEntity.getMaxWands());
            }
        }
    }

    @Override
    public void changeDefinedIrregularCycleValue(Integer value) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        userInfoEntity.setDefinedIrregularCycle(value);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);
    }
}
