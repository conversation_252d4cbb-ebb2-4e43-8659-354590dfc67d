package com.mira.user.service.user.impl;

import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.util.PeriodUtil;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.thirdparty.consts.KlaviyoPropertyConst;
import com.mira.api.user.dto.user.diary.UserMedicineDTO;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.StringListUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.async.KlaviyoProducer;
import com.mira.user.controller.vo.menopause.MenopauseSurveyResultVO;
import com.mira.user.dal.dao.AppUserInfoDAO;
import com.mira.user.dal.dao.AppUserMenopauseSurveyDAO;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import com.mira.user.dal.entity.AppUserInfoEntity;
import com.mira.user.dal.entity.AppUserMenopauseSurveyEntity;
import com.mira.user.dal.entity.AppUserPeriodEntity;
import com.mira.user.dto.info.v5.*;
import com.mira.user.enums.user.v5.MenopausePageCodeEnum;
import com.mira.user.service.manager.AlgorithmCallManager;
import com.mira.user.service.manager.CacheManager;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import com.mira.user.service.user.IUserInfoV5Service;
import com.mira.user.service.util.CallEditPeriodUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-06-11
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class UserInfoV5ServiceImpl implements IUserInfoV5Service {
    private final AppUserInfoDAO appUserInfoDAO;
    private final AppUserMenopauseSurveyDAO appUserMenopauseSurveyDAO;
    private final CacheManager cacheManager;
    private final AlgorithmCallManager algorithmCallManager;
    private final ISsoProvider ssoProvider;
    private final AppUserPeriodDAO appUserPeriodDAO;
    private final KlaviyoProducer klaviyoProducer;

    @Override
    public void checkTrackingMenopause(Boolean select, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        Integer goalStatus = userInfoEntity.getGoalStatus();
        if (select) {
            userInfoEntity.setTrackingMenopause(1);
            //从Fertility Status Check(4)选择的menopause，需要将goal切换到 Cycle tracking
            if (UserGoalEnum.OFT.getValue().equals(goalStatus)) {
                userInfoEntity.setGoalStatus(UserGoalEnum.CYCLE_TRACKING.getValue());
            }
        } else {
            userInfoEntity.setTrackingMenopause(0);
            //这个时间不更新，保持第一次的时间
            //userInfoEntity.setTrackingMenopauseDate(null);
        }
        if (update == null) {
            //只有on boarding才会设置这个时间
            String trackingMenopauseDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
            userInfoEntity.setTrackingMenopauseDate(trackingMenopauseDate);
        }

        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);
        cacheManager.deleteUserDetailCache(userId);

        if (update == null) {
            callAlgorithm(userInfoEntity);
        }

        klaviyoProducer.addMenopauseProperty(userId, KlaviyoPropertyConst.MENOPAUSE_TRACKING, select ? "Active" : "Inactive");
        klaviyoProducer.addMenopauseProperty(userId, KlaviyoPropertyConst.LAUNCH_MENOPAUSE_MODE, update == null ?
                "onboarding" : "profile");

    }

    private void callAlgorithm(AppUserInfoEntity appUserInfoEntity) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();

        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(appUserInfoEntity.getUserId());
        List<Long> dbPeriodList = StringListUtil.strToLongList(appUserPeriodEntity.getPeriods(), ",");
        // 以数据库的时间戳+用户当前时区来转换成用户本地时间
        List<String> periodStrList = PeriodUtil.periodsLong2String(dbPeriodList, loginInfo.getTimeZone());
        List<UserPeriodDataDTO> userPeriodDataDTOList;
        if (StringUtils.isBlank(appUserPeriodEntity.getCutPoints())) {
            userPeriodDataDTOList = PeriodUtil.buildPeriodList(appUserPeriodEntity.getAvgLenPeriod(), periodStrList);
        } else {
            List<String> cutPoints = JsonUtil.toArray(appUserPeriodEntity.getCutPoints(), String.class);
            userPeriodDataDTOList = PeriodUtil.buildPeriodListWithInterval(appUserPeriodEntity.getAvgLenPeriod(),
                    periodStrList, cutPoints);
        }
        // 上下文保存最近的一个周期信息
        ContextHolder.put("lastPeriodData", CollectionUtils.isEmpty(userPeriodDataDTOList) ? null : userPeriodDataDTOList.get(userPeriodDataDTOList.size() - 1));

        // 构建经期信息
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(loginInfo.getId()).getData();
        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity, userPeriodDataDTOList);
        // 调用算法
        PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(loginInfo.getTimeZone(),
                loginUserInfoDTO.getEmail(), appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
        algorithmCallManager.editPeriod(prepareEditPeriodDTO, AlgorithmRequestTypeEnum.CHANGE_MENOPAUSE_FLAG);
    }

    @Override
    public void menopauseProcedure(MenopauseProcedureDTO menopauseProcedureDTO, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        List<Integer> procedures = menopauseProcedureDTO.getProcedures();
        String procedureList = StringListUtil.listToString(procedures, ",");
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        if (menopauseSurveyEntity == null) {
            menopauseSurveyEntity = new AppUserMenopauseSurveyEntity();
            menopauseSurveyEntity.setUserId(userId);
            menopauseSurveyEntity.setProcedures(procedureList);
            this.setMenopausePageCode(menopauseSurveyEntity);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, menopauseSurveyEntity);
            appUserMenopauseSurveyDAO.save(menopauseSurveyEntity);
        } else {
            menopauseSurveyEntity.setProcedures(procedureList);
            if (update == null) {
                //update != null代表更新，为空表示onboarding时候设置
                this.setMenopausePageCode(menopauseSurveyEntity);
            }
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
            appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
        }
    }

    @Override
    public void menopauseTakeMedication(Integer select, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        menopauseSurveyEntity.setTakeMedication(select);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
    }

    @Override
    public void menopauseMedications(List<UserMedicineDTO> medications, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);

        if (CollectionUtils.isEmpty(medications)) {
            medications = new ArrayList<>();
        }
        String medicationStr = JsonUtil.toJson(medications);
        menopauseSurveyEntity.setMedications(medicationStr);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
    }

    @Override
    public void menopauseRegularPeriod(MenopauseRegularPeriodDTO menopauseRegularPeriodDTO, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        List<Integer> regularPeriodDTOValues = menopauseRegularPeriodDTO.getValues();
        String regularPeriods = StringListUtil.listToString(regularPeriodDTOValues, ",");
        menopauseSurveyEntity.setRegularPeriods(regularPeriods);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
    }

    @Override
    public void menopauseMotherEnter(Integer age, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        menopauseSurveyEntity.setMotherEnterAge(age);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
    }

    @Override
    public void checkHrt(Integer select, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        menopauseSurveyEntity.setHrt(select);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
    }

    @Override
    public void menopauseHrtType(MenopauseHrtTypeDTO menopauseHrtTypeDTO, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        List<Integer> hrtTypeDTOValues = menopauseHrtTypeDTO.getValues();
        String hrtTypes = StringListUtil.listToString(hrtTypeDTOValues, ",");
        menopauseSurveyEntity.setHrtTypes(hrtTypes);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
    }

    @Override
    public void menopauseEverPregnant(Integer value, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        menopauseSurveyEntity.setEverPregnant(value);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
    }

    @Override
    public void menopausePhysicallyActive(Integer value, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        menopauseSurveyEntity.setPhysicallyActive(value);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
    }

    @Override
    public void menopausePhysicallyActiveType(MenopausePhysicallyActiveTypeDTO menopausePhysicallyActiveTypeDTO, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        List<Integer> physicallyActiveTypeDTOValues = menopausePhysicallyActiveTypeDTO.getValues();
        String physicallyActiveTypes = StringListUtil.listToString(physicallyActiveTypeDTOValues, ",");
        menopauseSurveyEntity.setPhysicallyActiveTypes(physicallyActiveTypes);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
    }

    @Override
    public void menopauseGoal(MenopauseGoalDTO menopauseGoalDTO, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        List<Integer> menopauseGoalDTOValues = menopauseGoalDTO.getValues();
        String menopauseGoals = StringListUtil.listToString(menopauseGoalDTOValues, ",");
        menopauseSurveyEntity.setMenopauseGoals(menopauseGoals);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);
    }

    @Override
    public void menopauseSymptom(List<MenopauseSymptomDTO> menopauseSymptoms, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        if (CollectionUtils.isEmpty(menopauseSymptoms)) {
            menopauseSymptoms = new ArrayList<>();
        }
        String menopauseSymptomsStr = JsonUtil.toJson(menopauseSymptoms);
        menopauseSurveyEntity.setMenopauseSymptoms(menopauseSymptomsStr);
        if (update == null) {
            this.setMenopausePageCode(menopauseSurveyEntity);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, menopauseSurveyEntity);
        appUserMenopauseSurveyDAO.updateById(menopauseSurveyEntity);

        //由于用户可以注册后再编辑这个survey，所以这里需要再次将TrackingMenopause设置为1
        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        userInfoEntity.setTrackingMenopause(1);
        if (update == null) {// 更新survey时不需要修改这个时间
            String trackingMenopauseDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
            userInfoEntity.setTrackingMenopauseDate(trackingMenopauseDate);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);
        cacheManager.deleteUserDetailCache(userId);
    }

    @Override
    public Integer menopausePageCode() {
        //默认从第一页开始
        Integer pageCode = 1;
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        if (menopauseSurveyEntity != null) {
            pageCode = menopauseSurveyEntity.getPageCode();
        }

        return pageCode;
    }

    private void setMenopausePageCode(AppUserMenopauseSurveyEntity menopauseSurveyEntity) {
        //默认进入procedures填写页面
        Integer pageCode = MenopausePageCodeEnum.ONE.getValue();
        if (StringUtils.isNotBlank(menopauseSurveyEntity.getMenopauseSymptoms())) {
            //完成了Symptoms填写,这一步之后，survey完成
            pageCode = MenopausePageCodeEnum.DONE.getValue();
        } else if (StringUtils.isNotBlank(menopauseSurveyEntity.getMenopauseGoals())) {
            //完成了Goals填写，需要进入Symptoms填写页面
            pageCode = MenopausePageCodeEnum.TWELVE.getValue();
        } else if (StringUtils.isNotBlank(menopauseSurveyEntity.getPhysicallyActiveTypes())) {
            //完成了PhysicallyActiveType填写，需要进入Goals填写页面
            pageCode = MenopausePageCodeEnum.ELEVEN.getValue();
        } else if (menopauseSurveyEntity.getPhysicallyActive() != null) {
            //需要进入PhysicallyActiveType填写页面
            pageCode = MenopausePageCodeEnum.TEN.getValue();
        } else if (menopauseSurveyEntity.getEverPregnant() != null) {
            //需要进入physicallyActive填写页面
            pageCode = MenopausePageCodeEnum.NINE.getValue();
        } else if (menopauseSurveyEntity.getHrt() != null) {
            if (menopauseSurveyEntity.getHrt() == 0) {
                //需要进入everPregnant填写页面
                pageCode = MenopausePageCodeEnum.EIGHT.getValue();
            } else {
                if (StringUtils.isNotBlank(menopauseSurveyEntity.getHrtTypes())) {
                    //需要进入everPregnant填写页面
                    pageCode = MenopausePageCodeEnum.EIGHT.getValue();
                } else {
                    //需要进入hrtTypes填写页面(12.2)
                    pageCode = MenopausePageCodeEnum.SEVEN.getValue();
                }
            }
        } else if (menopauseSurveyEntity.getMotherEnterAge() != null) {
            //需要进入hrt填写页面
            pageCode = MenopausePageCodeEnum.SIX.getValue();
        } else if (StringUtils.isNotBlank(menopauseSurveyEntity.getRegularPeriods())) {
            //需要进入motherEnterAge填写页面
            pageCode = MenopausePageCodeEnum.FIVE.getValue();
        } else if (menopauseSurveyEntity.getTakeMedication() != null) {
            if (menopauseSurveyEntity.getTakeMedication() == 0) {
                //需要进入regularPeriods填写页面
                pageCode = MenopausePageCodeEnum.FOUR.getValue();
            } else {
                if (StringUtils.isNotBlank(menopauseSurveyEntity.getMedications())) {
                    //需要进入regularPeriods填写页面
                    pageCode = MenopausePageCodeEnum.FOUR.getValue();
                } else {
                    //需要进入medications填写页面
                    pageCode = MenopausePageCodeEnum.THREE.getValue();
                }
            }
        } else if (menopauseSurveyEntity.getProcedures() != null) {
            //需要进入takeMedication填写页面
            pageCode = MenopausePageCodeEnum.TWO.getValue();
        }
        menopauseSurveyEntity.setPageCode(pageCode);
    }

    @Override
    public MenopauseSurveyResultVO surveyResult() {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        AppUserMenopauseSurveyEntity menopauseSurveyEntity = appUserMenopauseSurveyDAO.getByUserId(userId);
        MenopauseSurveyResultVO menopauseSurveyResultVO = new MenopauseSurveyResultVO();
        if (Objects.nonNull(menopauseSurveyEntity)) {
            menopauseSurveyResultVO.setId(menopauseSurveyEntity.getId());
            menopauseSurveyResultVO.setTakeMedication(menopauseSurveyEntity.getTakeMedication());
            menopauseSurveyResultVO.setMotherEnterAge(menopauseSurveyEntity.getMotherEnterAge());
            menopauseSurveyResultVO.setHrt(menopauseSurveyEntity.getHrt());
            menopauseSurveyResultVO.setEverPregnant(menopauseSurveyEntity.getEverPregnant());
            menopauseSurveyResultVO.setPhysicallyActive(menopauseSurveyEntity.getPhysicallyActive());
            menopauseSurveyResultVO.setPageCode(menopauseSurveyEntity.getPageCode());
            if (StringUtils.isNotBlank(menopauseSurveyEntity.getProcedures())) {
                menopauseSurveyResultVO.setProcedures(StringListUtil.strToIntegerList(menopauseSurveyEntity.getProcedures(), ","));
            }
            if (StringUtils.isNotBlank(menopauseSurveyEntity.getMedications())) {
                List<UserMedicineDTO> medications = JsonUtil.toArray(menopauseSurveyEntity.getMedications(), UserMedicineDTO.class);
                menopauseSurveyResultVO.setMedications(medications);
            }
            if (StringUtils.isNotBlank(menopauseSurveyEntity.getRegularPeriods())) {
                menopauseSurveyResultVO.setRegularPeriods(StringListUtil.strToIntegerList(menopauseSurveyEntity.getRegularPeriods(),
                        ","));
            }
            if (StringUtils.isNotBlank(menopauseSurveyEntity.getHrtTypes())) {
                menopauseSurveyResultVO.setHrtTypes(StringListUtil.strToIntegerList(menopauseSurveyEntity.getHrtTypes(), ","));
            }
            if (StringUtils.isNotBlank(menopauseSurveyEntity.getPhysicallyActiveTypes())) {
                menopauseSurveyResultVO.setPhysicallyActiveTypes(StringListUtil.strToIntegerList(menopauseSurveyEntity.getPhysicallyActiveTypes(), ","));
            }
            if (StringUtils.isNotBlank(menopauseSurveyEntity.getMenopauseGoals())) {
                menopauseSurveyResultVO.setMenopauseGoals(StringListUtil.strToIntegerList(menopauseSurveyEntity.getMenopauseGoals(), ","));
            }
            if (StringUtils.isNotBlank(menopauseSurveyEntity.getMenopauseSymptoms())) {
                List<MenopauseSymptomDTO> menopauseSymptoms = JsonUtil.toArray(menopauseSurveyEntity.getMenopauseSymptoms(), MenopauseSymptomDTO.class);
                menopauseSurveyResultVO.setMenopauseSymptoms(menopauseSymptoms);
            }
        } else {
            menopauseSurveyResultVO.setPageCode(1);
        }

        return menopauseSurveyResultVO;
    }
}
