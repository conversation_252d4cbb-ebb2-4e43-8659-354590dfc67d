package com.mira.user.service.user.impl;

import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.dal.dao.AppUserInfoDAO;
import com.mira.user.dal.dao.AppUserOnboardingPageViewDAO;
import com.mira.user.dal.entity.AppUserInfoEntity;
import com.mira.user.dal.entity.AppUserOnboardingPageViewEntity;
import com.mira.api.user.enums.OnboardingStatusEnum;
import com.mira.user.service.manager.CacheManager;
import com.mira.user.service.user.IUserOnboardingPageViewService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-12-19
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class UserOnboardingPageViewServiceImpl implements IUserOnboardingPageViewService {
    private final AppUserOnboardingPageViewDAO appUserOnboardingPageViewDAO;
    private final AppUserInfoDAO appUserInfoDAO;
    private final CacheManager cacheManager;

    @Override
    public String getOnboardingPageView() {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserOnboardingPageViewEntity onboardingPageViewEntity = appUserOnboardingPageViewDAO.getByUserId(userId);
        if (onboardingPageViewEntity != null) {
            return onboardingPageViewEntity.getPageView();
        }
        return "";
    }

    @Override
    public void setOnboardingPageView(String pageView) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        AppUserOnboardingPageViewEntity onboardingPageViewEntity = appUserOnboardingPageViewDAO.getByUserId(userId);
        if (onboardingPageViewEntity == null) {
            onboardingPageViewEntity = new AppUserOnboardingPageViewEntity();
            onboardingPageViewEntity.setUserId(userId);
            onboardingPageViewEntity.setPageView(pageView);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, onboardingPageViewEntity);
            appUserOnboardingPageViewDAO.save(onboardingPageViewEntity);
        } else {
            onboardingPageViewEntity.setPageView(pageView);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, onboardingPageViewEntity);
            appUserOnboardingPageViewDAO.updateById(onboardingPageViewEntity);
        }

        // update onboarding status
        Integer onboardingStatus = null;
        if ("MonitorPage".equals(pageView)) {
            onboardingStatus = OnboardingStatusEnum.PAIRED_DEVICE.getCode();
        }
        if ("MainPage".equals(pageView)) {
            onboardingStatus = OnboardingStatusEnum.HOME_PAGE.getCode();
        }
        if (onboardingStatus != null) {
            AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(userId);
            appUserInfoEntity.setOnboardingStatus(onboardingStatus);
            appUserInfoDAO.updateById(appUserInfoEntity);
        }

        // delete cache
        cacheManager.deleteUserDetailCache(userId);
    }
}
