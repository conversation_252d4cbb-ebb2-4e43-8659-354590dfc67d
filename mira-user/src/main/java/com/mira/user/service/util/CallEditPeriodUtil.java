package com.mira.user.service.util;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.util.PeriodUtil;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.user.util.UserGoalUtil;
import com.mira.core.util.AgeUtil;
import com.mira.core.util.JsonUtil;
import com.mira.user.dal.entity.AppUserPeriodEntity;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * call edit period util
 *
 * <AUTHOR>
 */
public class CallEditPeriodUtil {
    public static UserPeriodParamDTO buildUserPeriodParamDTO(LoginUserInfoDTO loginUserInfoDTO,
                                                             AppUserPeriodEntity appUserPeriodEntity, List<UserPeriodDataDTO> userPeriodDataDTOS) {
        UserPeriodParamDTO userPeriodParamDTO = new UserPeriodParamDTO();
        BeanUtil.copyProperties(appUserPeriodEntity, userPeriodParamDTO);

        userPeriodParamDTO.setUserMode(UserGoalUtil.getUserGoalEnum(loginUserInfoDTO).getValue());
        userPeriodParamDTO.setIsOft(loginUserInfoDTO.getIsOft());
        userPeriodParamDTO.setConditions(loginUserInfoDTO.getConditions());
        userPeriodParamDTO.setAge(AgeUtil.calculateAge(loginUserInfoDTO.getBirthYear(), loginUserInfoDTO.getBirthMonth(), loginUserInfoDTO.getBirthDay()));
        userPeriodParamDTO.setAvgLenPeriod(appUserPeriodEntity.getAvgLenPeriod());
        userPeriodParamDTO.setAvgLenCycle(appUserPeriodEntity.getAvgLenCycle());
        userPeriodParamDTO.setUserPeriodDataDTOS(userPeriodDataDTOS);
        userPeriodParamDTO.setTrackingMenopause(loginUserInfoDTO.getTrackingMenopause());
        userPeriodParamDTO.setTrackingMenopauseDate(loginUserInfoDTO.getTrackingMenopauseDate());

        return userPeriodParamDTO;
    }

    public static PrepareEditPeriodDTO buildPrepareEditPeriodDTO(String timeZone, String email, String periods,
                                                                 UserPeriodParamDTO userPeriodParamDTO) {
        PrepareEditPeriodDTO prepareEditPeriodDTO = new PrepareEditPeriodDTO();
        prepareEditPeriodDTO.setTimeZone(timeZone);
        prepareEditPeriodDTO.setEmail(email);
        prepareEditPeriodDTO.setLastCycleFlag(0);
        prepareEditPeriodDTO.setPeriods(periods);
        prepareEditPeriodDTO.setUserPeriodParamDTO(userPeriodParamDTO);

        return prepareEditPeriodDTO;
    }

    public static List<UserPeriodDataDTO> buildUserPeriodDataDTOS(String timeZone, List<Long> periodList, String cutPoints, Integer avgLenPeriod) {
        List<String> periodStrList = PeriodUtil.periodsLong2String(periodList, timeZone);
        List<UserPeriodDataDTO> userPeriodDataDTOS;
        if (StringUtils.isBlank(cutPoints)) {
            userPeriodDataDTOS = PeriodUtil.buildPeriodList(avgLenPeriod, periodStrList);
        } else {
            List<String> cutPointList = JsonUtil.toArray(cutPoints, String.class);
            userPeriodDataDTOS = PeriodUtil.buildPeriodListWithInterval(avgLenPeriod, periodStrList, cutPointList);
        }
        return userPeriodDataDTOS;
    }

    public static List<UserPeriodDataDTO> buildUserPeriodDataDTOS(List<String> periodList, String cutPoints, Integer avgLenPeriod) {
        List<UserPeriodDataDTO> userPeriodDataDTOS;
        if (StringUtils.isBlank(cutPoints)) {
            userPeriodDataDTOS = PeriodUtil.buildPeriodList(avgLenPeriod, periodList);
        } else {
            List<String> cutPointList = JsonUtil.toArray(cutPoints, String.class);
            userPeriodDataDTOS = PeriodUtil.buildPeriodListWithInterval(avgLenPeriod, periodList, cutPointList);
        }
        return userPeriodDataDTOS;
    }
}
