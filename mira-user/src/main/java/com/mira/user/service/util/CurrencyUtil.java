package com.mira.user.service.util;

import com.mira.api.thirdparty.dto.blog.Ip2CountryDTO;
import com.mira.api.thirdparty.provider.IBlogProvider;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.CountryCodeEnum;
import com.mira.core.consts.enums.CurrencyEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.user.dal.entity.AppUserEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 货币工具
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CurrencyUtil {
    @Resource
    private IBlogProvider blogProvider;

    public String getCurrentCurrency(AppUserEntity appUser) {
        String countryCode = appUser.getCountryCode();
        String continentCode = appUser.getContinentCode();
        String currentCurrency = appUser.getCurrentCurrency();

        if (StringUtils.isNotBlank(currentCurrency)) {
            return currentCurrency;
        }

        if (StringUtils.isNotBlank(countryCode) && StringUtils.isNotBlank(continentCode)) {
            CurrencyEnum currencyEnum = CountryCodeEnum.getCurrencyByCountry(countryCode, continentCode);
            return currencyEnum.getCode();
        }

        String ip = ContextHolder.get(HeaderConst.IP);
        if (StringUtils.isEmpty(ip)) {
            return CurrencyEnum.USD.getCode();
        }

        try {
            CommonResult<Ip2CountryDTO> countryByIp = blogProvider.getCountryByIp(ip, appUser.getId());
            Ip2CountryDTO ip2CountryDTO = countryByIp.getData();
            CurrencyEnum currencyEnum = CountryCodeEnum.getCurrencyByCountry(ip2CountryDTO.getCountryCode(), ip2CountryDTO.getContinentCode());
            return currencyEnum.getCode();
        } catch (Exception e) {
            log.error("get currency error, ip:{}", ip, e);
        }
        return CurrencyEnum.USD.getCode();
    }
}
