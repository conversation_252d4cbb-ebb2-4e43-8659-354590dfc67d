package com.blackj.springbootdemo.demo;

import cn.hutool.crypto.digest.MD5;
import com.blackj.springbootdemo.utils.JsonUtil;
import com.blackj.springbootdemo.utils.PasswordUtil;
import com.blackj.springbootdemo.utils.Sha256Util;
import com.blackj.springbootdemo.utils.StringUtil;
import com.google.common.base.Joiner;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;

public class Demo {

    public static void main(String[] args) throws Exception {
//        System.out.println(UUID.randomUUID().toString().replaceAll("-", ""));
//        System.out.println(PasswordUtil.encryptPassword("b6fe10e0676f47a8a79c3c12ca336dba3e7478fbae85457abf4d0e766377bc29", null));
    }
}
