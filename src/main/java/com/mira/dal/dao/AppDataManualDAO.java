package com.mira.dal.dao;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.dal.entity.AppDataManualEntity;
import com.mira.dal.mapper.AppDataManualMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_data_manual DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppDataManualDAO extends ServiceImpl<AppDataManualMapper, AppDataManualEntity> {
    public List<AppDataManualEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppDataManualEntity>lambdaQuery()
                .eq(AppDataManualEntity::getUserId, userId)
                .orderByDesc(AppDataManualEntity::getCreateTime));
    }
}
