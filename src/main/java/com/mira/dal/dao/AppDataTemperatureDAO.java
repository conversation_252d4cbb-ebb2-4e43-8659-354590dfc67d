package com.mira.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.dal.entity.AppDataTemperatureEntity;
import com.mira.dal.mapper.AppDataTemperatureMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AppDataTemperatureDAO extends ServiceImpl<AppDataTemperatureMapper, AppDataTemperatureEntity> {
    public List<AppDataTemperatureEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppDataTemperatureEntity>lambdaQuery()
                .eq(AppDataTemperatureEntity::getUserId, userId)
                .orderByDesc(AppDataTemperatureEntity::getId));
    }
}
