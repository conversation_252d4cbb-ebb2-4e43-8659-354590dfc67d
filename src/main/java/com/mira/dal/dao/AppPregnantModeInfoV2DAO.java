package com.mira.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppPregnantModeInfoV2Entity;
import com.mira.user.dal.mapper.AppPregnantModeInfoV2Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_access_log DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppPregnantModeInfoV2DAO extends ServiceImpl<AppPregnantModeInfoV2Mapper, AppPregnantModeInfoV2Entity> {
    /**
     * 获取最近的怀孕模式信息
     * 如果没有，返回null
     * 如果isEnd为1，表示最近一次怀孕模式已经结束，返回null
     *
     * @param userId
     * @return
     */
    public AppPregnantModeInfoV2Entity getRecentPregnantModeInfoByUserId(Long userId) {
        QueryWrapper<AppPregnantModeInfoV2Entity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("modify_time");
        queryWrapper.last("limit 1");
        AppPregnantModeInfoV2Entity recentPregnantModeInfo = this.getOne(queryWrapper);
        if (recentPregnantModeInfo == null) {
            return null;
        }
        Integer isEnd = recentPregnantModeInfo.getIsEnd();
        if (isEnd == 1) {
            return null;
        }
        return recentPregnantModeInfo;
    }

    public List<AppPregnantModeInfoV2Entity> getPregnantModeInfoListByUserId(Long userId) {
        QueryWrapper<AppPregnantModeInfoV2Entity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("modify_time");
        return this.list(queryWrapper);
    }

}
