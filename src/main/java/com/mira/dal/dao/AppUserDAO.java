package com.mira.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.dal.entity.AppUserEntity;
import com.mira.dal.mapper.AppUserMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_user DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserDAO extends ServiceImpl<AppUserMapper, AppUserEntity> {
    public List<AppUserEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppUserEntity>lambdaQuery()
                .eq(AppUserEntity::getId, userId)
                .orderByDesc(AppUserEntity::getId));
    }
}
