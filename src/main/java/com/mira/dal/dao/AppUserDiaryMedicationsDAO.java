package com.mira.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppUserDiaryMedicationsEntity;
import com.mira.user.dal.mapper.AppUserDiaryMedicationsMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_user_diary_medications DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserDiaryMedicationsDAO extends ServiceImpl<AppUserDiaryMedicationsMapper, AppUserDiaryMedicationsEntity> {
    public AppUserDiaryMedicationsEntity getByUserIdAndDayStr(Long userId, String dayStr) {
        return getOne(Wrappers.<AppUserDiaryMedicationsEntity>lambdaQuery()
                .eq(AppUserDiaryMedicationsEntity::getUserId, userId)
                .eq(AppUserDiaryMedicationsEntity::getDiaryDayStr, dayStr));
    }

    public List<AppUserDiaryMedicationsEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppUserDiaryMedicationsEntity>lambdaQuery()
                            .eq(AppUserDiaryMedicationsEntity::getUserId, userId));
    }

    public List<AppUserDiaryMedicationsEntity> listByUserIdInDayStr(Long userId, List<String> dates) {
        return list(Wrappers.<AppUserDiaryMedicationsEntity>lambdaQuery()
                .eq(AppUserDiaryMedicationsEntity::getUserId, userId)
                .in(AppUserDiaryMedicationsEntity::getDiaryDayStr, dates));
    }

    public List<AppUserDiaryMedicationsEntity> listByUserIdAndBetweenDay(Long userId, String startDay, String endDay) {
        return list(Wrappers.<AppUserDiaryMedicationsEntity>lambdaQuery()
                .eq(AppUserDiaryMedicationsEntity::getUserId, userId)
                .ge(AppUserDiaryMedicationsEntity::getDiaryDayStr, startDay)
                .le(AppUserDiaryMedicationsEntity::getDiaryDayStr, endDay));
    }
}
