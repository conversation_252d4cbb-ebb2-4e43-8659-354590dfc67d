package com.mira.gpt.translate.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppUserDiaryMoodsEntity;
import com.mira.user.dal.mapper.AppUserDiaryMoodsMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AppUserDiaryMoodsDAO extends ServiceImpl<AppUserDiaryMoodsMapper, AppUserDiaryMoodsEntity> {
    public List<AppUserDiaryMoodsEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppUserDiaryMoodsEntity>lambdaQuery()
                .eq(AppUserDiaryMoodsEntity::getUserId, userId));
    }

    public List<AppUserDiaryMoodsEntity> listByUserIdInDay(Long userId, List<String> dates) {
        return list(Wrappers.<AppUserDiaryMoodsEntity>lambdaQuery()
                .eq(AppUserDiaryMoodsEntity::getUserId, userId)
                .in(AppUserDiaryMoodsEntity::getDiaryDayStr, dates));
    }

    public AppUserDiaryMoodsEntity getByUserIdAndDiaryDayStr(Long userId, String dateStr) {
        return getOne(Wrappers.<AppUserDiaryMoodsEntity>lambdaQuery()
                .eq(AppUserDiaryMoodsEntity::getUserId, userId)
                .eq(AppUserDiaryMoodsEntity::getDiaryDayStr, dateStr));
    }

    public long countByUserId(Long userId) {
        return count(Wrappers.<AppUserDiaryMoodsEntity>lambdaQuery()
                .eq(AppUserDiaryMoodsEntity::getUserId, userId));
    }
}
