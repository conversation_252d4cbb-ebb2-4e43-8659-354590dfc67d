package com.mira.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppUserSexConfigEntity;
import com.mira.user.dal.mapper.AppUserSexConfigMapper;
import org.springframework.stereotype.Repository;

@Repository
public class AppUserSexConfigDAO extends ServiceImpl<AppUserSexConfigMapper, AppUserSexConfigEntity> {
    public AppUserSexConfigEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppUserSexConfigEntity>lambdaQuery()
                .eq(AppUserSexConfigEntity::getUserId, userId));
    }
}
