package com.mira.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppUserTemperatureEntity;
import com.mira.user.dal.mapper.AppUserTemperatureMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_user_temperature DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserTemperatureDAO extends ServiceImpl<AppUserTemperatureMapper, AppUserTemperatureEntity> {
    public boolean deleteByUserIdAndTesttime(Long userId, String testTime, Integer autoFlag) {
        return remove(Wrappers.<AppUserTemperatureEntity>lambdaUpdate()
                .eq(AppUserTemperatureEntity::getUserId, userId)
                .eq(AppUserTemperatureEntity::getTempTime, testTime)
                .eq(AppUserTemperatureEntity::getAutoFlag, autoFlag));
    }

    public List<AppUserTemperatureEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppUserTemperatureEntity>lambdaQuery()
                .eq(AppUserTemperatureEntity::getUserId, userId)
                .orderByAsc(AppUserTemperatureEntity::getTempTime));
    }

    public List<AppUserTemperatureEntity> listByUserIdAndDay(Long userId, String day) {
        return list(Wrappers.<AppUserTemperatureEntity>lambdaQuery()
                .eq(AppUserTemperatureEntity::getUserId, userId)
                .eq(AppUserTemperatureEntity::getTempDay, day)
                .orderByAsc(AppUserTemperatureEntity::getTempTime));
    }

    public List<AppUserTemperatureEntity> listByUserIdInDayStr(Long userId, List<String> dates) {
        return list(Wrappers.<AppUserTemperatureEntity>lambdaQuery()
                .eq(AppUserTemperatureEntity::getUserId, userId)
                .in(AppUserTemperatureEntity::getTempDay, dates));
    }

    public List<AppUserTemperatureEntity> listByUserIdAndBetweenDay(Long userId, String startDay, String endDay) {
        return list(Wrappers.<AppUserTemperatureEntity>lambdaQuery()
                .eq(AppUserTemperatureEntity::getUserId, userId)
                .ge(AppUserTemperatureEntity::getTempDay, startDay)
                .le(AppUserTemperatureEntity::getTempDay, endDay)
                .orderByAsc(AppUserTemperatureEntity::getTempTime));
    }

    public AppUserTemperatureEntity getOneBySql(Long id) {
        String sql = "select * from app_user_temperature where id=" + id;
        return baseMapper.getOneBySql(sql);
    }
}
