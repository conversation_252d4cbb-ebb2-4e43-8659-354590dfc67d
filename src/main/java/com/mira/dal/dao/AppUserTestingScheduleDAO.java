package com.mira.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppUserTestingScheduleEntity;
import com.mira.user.dal.mapper.AppUserTestingScheduleMapper;
import org.springframework.stereotype.Repository;

@Repository
public class AppUserTestingScheduleDAO extends ServiceImpl<AppUserTestingScheduleMapper, AppUserTestingScheduleEntity> {
    public AppUserTestingScheduleEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppUserTestingScheduleEntity>lambdaQuery()
                .eq(AppUserTestingScheduleEntity::getUserId, userId));
    }
}
