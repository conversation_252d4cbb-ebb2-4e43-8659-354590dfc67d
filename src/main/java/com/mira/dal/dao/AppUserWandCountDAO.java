package com.mira.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.dal.entity.AppUserWandCountEntity;
import com.mira.dal.mapper.AppUserWandCountMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-12-12
 **/
@Repository
public class AppUserWandCountDAO extends ServiceImpl<AppUserWandCountMapper, AppUserWandCountEntity> {
    public List<AppUserWandCountEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppUserWandCountEntity>lambdaQuery()
                .eq(AppUserWandCountEntity::getUserId, userId)
                .orderByDesc(AppUserWandCountEntity::getId));
    }
}
