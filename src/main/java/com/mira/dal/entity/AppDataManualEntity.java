package com.mira.gpt.translate.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 手动添加测试数据
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_data_manual")
public class AppDataManualEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 试剂完成时间
     */
    private String completeTime;

    /**
     * 试剂完成时间戳
     */
    private Long completeTimestamp;

    /**
     * 试剂类型
     */
    private String testWandType;

    /**
     * 试剂批次
     */
    private String testWandBatch;

    /**
     * t1浓度值
     */
    private BigDecimal t1ConValue;

    /**
     * t2浓度值
     */
    private BigDecimal t2ConValue;

    /**
     * t3浓度值
     */
    private BigDecimal t3ConValue;

    private String photoUrl1;
    private String photoUrl2;
    private String photoUrl3;

    /**
     * 状态: 0:未处理;1:正在处理(没有);2:添加成功;3:添加失败(默认0)
     * 状态: 0:未处理;1:正在处理;2:Your data was added;3:Your data was synchronized;4:Your data was not added to your profile
     */
    private Integer status;

    /**
     * 通知状态: 0:未通知(默认);29:Your data was added;30:Your data was synchronized;31:Your data was not added to your profile
     */
    private Long notificationStatus;

    /**
     * 0:等于数值；1:大于数值；2:小于数值(默认0)
     */
    private Integer data1Compare;
    private Integer data2Compare;
    private Integer data3Compare;
}
