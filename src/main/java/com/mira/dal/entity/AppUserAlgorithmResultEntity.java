package com.mira.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户算法结果
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_algorithm_result")
public class AppUserAlgorithmResultEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 周期数据json
     */
    private String cycleData;

    /**
     * 测试数据json
     */
    private String hormoneData;

    /**
     * 周期分析数据
     */
    private String cycleAnalysis;

    /**
     * 额外分析数据
     */
    private String extraResult;

    /**
     * 参考BarTipEnum
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer barTip;

    /**
     * 阈值，参考ThresholdModeEnum
     */
    private Integer thresholdMode;
}
