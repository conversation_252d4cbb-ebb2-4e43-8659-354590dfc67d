package com.mira.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.api.user.enums.daily.*;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("app_user_diary_moods")
public class AppUserDiaryMoodsEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 日记时间戳,年月日（不带时分秒）
     */
    private Long diaryDay;

    /**
     * 日记时间（年月日）
     */
    private String diaryDayStr;

    /**
     * 心情
     *
     * @see DailyStatusMoodEnum
     */
    private String mood;

    /**
     * 心情干扰
     * - Is your mood interfering with life?
     * -- Not at all
     * -- Moderately
     * -- Strongly
     *
     * @see DailyStatusMoodInterferingEnum
     */
    private String moodInterfering;

    /**
     * - Do you feel any of the following?
     * -- Hopeless
     * -- Tearful
     * -- Angry outbursts
     * -- Excited
     * -- Bad sleep
     * -- Trouble focusing
     *
     * @see DailyStatusMoodFeelingEnum
     */
    private String feeling;


    /**
     * 性欲
     * - Super sexy
     * - Not feeling it at all
     * - Neutral
     *
     * @see DailyStatusSexEnum
     */
    private String sexDrive;

    /**
     * 生产力
     * - Productivity at work?
     * -- Very productive
     * -- Not productive, tired
     * -- Neutral
     *
     * @see DailyStatusMoodProductivityEnum
     */
    private String productivity;

    /**
     * 渴望
     * - Cravings?
     * -- Yes, junk food
     * -- Yes, healthy snacks
     * -- Regular appetite
     * -- Reduced appetite
     *
     * @see DailyStatusMoodCravingEnum
     */
    private String cravings;

    /**
     * 锻炼
     * - Did you exercise?
     * -- Yes, it felt great
     * -- Yes, it felt hard
     * -- I don’t exercise
     * -- No, feeling sluggish
     *
     * @see DailyStatusMoodExerciseEnum
     */
    private String exercise;

    /**
     * 皮肤
     * - How’s your skin?
     * -- Dry
     * -- Oily
     * -- Breaking out
     * -- Glowing
     * -- Normal
     *
     * @see DailyStatusMoodSkinEnum
     */
    private String skin;

    /**
     * 外表
     * - How do you feel about your looks?
     * -- Really great
     * -- I don’t like myself
     * -- Neutral
     *
     * @see DailyStatusMoodLooksEnum
     */
    private String looks;
}
