package com.mira.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 保存更年期相关结果信息
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-07-16
 **/
@Getter
@Setter
@TableName("app_user_menopause_result")
public class AppUserMenopauseResultEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * menopause进度百分比,0.2
     */
    private Float progressStatus;
    /**
     * menopause定义Stage的date,"2024-07-12"
     */
    private String defineStageDate;
    /**
     * menopause后台Status:
     * null 或者 0 : 无标识
     * 1: in process
     * 2:result+waiting
     * 3: in process+result
     */
    private Integer backendStatus;
    /**
     */
    private Integer defineStage;
    /**
     * Flow right after the last necessary test completed - stage defined
     * What happen next? 0(default):I'll just log my symptoms;1:Remind me to test
     */
    private Integer afterCompletedRemind;


}
