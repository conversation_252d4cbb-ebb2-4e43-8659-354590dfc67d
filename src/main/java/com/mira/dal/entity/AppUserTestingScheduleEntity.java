package com.mira.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("app_user_testing_schedule")
public class AppUserTestingScheduleEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 是否推荐对应试剂的测试日，0否；1是
     */
    private Integer plus;

    /**
     * 是否推荐对应试剂的测试日，0否；1是
     */
    private Integer confirm;

    /**
     * 是否推荐对应试剂的测试日，0否；1是
     */
    private Integer max;

    /**
     * 是否推荐对应试剂的测试日，0否；1是
     */
    private Integer ovum;
}
