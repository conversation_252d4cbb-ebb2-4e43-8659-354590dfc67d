package com.mira.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户信息表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user")
public class OnboardingConditionEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 类别
     */
    private Integer type;

    /**
     * 选项
     */
    private String conditions;

    /**
     * 创建时间
     */
    protected Long createTime;

    /**
     * 创建时间
     */
    protected String createTimeStr;

    /**
     * 修改时间
     */
    protected Long modifyTime;

    /**
     * 修改时间
     */
    protected String modifyTimeStr;

    /**
     * 状态 0 正常状态 1 删除状态
     */
    @TableLogic
    protected Integer deleted;
}
