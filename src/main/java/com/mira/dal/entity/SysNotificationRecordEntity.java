package com.mira.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 系统通知记录
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("sys_notification_record")
public class SysNotificationRecordEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 通知定义id
     */
    private Long notificationDefineId;

    /**
     * 是否已读：0-未读, 1-已读, 2-由已读重新标记为未读
     */
    @TableField("`read`")
    private Integer read;

    /**
     * 推送类型：0客户端本地推送，1 firebase iOS推送，2 firebase 安卓推送，3 静默通知
     */
    private Integer pushType;

    /**
     * 推送状态：0成功；-1未成功
     */
    private Integer pushStatus;

    /**
     * 推送的文字内容
     */
    private String content;

    /**
     * 过期时间
     */
    private Long expireTime;
}
