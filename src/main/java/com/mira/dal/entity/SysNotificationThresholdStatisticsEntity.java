package com.mira.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户高值复测提醒统计表
 */
@Getter
@Setter
@TableName("sys_notification_threshold_statistics")
public class SysNotificationThresholdStatisticsEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 通知定义id
     */
    private Long notificationDefineId;

    /**
     * 提醒时间
     */
    private Long reminderTime;

    /**
     * 提醒时间
     */
    private String reminderTimeStr;
}
