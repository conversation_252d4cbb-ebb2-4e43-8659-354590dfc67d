package com.mira.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("user_period_edit_note")
public class UserPeriodEditNoteEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 删除的预测经期
     */
    private String delPredicted;

    /**
     * 前实际周期的index
     */
    private Integer previousIndex;

    /**
     * 前实际周期的经期开始日
     */
    private String previousStart;

    /**
     * 前实际周期到下个实际周期的长度
     */
    private Integer lenCycle;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 时区
     */
    private String timeZone;
}
