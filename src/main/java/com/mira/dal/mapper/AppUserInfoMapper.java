package com.mira.gpt.translate.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.sso.dal.entity.AppUserInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * app_user_info
 *
 * <AUTHOR>
 */
@Mapper
public interface AppUserInfoMapper extends BaseMapper<AppUserInfoEntity> {
    LoginUserInfoDTO getLoginUser(@Param("userId") Long userId);
}
