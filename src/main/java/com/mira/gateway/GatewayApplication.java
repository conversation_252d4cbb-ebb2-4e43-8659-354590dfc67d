package com.mira.gateway;

import com.mira.rpc.interceptor.FeignRequestInterceptor;
import com.mira.rpc.interceptor.FeignResponseInterceptor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@EnableDiscoveryClient
@ComponentScan(basePackages = "com.mira",
        excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
        value = {FeignRequestInterceptor.class, FeignResponseInterceptor.class})})
@SpringBootApplication
public class GatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
    }

}
