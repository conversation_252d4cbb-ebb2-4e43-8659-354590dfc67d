package com.mira.gateway.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 监控配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class MonitoringConfig {

    /**
     * WebClient连接池健康检查
     */
    @Bean
    public HealthIndicator webClientHealthIndicator(WebClient webClient) {
        return () -> {
            try {
                // 检查WebClient是否可用
                return Health.up()
                        .withDetail("status", "WebClient is available")
                        .withDetail("timestamp", System.currentTimeMillis())
                        .build();
            } catch (Exception e) {
                log.error("WebClient health check failed", e);
                return Health.down()
                        .withDetail("error", e.getMessage())
                        .withDetail("timestamp", System.currentTimeMillis())
                        .build();
            }
        };
    }

    /**
     * 连接池健康检查
     */
    @Bean
    public HealthIndicator connectionPoolHealthIndicator() {
        return () -> {
            try {
                // 获取连接池统计信息
                ConnectionProvider provider = ConnectionProvider.builder("health-check")
                        .maxConnections(1)
                        .maxIdleTime(Duration.ofSeconds(5))
                        .build();
                
                return Health.up()
                        .withDetail("status", "Connection pool is healthy")
                        .withDetail("timestamp", System.currentTimeMillis())
                        .build();
            } catch (Exception e) {
                log.error("Connection pool health check failed", e);
                return Health.down()
                        .withDetail("error", e.getMessage())
                        .withDetail("timestamp", System.currentTimeMillis())
                        .build();
            }
        };
    }

    /**
     * 自定义指标收集器
     */
    @Bean
    public GatewayMetricsCollector gatewayMetricsCollector(MeterRegistry meterRegistry) {
        return new GatewayMetricsCollector(meterRegistry);
    }

    /**
     * 网关指标收集器
     */
    public static class GatewayMetricsCollector {
        private final MeterRegistry meterRegistry;
        private final AtomicLong activeConnections = new AtomicLong(0);
        private final AtomicLong totalRequests = new AtomicLong(0);
        private final AtomicLong failedRequests = new AtomicLong(0);
        private final Timer requestTimer;

        public GatewayMetricsCollector(MeterRegistry meterRegistry) {
            this.meterRegistry = meterRegistry;
            this.requestTimer = Timer.builder("gateway.request.duration")
                    .description("Gateway request duration")
                    .register(meterRegistry);

            // 注册自定义指标
            meterRegistry.gauge("gateway.connections.active", activeConnections);
            meterRegistry.gauge("gateway.requests.total", totalRequests);
            meterRegistry.gauge("gateway.requests.failed", failedRequests);
        }

        public void incrementActiveConnections() {
            activeConnections.incrementAndGet();
        }

        public void decrementActiveConnections() {
            activeConnections.decrementAndGet();
        }

        public void incrementTotalRequests() {
            totalRequests.incrementAndGet();
        }

        public void incrementFailedRequests() {
            failedRequests.incrementAndGet();
        }

        public Timer.Sample startTimer() {
            return Timer.start(meterRegistry);
        }

        public void stopTimer(Timer.Sample sample) {
            sample.stop(requestTimer);
        }
    }
}
