package com.mira.gateway.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * web client <br/>
 * 非阻塞、异步，配合 Spring WebFlux
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class WebClientConfig {

    // 默认配置值，避免配置缺失导致启动失败
    @Value("${webclient.auth.connect-timeout:10000}")
    private int connectTimeout;

    @Value("${webclient.auth.response-timeout:PT30S}")
    private Duration responseTimeout;

    @Value("${webclient.auth.read-timeout:30}")
    private int readTimeout;

    @Value("${webclient.auth.write-timeout:30}")
    private int writeTimeout;

    @Value("${webclient.auth.pool.max-connections:100}")
    private int maxConnections;

    @Value("${webclient.auth.pool.max-idle-time:PT30S}")
    private Duration maxIdleTime;

    @Value("${webclient.auth.pool.max-life-time:PT60S}")
    private Duration maxLifeTime;

    @Value("${webclient.auth.pool.pending-acquire-timeout:PT10S}")
    private Duration pendingAcquireTimeout;

    @Value("${webclient.auth.pool.evict-in-background:PT30S}")
    private Duration evictInBackground;

    @Bean
    @LoadBalanced
    public WebClient.Builder loadBalancedWebClientBuilder() {
        // 创建连接池配置
        ConnectionProvider connectionProvider = ConnectionProvider.builder("auth-pool")
                .maxConnections(maxConnections)
                .maxIdleTime(maxIdleTime)
                .maxLifeTime(maxLifeTime)
                .pendingAcquireTimeout(pendingAcquireTimeout)
                .evictInBackground(evictInBackground)
                // 启用连接池指标监控
                .metrics(true)
                .build();

        // 创建HttpClient配置
        HttpClient httpClient = HttpClient.create(connectionProvider)
                // 连接超时
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectTimeout)
                // 启用TCP_NODELAY，减少延迟
                .option(ChannelOption.TCP_NODELAY, true)
                // 启用SO_KEEPALIVE，保持连接活跃
                .option(ChannelOption.SO_KEEPALIVE, true)
                // 响应超时
                .responseTimeout(responseTimeout)
                // 添加读写超时处理器
                .doOnConnected(conn -> {
                    conn.addHandlerLast(new ReadTimeoutHandler(readTimeout, TimeUnit.SECONDS));
                    conn.addHandlerLast(new WriteTimeoutHandler(writeTimeout, TimeUnit.SECONDS));
                })
                // 连接事件监听，用于调试
                .doOnConnected(conn -> log.debug("Connection established: {}", conn))
                .doOnDisconnected(conn -> log.debug("Connection disconnected: {}", conn));

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                // 添加错误处理过滤器
                .filter(errorHandlingFilter())
                // 添加重试过滤器
                .filter(retryFilter())
                // 添加日志过滤器
                .filter(loggingFilter());
    }

    @Bean
    public WebClient webClient(@LoadBalanced WebClient.Builder builder) {
        return builder.build();
    }

    /**
     * 错误处理过滤器
     */
    private ExchangeFilterFunction errorHandlingFilter() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
            if (clientResponse.statusCode().isError()) {
                log.warn("HTTP error response: {} {}",
                    clientResponse.statusCode().value(),
                    clientResponse.statusCode().getReasonPhrase());
            }
            return Mono.just(clientResponse);
        });
    }

    /**
     * 重试过滤器
     */
    private ExchangeFilterFunction retryFilter() {
        return (request, next) -> {
            return next.exchange(request)
                .retryWhen(Retry.backoff(2, Duration.ofMillis(500))
                    .maxBackoff(Duration.ofSeconds(2))
                    .filter(throwable -> {
                        // 只对特定异常进行重试
                        return throwable instanceof java.net.ConnectException ||
                               throwable instanceof java.util.concurrent.TimeoutException ||
                               throwable instanceof reactor.netty.channel.AbortedException;
                    })
                    .doBeforeRetry(retrySignal ->
                        log.warn("Retrying request due to: {}", retrySignal.failure().getMessage()))
                );
        };
    }

    /**
     * 日志过滤器
     */
    private ExchangeFilterFunction loggingFilter() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            if (log.isDebugEnabled()) {
                log.debug("WebClient request: {} {}", clientRequest.method(), clientRequest.url());
            }
            return Mono.just(clientRequest);
        });
    }
}
