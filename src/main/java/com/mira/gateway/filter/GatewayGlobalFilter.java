package com.mira.gateway.filter;

import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.response.CommonResult;
import com.mira.core.util.JsonUtil;
import com.mira.gateway.properties.ApiBlackWhiteProperties;
import com.mira.gateway.properties.SwaggerProperties;
import com.mira.gateway.util.DingTalkTextMsgUtil;
import com.mira.gateway.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;

/**
 * 全局过滤器
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class GatewayGlobalFilter implements GlobalFilter, Ordered {
    @Resource
    private ApiBlackWhiteProperties apiBlackWhiteProperties;
    @Resource
    private SwaggerProperties swaggerProperties;
    @Resource
    private WebClient webClient;

    /**
     * 预编译的正则表达式缓存
     */
    private final ConcurrentHashMap<String, Pattern> patternCache = new ConcurrentHashMap<>();

    /**
     * 认证超时时间
     */
    private static final Duration AUTH_TIMEOUT = Duration.ofSeconds(10);

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        String path = request.getURI().getPath();

        String clientIp = IpUtil.getClientIp(request);
        long startTime = System.currentTimeMillis();

        // Api Doc
        if (!swaggerProperties.isProduction() && matchApiDoc(path)) {
            return chain.filter(exchange);
        }
        // 白名单
        if (matchWhite(path)) {
            return chain.filter(exchange);
        }
        // 黑名单
        if (matchBlack(path)) {
            return wrapMono(response, CommonResult.FAILED(HttpStatus.FORBIDDEN.value(), HttpStatus.FORBIDDEN.getReasonPhrase()));
        }

        // 获取令牌，获取用户类别
        AtomicReference<String> userTypeRef = new AtomicReference<>();
        String authorization = getAuthorization(request, userTypeRef);
        if (StringUtils.isEmpty(authorization)) {
            return wrapMono(response, CommonResult.FAILED(BizCodeEnum.TOKEN_INVALID.getCode(), BizCodeEnum.TOKEN_INVALID.getMsg()));
        }

        // 鉴权
        String iamEndpoint = "http://mira-iam/oauth/convertToken?oauthToken=" + authorization + "&userType=" + userTypeRef.get();
        return webClient
                .get()
                .uri(iamEndpoint)
                .retrieve()
                .bodyToMono(CommonResult.class)
                .timeout(AUTH_TIMEOUT)
                .onErrorResume(throwable -> {
                    // 处理WebClient调用异常
                    log.error("IAM service call failed for path: {}, error: {}", path, throwable.getMessage(), throwable);

                    // 根据异常类型返回不同的错误响应
                    if (throwable instanceof java.util.concurrent.TimeoutException) {
                        return Mono.just(CommonResult.FAILED(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), "认证服务超时"));
                    } else if (throwable instanceof reactor.netty.channel.AbortedException) {
                        return Mono.just(CommonResult.FAILED(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), "认证服务连接中断"));
                    } else if (throwable instanceof java.net.ConnectException) {
                        return Mono.just(CommonResult.FAILED(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), "认证服务不可用"));
                    } else {
                        return Mono.just(CommonResult.FAILED(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), "认证服务异常"));
                    }
                })
                .flatMap(iamResult -> {
                    if (Objects.equals(BizCodeEnum.TOKEN_INVALID.getCode(), iamResult.getCode())) {
                        return wrapMono(response, CommonResult.FAILED(BizCodeEnum.TOKEN_INVALID.getCode(), BizCodeEnum.TOKEN_INVALID.getMsg()));
                    }
                    if (Objects.equals(HttpStatus.FORBIDDEN.value(), iamResult.getCode())) {
                        return wrapMono(response, CommonResult.FAILED(HttpStatus.FORBIDDEN.value(), HttpStatus.FORBIDDEN.getReasonPhrase()));
                    }
                    if (Objects.equals(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), iamResult.getCode())) {
                        DingTalkTextMsgUtil.sendDingTalk(authorization, path, iamResult.getMsg(), true);
                        return wrapMono(response, CommonResult.FAILED(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), BizCodeEnum.INTERNAL_SERVER_ERROR.getMsg()));
                    }

                    // 安全地获取JWT token
                    String jwt = extractJwtToken(iamResult);
                    if (StringUtils.isEmpty(jwt)) {
                        log.error("Failed to extract JWT token from IAM response for path: {}", path);
                        return wrapMono(response, CommonResult.FAILED(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), "认证令牌获取失败"));
                    }

                    // 设置请求头
                    ServerHttpRequest mutateRequest = request.mutate()
                            .header(HeaderConst.AUTHORIZATION, jwt)
                            .header(HeaderConst.USER_TYPE, userTypeRef.get())
                            .build();
                    ServerWebExchange mutateExchange = exchange.mutate().request(mutateRequest).build();

                    return chain.filter(mutateExchange)
                            .doOnSuccess(aVoid -> logPrint(path, userTypeRef.get(), startTime, clientIp, "success", "request success"))
                            .doOnError(error -> {
                                log.error("Downstream service call failed for path: {}, error: {}", path, error.getMessage(), error);
                                logPrint(path, userTypeRef.get(), startTime, clientIp, "fail", error.getMessage());
                            })
                            .onErrorResume(error -> {
                                // 处理下游服务调用异常
                                log.error("Downstream service error for path: {}, error: {}", path, error.getMessage(), error);
                                return wrapMono(response, CommonResult.FAILED(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), "下游服务异常"));
                            });
                });
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE;
    }

    private boolean matchApiDoc(String path) {
        return path.contains("v3/api-docs");
    }

    private boolean matchWhite(String path) {
        try {
            String[] whiteList = apiBlackWhiteProperties.getApiWhite();
            if (whiteList == null) {
                return false;
            }

            for (String white : whiteList) {
                if (StringUtils.isEmpty(white)) {
                    continue;
                }

                if (path.equals(white)) {
                    return true;
                }

                if (white.endsWith("*")) {
                    Pattern pattern = getOrCreatePattern(white);
                    if (pattern != null && pattern.matcher(path).matches()) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Error matching white list for path: {}, error: {}", path, e.getMessage());
            return false;
        }
        return false;
    }

    private boolean matchBlack(String path) {
        try {
            String[] blackList = apiBlackWhiteProperties.getApiBlack();
            if (blackList == null) {
                return false;
            }

            for (String black : blackList) {
                if (StringUtils.isEmpty(black)) {
                    continue;
                }

                if (path.equals(black)) {
                    return true;
                }

                if (black.endsWith("*")) {
                    Pattern pattern = getOrCreatePattern(black);
                    if (pattern != null && pattern.matcher(path).matches()) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Error matching black list for path: {}, error: {}", path, e.getMessage());
            return false;
        }
        return false;
    }

    private Pattern getOrCreatePattern(String patternStr) {
        return patternCache.computeIfAbsent(patternStr, key -> {
            try {
                String regex = key.substring(0, key.length() - 1) + ".+";
                return Pattern.compile(regex);
            } catch (Exception e) {
                log.warn("Failed to compile pattern: {}, error: {}", key, e.getMessage());
                return null;
            }
        });
    }

    private String getAuthorization(ServerHttpRequest request, AtomicReference<String> userTypeRef) {
        for (UserTypeEnum userTypeEnum : UserTypeEnum.values()) {
            for (String headerName : userTypeEnum.getTokenHeaderList()) {
                String authorization = request.getHeaders().getFirst(headerName);
                if (StringUtils.isNotEmpty(authorization)) {
                    userTypeRef.set(userTypeEnum.getType());
                    return authorization;
                }
            }
        }
        return null;
    }

    private void logPrint(String path, String userType, long startTime, String clientIp, String status, String message) {
        log.info("path: {}, user type: {}, duration: {}ms, client ip: {}, status: {}, message: {}",
                path, userType, System.currentTimeMillis() - startTime, clientIp, status, message);
    }

    /**
     * 安全地从IAM响应中提取JWT token
     */
    private String extractJwtToken(CommonResult<?> iamResult) {
        try {
            if (iamResult.getData() instanceof Map) {
                Map<?, ?> dataMap = (Map<?, ?>) iamResult.getData();
                Object accessToken = dataMap.get("access_token");
                return accessToken != null ? accessToken.toString() : null;
            }
        } catch (Exception e) {
            log.error("Error extracting JWT token from IAM response", e);
        }
        return null;
    }

    /**
     * 包装响应，确保正确释放资源
     */
    private Mono<Void> wrapMono(ServerHttpResponse response, CommonResult<String> commonResult) {
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        try {
            String jsonResponse = JsonUtil.toJson(commonResult);
            DataBuffer dataBuffer = response.bufferFactory().wrap(jsonResponse.getBytes());

            return response.writeWith(Flux.just(dataBuffer))
                    .doOnError(error -> {
                        log.error("Error writing response", error);
                        // 确保DataBuffer被释放
                        if (dataBuffer.readableByteCount() > 0) {
                            try {
                                dataBuffer.release();
                            } catch (Exception e) {
                                log.warn("Error releasing DataBuffer", e);
                            }
                        }
                    })
                    .doFinally(signalType -> {
                        // 在完成时确保资源被释放
                        if (signalType != reactor.core.publisher.SignalType.ON_COMPLETE &&
                            dataBuffer.readableByteCount() > 0) {
                            try {
                                dataBuffer.release();
                            } catch (Exception e) {
                                log.warn("Error releasing DataBuffer in finally block", e);
                            }
                        }
                    });
        } catch (Exception e) {
            log.error("Error creating response", e);
            return response.setComplete();
        }
    }
}
