package com.mira.gateway.handler;

import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.response.CommonResult;
import com.mira.core.util.JsonUtil;
import com.mira.gateway.util.DingTalkTextMsgUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * Global Exception Handler
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GlobalExceptionHandler implements ErrorWebExceptionHandler {
    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        log.error("Exception: ", ex);

        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        DingTalkTextMsgUtil.sendDingTalk(path, ex.getMessage(), true);

        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        String body = JsonUtil.toJson(CommonResult.FAILED(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), BizCodeEnum.INTERNAL_SERVER_ERROR.getMsg()));
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }
}
