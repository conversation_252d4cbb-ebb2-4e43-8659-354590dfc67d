package com.mira.gateway.handler;

import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.response.CommonResult;
import com.mira.core.util.JsonUtil;
import com.mira.gateway.util.DingTalkTextMsgUtil;
import io.netty.channel.StacklessClosedChannelException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.netty.channel.AbortedException;

import java.net.ConnectException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeoutException;

/**
 * Global Exception Handler
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GlobalExceptionHandler implements ErrorWebExceptionHandler {

    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        String path = request.getURI().getPath();
        String clientIp = getClientIp(request);

        // 根据异常类型进行不同的处理
        if (ex instanceof AbortedException) {
            log.warn("Connection aborted for path: {}, client: {}, message: {}", path, clientIp, ex.getMessage());
            return Mono.empty();
        }

        if (ex instanceof StacklessClosedChannelException) {
            log.warn("Channel closed unexpectedly for path: {}, client: {}", path, clientIp);
            return Mono.empty();
        }

        if (ex instanceof ConnectException) {
            log.error("Connection failed for path: {}, client: {}, error: {}", path, clientIp, ex.getMessage());
            return writeErrorResponse(response, BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), "服务连接失败");
        }

        if (ex instanceof TimeoutException) {
            log.error("Request timeout for path: {}, client: {}, error: {}", path, clientIp, ex.getMessage());
            return writeErrorResponse(response, BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), "请求超时");
        }

        if (ex instanceof ResponseStatusException) {
            ResponseStatusException rse = (ResponseStatusException) ex;
            log.error("Response status exception for path: {}, client: {}, status: {}, error: {}",
                path, clientIp, rse.getStatus(), rse.getMessage());
            return writeErrorResponse(response, rse.getStatus().value(), rse.getReason());
        }

        // 其他未知异常
        log.error("Unhandled exception for path: {}, client: {}", path, clientIp, ex);

        // 发送钉钉通知（异步执行，避免阻塞）
        Mono.fromRunnable(() -> {
            try {
                DingTalkTextMsgUtil.sendDingTalk(path, ex.getMessage(), true);
            } catch (Exception e) {
                log.warn("Failed to send DingTalk notification", e);
            }
        }).subscribeOn(reactor.core.scheduler.Schedulers.boundedElastic()).subscribe();

        return writeErrorResponse(response, BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), BizCodeEnum.INTERNAL_SERVER_ERROR.getMsg());
    }

    /**
     * 写入错误响应
     */
    private Mono<Void> writeErrorResponse(ServerHttpResponse response, int code, String message) {
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        try {
            String body = JsonUtil.toJson(CommonResult.FAILED(code, message));
            DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));

            return response.writeWith(Flux.just(buffer))
                    .doOnError(error -> {
                        log.error("Error writing error response", error);
                        // 确保DataBuffer被释放
                        try {
                            if (buffer.readableByteCount() > 0) {
                                buffer.release();
                            }
                        } catch (Exception e) {
                            log.warn("Error releasing DataBuffer", e);
                        }
                    })
                    .doFinally(signalType -> {
                        // 在完成时确保资源被释放
                        if (signalType != reactor.core.publisher.SignalType.ON_COMPLETE &&
                            buffer.readableByteCount() > 0) {
                            try {
                                buffer.release();
                            } catch (Exception e) {
                                log.warn("Error releasing DataBuffer in finally block", e);
                            }
                        }
                    });
        } catch (Exception e) {
            log.error("Error creating error response", e);
            return response.setComplete();
        }
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddress() != null ?
            request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }
}
