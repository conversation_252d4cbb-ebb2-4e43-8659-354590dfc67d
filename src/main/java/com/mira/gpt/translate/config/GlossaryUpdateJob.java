package com.mira.gpt.translate.config;

import com.mira.gpt.translate.model.notion.NotionRequest;
import com.mira.gpt.translate.model.notion.filter.NotionFilter;
import com.mira.gpt.translate.model.notion.filter.Property;
import com.mira.gpt.translate.model.notion.filter.RichText;
import com.mira.gpt.translate.util.NotionUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * glossary config
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GlossaryUpdateJob {
    @Resource
    private NotionUtil notionUtil;

    private final static ConcurrentHashMap<String, String> GLOSSARY = new ConcurrentHashMap<>();
    private final static String DATABASE_QUERY_PATH = "/query?filter_properties=%5B%3AmD&filter_properties=title";

    @Value("${notion.database-id}")
    private String databaseId;

    private final Lock lock = new ReentrantLock();

    public String get(String key) {
        return GLOSSARY.get(key);
    }

    @PostConstruct
    public void init() {
        update();
    }

    @Scheduled(cron = "0 */10 * * * ?")
    public void update() {
        // get data for notion
        String path = "/databases/" + databaseId + DATABASE_QUERY_PATH;
        NotionRequest notionRequest = new NotionRequest();
        notionRequest.setFilter(getNotionFilter());
        Map<String, String> result = notionUtil.pullData(path, notionRequest);

        lock.lock();
        try {
            GLOSSARY.clear();
            for (Map.Entry<String, String> entry : result.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                GLOSSARY.put(key, value);
            }
        } finally {
            lock.unlock();
        }
    }

    public String replaceGlossary(String text) {
        lock.lock();
        try {
            for (Map.Entry<String, String> entry : GLOSSARY.entrySet()) {
                text = text.replace(entry.getKey(), " ".concat(entry.getValue()).concat(" "));
            }
            return text;
        } finally {
            lock.unlock();
        }
    }

    @NotNull
    private static NotionFilter getNotionFilter() {
        RichText richText = new RichText();
        richText.set_not_empty(true);

        Property chineseProperty = new Property();
        chineseProperty.setProperty("Term (CN) 中文");
        chineseProperty.setRich_text(richText);
        Property englishProperty = new Property();
        englishProperty.setProperty("Term");
        englishProperty.setRich_text(richText);
        List<Property> and = List.of(chineseProperty, englishProperty);

        NotionFilter filter = new NotionFilter();
        filter.setAnd(and);
        return filter;
    }
}
