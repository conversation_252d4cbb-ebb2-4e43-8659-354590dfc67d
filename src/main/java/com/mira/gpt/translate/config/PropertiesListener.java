//package com.mira.gpt.translate.config;
//
//import com.alibaba.cloud.nacos.NacosConfigManager;
//import com.alibaba.nacos.api.config.listener.Listener;
//import com.alibaba.nacos.api.exception.NacosException;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.concurrent.Executor;
//
///**
// * properties listener
// *
// * <AUTHOR>
// */
//@Component
//public class PropertiesListener {
//    @Resource
//    private NacosConfigManager nacosConfigManager;
//    @Resource
//    private GlossaryConfig glossaryConfig;
//
//    @Value("${spring.application.name}")
//    private String dataId;
//    @Value("${spring.cloud.nacos.config.group}")
//    private String group;
//
//    @PostConstruct
//    public void init() throws NacosException {
//        nacosConfigManager.getConfigService().addListener(dataId, group, new Listener() {
//            @Override
//            public Executor getExecutor() {
//                return null;
//            }
//
//            @Override
//            public void receiveConfigInfo(String configInfo) {
//                glossaryConfig.update(configInfo);
//            }
//        });
//    }
//}
