package com.mira.gpt.translate.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * exception handler
 *
 * <AUTHOR>
 */
@Slf4j
@ResponseBody
@RestControllerAdvice
public class TranslateExceptionHandler {
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(Exception.class)
    public String exception(Exception ex) {
        log.error(ex.getMessage(), ex);
        return ex.getMessage();
    }
}
