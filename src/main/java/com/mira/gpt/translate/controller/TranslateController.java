package com.mira.gpt.translate.controller;

import com.mira.gpt.translate.service.IExcelTranslateService;
import com.mira.gpt.translate.service.IFileService;
import com.mira.gpt.translate.service.ITextTranslateService;
import com.mira.gpt.translate.service.IWorldTranslateService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * translate controller
 *
 * <AUTHOR>
 */
@RestController
public class TranslateController {
    @Resource
    private IWorldTranslateService worldTranslateService;
    @Resource
    private IExcelTranslateService excelTranslateService;
    @Resource
    private ITextTranslateService textTranslateService;
    @Resource
    private IFileService fileService;

    @PostMapping("/upload/file")
    public String uploadFile(@RequestParam("file") MultipartFile file,
                             @RequestParam("tOption") Integer tOption,
                             @RequestParam("option") Integer option) throws Exception {
        // file name
        String fileName = file.getOriginalFilename();
        String prefix = fileName;
        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex >= 0) {
            prefix = fileName.substring(0, dotIndex);
        }
        String suffix = fileName.substring(dotIndex + 1);
        boolean isWord = "doc".equals(suffix) || "docx".equals(suffix);
        boolean isExcel = "xlsx".equals(suffix) || "xls".equals(suffix);
        // check
        if (!isWord && !isExcel) {
            throw new RuntimeException("Only doc,docx,xlsx,xls format files are supported.");
        }

        if (isWord) {
            return worldTranslateService.translateWord(file, prefix, tOption, option);
        }
        return excelTranslateService.translateExcel(file, prefix, suffix, tOption, option);
    }

    @GetMapping("/download/file")
    public void downloadFile(@RequestParam("path") String path,
                             HttpServletRequest request,
                             HttpServletResponse response) {
        fileService.downloadFile(path, request, response);
    }

    @PostMapping("/upload/text")
    public String uploadText(@RequestParam("text") String text,
                             @RequestParam("tOption") Integer tOption,
                             @RequestParam("option") Integer option) {
        return textTranslateService.translateText(text, tOption, option);
    }
}
