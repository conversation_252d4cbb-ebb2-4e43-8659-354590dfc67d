package com.mira.gpt.translate.model.gpt;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * gpt request
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ChatRequest {

    private String model;
    private List<Message> messages;
    private Double temperature = 0.6D;
    private Double top_p = 1.0D;

    public ChatRequest() {
    }

    public ChatRequest(String model, String system, String prompt) {
        this.model = model;

        this.messages = new ArrayList<>();
        this.messages.add(new Message("developer", system));
        this.messages.add(new Message("user", prompt));
    }
}
