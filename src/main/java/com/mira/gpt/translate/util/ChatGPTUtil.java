package com.mira.gpt.translate.util;

import com.mira.gpt.translate.model.gpt.ChatRequest;
import com.mira.gpt.translate.model.gpt.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * chat gpt util
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ChatGPTUtil {
    @Value("${openai.model}")
    private String model;
    @Value("${openai.api.url}")
    private String apiUrl;
    @Value("${openai.api.key}")
    private String openaiApiKey;

    public String chat(String prompt, String system) {
        // create a request
        ChatRequest request = new ChatRequest(model, system, prompt);

        // call the API
        try {
            String result = HttpUtil.postGPT(openaiApiKey, apiUrl, JsonUtil.toJson(request));
            ChatResponse chatResponse = JsonUtil.toObject(result, ChatResponse.class);

            if (chatResponse == null || chatResponse.getChoices() == null || chatResponse.getChoices().isEmpty()) {
                return "No response";
            }

            // return the first response
            return chatResponse.getChoices().get(0).getMessage().getContent();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("Unknown error");
        }
    }
}
