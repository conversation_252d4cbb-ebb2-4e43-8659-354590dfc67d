package com.mira.gpt.translate.util;

import com.mira.gpt.translate.consts.OptionConst;
import com.mira.gpt.translate.consts.TOptionConst;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.Map;

/**
 * additional util
 *
 * <AUTHOR>
 */
public class DocxAdditionalUtil {
    private static boolean matchCheckboxText(XWPFParagraph paragraph) {
        // 复选框，比如"是 否"、"合格、不合格"
        String trimText = paragraph.getText().replace(" ", "");
        boolean matchLength = StringUtil.countChineseCharacters(trimText) < 10;
        boolean matchText = (trimText.equals("是否") || trimText.equals("否是")
                || (trimText.equals("合格不合格") || trimText.equals("不合格合格"))
                || (trimText.contains("是") && trimText.contains("否"))
                || (trimText.contains("合格") && trimText.contains("不合格")));

        return matchLength && matchText;
    }

    public static boolean additionalProcessToGet(XWPFParagraph paragraph, Map<String, String> waitTranslate,
                                                 Integer tOption) {
        if (matchCheckboxText(paragraph)) {
            for (XWPFRun run : paragraph.getRuns()) {
                String runText = run.text().trim();
                boolean b1 = (TOptionConst.FILE_ZH_TO_EN == tOption && StringUtil.isContainChinese(runText));
                boolean b2 = (TOptionConst.FILE_EN_TO_ZH == tOption && StringUtil.isContainEnglish(runText));
                if (b1 || b2) {
                    String key = String.valueOf(run.hashCode());
                    waitTranslate.put(key, runText);
                }
            }
            return true;
        }

        return false;
    }

    public static boolean additionalProcessToSet(XWPFParagraph paragraph, Map<String, String> resultMap,
                                                 Integer option, Integer tOption) {
        if (matchCheckboxText(paragraph)) {
            for (XWPFRun run : paragraph.getRuns()) {
                String runText = run.text().trim();
                boolean b1 = (TOptionConst.FILE_ZH_TO_EN == tOption && StringUtil.isContainChinese(runText));
                boolean b2 = (TOptionConst.FILE_EN_TO_ZH == tOption && StringUtil.isContainEnglish(runText));
                if (b1 || b2) {
                    String key = String.valueOf(run.hashCode());
                    String enText = resultMap.get(key);
                    if (option == OptionConst.ZH_AND_EN) {
                        enText = enText + " " + runText;
                    }
                    run.setText(enText, 0);
                }
            }
            return true;
        }

        return false;
    }
}
