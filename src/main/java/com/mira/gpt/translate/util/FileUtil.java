package com.mira.gpt.translate.util;

import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * file util
 *
 * <AUTHOR>
 */
public class FileUtil {
    public static void saveSourceFile(MultipartFile file, String rootPathSource) throws Exception {
        String folder = DateUtil.formatMonth(System.currentTimeMillis());
        String pathPrefix = rootPathSource + folder + "/";
        Path directoryPath = Paths.get(pathPrefix);
        Files.createDirectories(directoryPath);
        Files.write(Paths.get(pathPrefix + file.getOriginalFilename()), file.getBytes());
    }
}
