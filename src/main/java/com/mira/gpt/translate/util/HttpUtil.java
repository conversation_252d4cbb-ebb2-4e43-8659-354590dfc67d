package com.mira.gpt.translate.util;

import okhttp3.*;

import java.io.IOException;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class HttpUtil {
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final OkHttpClient CLIENT;

    static {
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(500);
        dispatcher.setMaxRequestsPerHost(500);
        CLIENT = new OkHttpClient.Builder()
                .dispatcher(dispatcher)
                .protocols(Collections.singletonList(Protocol.HTTP_1_1))
                .connectTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(120, TimeUnit.SECONDS)
                .readTimeout(180, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 发送GET请求
     *
     * @param url 请求的URL
     * @return 响应内容
     */
    public static String get(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .build();

        try (Response response = CLIENT.newCall(request).execute()) {
            return response.body().string();
        }
    }

    /**
     * 发送POST请求，gpt
     *
     * @param authorization Authorization
     * @param url           请求的URL
     * @param json          请求体，以JSON格式传递
     * @return 响应内容
     */
    public static String postGPT(String authorization, String url, String json) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", "Bearer " + authorization)
                .post(body)
                .build();

        try (Response response = CLIENT.newCall(request).execute()) {
            return response.body().string();
        }
    }

    /**
     * 发送POST请求，notion
     *
     * @param authorization Authorization
     * @param notionVersion Notion-Version
     * @param url           请求的URL
     * @param json          请求体，以JSON格式传递
     * @return 响应内容
     */
    public static String postNotion(String authorization, String notionVersion, String url, String json) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .url(url)
                .header("Notion-Version", notionVersion)
                .header("Authorization", "Bearer " + authorization)
                .post(body)
                .build();

        try (Response response = CLIENT.newCall(request).execute()) {
            return response.body().string();
        }
    }
}
