package com.mira.gpt.translate.util;

import com.mira.gpt.translate.model.notion.NotionRequest;
import com.mira.gpt.translate.model.notion.NotionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * notion util
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Slf4j
@Component
public class NotionUtil {
    @Value("${notion.api.version}")
    private String apiVersion;
    @Value("${notion.api.url}")
    private String apiUrl;
    @Value("${notion.api.key}")
    private String apiKey;

    private final static String CH_KEY = "Term (CN) 中文";
    private final static String EN_KEY = "Term";

    public Map<String, String> pullData(String path, NotionRequest notionRequest) {
        // call the API
        try {
            // 取出中文和对应的翻译
            Map<String, String> glossaryMap = new HashMap<>();
            for (;;) {
                String result = HttpUtil.postNotion(apiKey, apiVersion, apiUrl + path, JsonUtil.toJson(notionRequest));
                NotionResponse notionResponse = JsonUtil.toObject(result, NotionResponse.class);
                List<Map<String, Object>> results = notionResponse.getResults();
                for (Map<String, Object> map : results) {
                    Map<String, Object> properties = (Map<String, Object>) map.get("properties");
                    Map<String, Object> chMap = (Map<String, Object>) properties.get(CH_KEY);
                    List<Map<String, Object>> richTextList = (List<Map<String, Object>>) chMap.get("rich_text");
                    Map<String, Object> richText = richTextList.get(0);
                    String ch = (String) richText.get("plain_text");

                    Map<String, Object> enMap = (Map<String, Object>) properties.get(EN_KEY);
                    List<Map<String, Object>> titleList = (List<Map<String, Object>>) enMap.get("title");
                    Map<String, Object> title = titleList.get(0);
                    String en = (String) title.get("plain_text");

                    glossaryMap.put(ch, en);
                }
                if (notionResponse.getHas_more()) {
                    String nextCursor = notionResponse.getNext_cursor();
                    notionRequest.setStart_cursor(nextCursor);
                } else {
                    break;
                }
            }
            return glossaryMap;

        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("Unknown error");
        }
    }
}
