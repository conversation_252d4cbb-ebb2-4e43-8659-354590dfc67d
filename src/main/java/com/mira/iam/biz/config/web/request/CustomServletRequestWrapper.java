package com.mira.iam.biz.config.web.request;

import org.apache.commons.io.IOUtils;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * request包装类
 *
 * <AUTHOR>
 */
public class CustomServletRequestWrapper extends HttpServletRequestWrapper {
    private Map<String, String[]> parameterMap = new HashMap<>();
    private final byte[] body;

    public CustomServletRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        this.parameterMap.putAll(request.getParameterMap());
        this.body = IOUtils.toByteArray(request.getInputStream());
    }

    @Override
    public String getParameter(String name) {
        String[] values = this.parameterMap.get(name);
        if(values == null || values.length == 0) {
            return null;
        }
        return values[0];
    }

    @Override
    public String[] getParameterValues(String name) {
        return this.getParameterMap().get(name);
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        return this.parameterMap;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        return new CustomServletInputStream(body);
    }

    public void setParameterMap(Map<String, String[]> parameterMap) {
        this.parameterMap = parameterMap;
    }
}
