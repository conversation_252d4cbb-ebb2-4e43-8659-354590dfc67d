package com.mira.iam.biz.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("app_tenant_doctor")
public class AppTenantDoctorEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户code
     */
    private String tenantCode;

    /**
     * 管理员名称
     */
    private String name;

    /**
     * 管理员email
     */
    private String email;

    /**
     * 管理员mobile
     */
    private String mobile;

    /**
     * 管理员状态:1:邀请中；2:正常激活状态
     */
    private Integer status;

    /**
     * 管理员角色编码:1:clinic管理员;2:医生;3:护士
     */
    private Integer role;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐
     */
    private String salt;
}
