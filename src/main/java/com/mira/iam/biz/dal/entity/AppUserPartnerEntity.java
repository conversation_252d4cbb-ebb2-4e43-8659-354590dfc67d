package com.mira.iam.biz.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * partner 用户信息表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_partner")
public class AppUserPartnerEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 绑定的用户ID
     */
    private Long userId;

    /**
     * 绑定的用户邮箱
     */
    private String userEmail;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 英文名
     */
    private String firstName;

    /**
     * 英文姓
     */
    private String lastName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐
     */
    private String salt;

    /**
     * 用户当前ip地址
     */
    private String currentIp;

    /**
     * 状态  0-未激活，1-已完成激活但未注册，2-已完成注册
     */
    private Integer status;
}
