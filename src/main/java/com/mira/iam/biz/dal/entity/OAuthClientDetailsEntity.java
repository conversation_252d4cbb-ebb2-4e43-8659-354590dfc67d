package com.mira.iam.biz.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * OAuth 客户端信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("oauth_client_details")
public class OAuthClientDetailsEntity {
    /**
     * 客户端 id
     */
    @TableId(type = IdType.INPUT)
    private String clientId;

    /**
     * 客户端密钥
     */
    private String clientSecret;

    /**
     * 资源保护 id 列表
     */
    private String resourceIds;

    /**
     * 授权范围
     */
    private String scope;

    /**
     * 授权认证类型
     */
    private String authorizedGrantTypes;

    /**
     * 授权回调地址
     */
    private String webServerRedirectUri;

    /**
     * 权限列表
     */
    private String authorities;

    /**
     * token有效期
     */
    private Integer accessTokenValidity;

    /**
     * 刷新token有效期
     */
    private Integer refreshTokenValidity;

    /**
     * 自定义信息
     */
    private String additionalInformation;

    /**
     * 自动授权
     */
    private String autoapprove;

    /**
     * 状态 0 正常状态 1 删除状态
     */
    @TableLogic
    private Integer deleted;
}
