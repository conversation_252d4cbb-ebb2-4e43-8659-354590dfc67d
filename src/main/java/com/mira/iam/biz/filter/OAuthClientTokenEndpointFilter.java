package com.mira.iam.biz.filter;

import com.mira.iam.biz.config.web.request.CustomServletRequestWrapper;
import com.mira.iam.biz.enums.GrantTypeEnum;
import com.mira.iam.biz.properties.GrantProperties;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 客户端认证过滤器
 * <p>这里使用自定义的认证过滤器，使用我们自定义的数据返回格式
 *
 * <AUTHOR>
 */
public class OAuthClientTokenEndpointFilter extends ClientCredentialsTokenEndpointFilter {
    private final AuthorizationServerSecurityConfigurer configurer;

    private AuthenticationEntryPoint authenticationEntryPoint;

    private final GrantProperties grantProperties;

    public OAuthClientTokenEndpointFilter(AuthorizationServerSecurityConfigurer configurer, GrantProperties grantProperties) {
        this.configurer = configurer;
        this.grantProperties = grantProperties;
    }

    @Override
    public void setAuthenticationEntryPoint(AuthenticationEntryPoint authenticationEntryPoint) {
        this.authenticationEntryPoint = authenticationEntryPoint;
    }

    @Override
    protected AuthenticationManager getAuthenticationManager() {
        return configurer.and().getSharedObject(AuthenticationManager.class);
    }

    @Override
    public void afterPropertiesSet() {
        setAuthenticationFailureHandler(authenticationEntryPoint::commence);
        setAuthenticationSuccessHandler((request, response, authentication) -> {});
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;

        // 识别是否来自外部服务
        if (StringUtils.isNotEmpty(httpRequest.getHeader("SDK"))) {
            super.doFilter(request, response, chain);
            return;
        }

        // 内部服务，使用自定义用户名密码模式
        CustomServletRequestWrapper customServletRequestWrapper = new CustomServletRequestWrapper(httpRequest);
        Map<String, String[]> parameterMap = new HashMap<>(customServletRequestWrapper.getParameterMap());
        parameterMap.put("grant_type", new String[]{GrantTypeEnum.USERNAME_PASSWORD.getGrantType()});
        parameterMap.put("client_id", new String[]{grantProperties.getClientId()});
        parameterMap.put("client_secret", new String[]{grantProperties.getClientSecret()});
        customServletRequestWrapper.setParameterMap(parameterMap);

        super.doFilter(customServletRequestWrapper, response, chain);
    }
}
