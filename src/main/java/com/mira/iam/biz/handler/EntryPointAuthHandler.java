package com.mira.iam.biz.handler;

import com.mira.core.response.CommonResult;
import com.mira.iam.biz.service.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 未登录或令牌失效处理器
 *
 * <AUTHOR>
 */
@Slf4j
public class EntryPointAuthHandler implements AuthenticationEntryPoint {
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) {
        log.error("AuthenticationException:{}", authException.getMessage());
        ResponseUtil.responseWrite(response,
                CommonResult.FAILED(HttpStatus.UNAUTHORIZED.value(), HttpStatus.UNAUTHORIZED.getReasonPhrase()));
    }
}
