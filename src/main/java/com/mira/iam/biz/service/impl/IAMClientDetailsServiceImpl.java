package com.mira.iam.biz.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.mira.core.util.JsonUtil;
import com.mira.iam.biz.dal.dao.OAuthClientDetailsDAO;
import com.mira.iam.biz.dal.entity.OAuthClientDetailsEntity;
import com.mira.iam.biz.exception.IAMException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.security.oauth2.provider.client.BaseClientDetails;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * 客户端信息获取服务
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Service("iamClientDetailsService")
public class IAMClientDetailsServiceImpl implements ClientDetailsService {
    @Resource
    private OAuthClientDetailsDAO oAuthClientDetailsDAO;

    @Override
    public ClientDetails loadClientByClientId(String clientId) throws ClientRegistrationException {
        OAuthClientDetailsEntity oAuthClientDetailsEntity = oAuthClientDetailsDAO.getOne(Wrappers.<OAuthClientDetailsEntity>lambdaQuery()
                .eq(OAuthClientDetailsEntity::getClientId, clientId).last("limit 1"));
        Optional.ofNullable(oAuthClientDetailsEntity).<IAMException>orElseThrow(() -> {
            throw new IAMException(HttpStatus.FORBIDDEN.value(), "Authorization failure, Account does not have permissions to perform this action");
        });

        return bulidClientDetails(oAuthClientDetailsEntity);
    }

    /**
     * 构建 ClientDetails 接口实现类
     *
     * @param oAuthClientDetailsEntity 客户端信息
     * @return BaseClientDetails
     */
    private BaseClientDetails bulidClientDetails(OAuthClientDetailsEntity oAuthClientDetailsEntity) {
        BaseClientDetails baseClientDetails = new BaseClientDetails(
                oAuthClientDetailsEntity.getClientId(), oAuthClientDetailsEntity.getResourceIds(),
                oAuthClientDetailsEntity.getScope(), oAuthClientDetailsEntity.getAuthorizedGrantTypes(),
                oAuthClientDetailsEntity.getAuthorities(), oAuthClientDetailsEntity.getWebServerRedirectUri());

        baseClientDetails.setClientSecret(oAuthClientDetailsEntity.getClientSecret());
        baseClientDetails.setAccessTokenValiditySeconds(oAuthClientDetailsEntity.getAccessTokenValidity());
        baseClientDetails.setRefreshTokenValiditySeconds(oAuthClientDetailsEntity.getRefreshTokenValidity());

        String scope = oAuthClientDetailsEntity.getScope();
        if (StringUtils.isNotBlank(scope)) {
            baseClientDetails.setScope(Lists.newArrayList(oAuthClientDetailsEntity.getScope().split(",")));
        }

        String additionalInformation = oAuthClientDetailsEntity.getAdditionalInformation();
        if (StringUtils.isNotBlank(additionalInformation)) {
            Map<String, Object> additionalInformationMap = JsonUtil.toObject(additionalInformation, Map.class);
            baseClientDetails.setAdditionalInformation(additionalInformationMap);
        }

        String autoApproveScopes = oAuthClientDetailsEntity.getAutoapprove();
        if (StringUtils.isNotBlank(autoApproveScopes)) {
            baseClientDetails.setAutoApproveScopes(Lists.newArrayList(autoApproveScopes.split(",")));
        }

        return baseClientDetails;
    }
}
