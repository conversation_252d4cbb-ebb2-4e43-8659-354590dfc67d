package com.mira.openapi.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * redis 序列化工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisComponent {
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        // key 序列化
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringRedisSerializer);
        redisTemplate.setHashKeySerializer(stringRedisSerializer);

        this.redisTemplate = redisTemplate;
    }

    /**
     * 检查key是否存在
     */
    public Boolean exists(String key) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * 删除key
     */
    public Boolean delete(String key) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return redisTemplate.delete(key);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * 删除多个key
     */
    public Long delete(String... keys) {
        try {
            return redisTemplate.delete(Arrays.stream(keys).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * string
     * 设置缓存
     */
    public <V> void set(String key, V val) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        try {
            redisTemplate.opsForValue().set(key, val);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * string
     * 设置缓存，过期时间
     */
    public <V> void setEx(String key, V val, long timeout, TimeUnit unit) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        try {
            redisTemplate.opsForValue().set(key, val, timeout, unit);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * string
     * 设置缓存，过期时间
     */
    public <V> Boolean setIfAbsent(String key, V val, long timeout, TimeUnit unit) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return redisTemplate.opsForValue().setIfAbsent(key, val, timeout, unit);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * 设置过期时间
     */
    public Boolean expire(String key, long timeout, TimeUnit unit) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return redisTemplate.expire(key, timeout, unit);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * string
     * 获取剩余过期时间，秒
     */
    public Long ttl(String key) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return redisTemplate.getExpire(key);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * string
     * 获取缓存，字符串
     */
    public String get(String key) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return (String) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * string
     * 获取缓存，实体类
     */
    public <V> V get(String key, Class<V> clazz) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return clazz.cast(redisTemplate.opsForValue().get(key));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * string
     * valud 自增1
     */
    public Long incr(String key) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return redisTemplate.opsForValue().increment(key, 1);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * hash
     * 设置缓存
     */
    public <F, V> void hSet(String key, F field, V val) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        try {
            redisTemplate.opsForHash().put(key, field, val);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * hash
     * 获取缓存
     */
    public <F, V> V hGet(String key, F field, Class<V> clazz) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return clazz.cast(redisTemplate.opsForHash().get(key, field));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * hash
     * 获取缓存
     */
    public <F, V> V hGet(String key, Class<V> clazz) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return clazz.cast(redisTemplate.opsForHash().entries(key));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * hash
     * 检查field是否存在
     */
    public <F> Boolean hExists(String key, F field) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return redisTemplate.opsForHash().hasKey(key, field);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * hash
     * 删除field
     */
    public <F> Long hDelete(String key, F field) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return redisTemplate.opsForHash().delete(key, field);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * set
     * 添加元素
     */
    public <V> void sAdd(String key, V value) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        try {
            redisTemplate.opsForSet().add(key, value);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * set
     * 批量添加元素
     *
     * @param key
     * @param values
     * @param <V>
     */
    public <V> void sAdds(String key, List<V> values) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        try {
            for (V value : values) {
                redisTemplate.opsForSet().add(key, value);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * set
     * 元素是否存在
     */
    public <V> Boolean sIsmember(String key, V value) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * set
     * 移除并返回集合中的一个随机元素
     */
    public <V> V sPop(String key, Class<V> clazz) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return clazz.cast(redisTemplate.opsForSet().pop(key));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * set
     * 移除集合中一个或多个成员
     */
    public <V> Long sRem(String key, V... values) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return redisTemplate.opsForSet().remove(key, values);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * set
     * 获取集合所有元素
     */
    public <V> Set<V> sMembers(String key, Class<V> clazz) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return redisTemplate.opsForSet().members(key).stream().map(v -> clazz.cast(v)).collect(Collectors.toSet());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * list
     * 将一个或多个值插入到列表头部
     */
    public <V> void lPush(String key, V value) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        try {
            redisTemplate.opsForList().leftPush(key, value);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * list
     * 将一个或多个值插入到列表尾部
     */
    public <V> void rPush(String key, V value) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        try {
            redisTemplate.opsForList().rightPush(key, value);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * list
     * 移出并获取列表的第一个元素
     */
    public <V> V lPop(String key, Class<V> clazz) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return clazz.cast(redisTemplate.opsForList().leftPop(key));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * list
     * 移出并获取列表的最后一个元素
     */
    public <V> V rPop(String key, Class<V> clazz) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return clazz.cast(redisTemplate.opsForList().rightPop(key));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * list
     * 获取列表所有元素
     */
    public <V> List<V> lRange(String key, Class<V> clazz) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return redisTemplate.opsForList().range(key, 0, -1).stream().map(v -> clazz.cast(v)).collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * get bit
     */
    public Boolean getBit(String key, long value) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return redisTemplate.opsForValue().getBit(key, value);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * set bit
     */
    public Boolean setBit(String key, long value) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return redisTemplate.opsForValue().setBit(key, value, true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * 执行脚本
     */
    public <V> V eval(String luaScript, List<String> keys, Class<V> resultClass, Object... args) {
        DefaultRedisScript<V> redisScript = new DefaultRedisScript<>(luaScript, resultClass);
        try {
            return redisTemplate.execute(redisScript, keys, args);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 遍历从redis中模糊查找,最多返回n条数据
     *
     * @param key       Set 的键
     * @param pattern   匹配模式
     * @param batchSize 每次批量获取的数量
     * @return List<String>
     */
    public List<String> scan(String key, String pattern, int batchSize) {
        List<String> results = new ArrayList<>();

        ScanOptions options = ScanOptions.scanOptions()
                .match(pattern)
                .count(batchSize)
                .build();

        try {
            Cursor<Object> resultScan = redisTemplate.opsForSet().scan(key, options);
            while (resultScan.hasNext()) {
                String member = (String) resultScan.next();
                if (results.size() < batchSize) {
                    results.add(member);
                }
            }
            resultScan.close();
            return results;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
