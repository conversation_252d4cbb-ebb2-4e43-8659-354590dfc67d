package com.mira.openapi.config;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * redis 工具类，纯 string 操作
 * <p>一般用作手动操作的redis key</p>
 *
 * <AUTHOR>
 */
@Component
public class StringRedisComponent {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 检查key是否存在
     */
    public Boolean exists(String key) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        return stringRedisTemplate.hasKey(key);
    }

    /**
     * 删除key
     */
    public Boolean delete(String key) {
        return stringRedisTemplate.delete(key);
    }

    /**
     * 删除多个key
     */
    public Long delete(String... keys) {
        return stringRedisTemplate.delete(Arrays.stream(keys).collect(Collectors.toList()));
    }

    /**
     * string
     * 设置缓存
     */
    public void set(String key, String val) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        stringRedisTemplate.opsForValue().set(key, val);
    }

    /**
     * string
     * 设置缓存，过期时间
     */
    public void setEx(String key, String val, long timeout, TimeUnit unit) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        stringRedisTemplate.opsForValue().set(key, val, timeout, unit);
    }

    /**
     * string
     * 获取剩余过期时间，秒
     */
    public Long ttl(String key) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        return stringRedisTemplate.getExpire(key);
    }

    /**
     * string
     * 获取缓存，字符串
     */
    public String get(String key) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        return stringRedisTemplate.opsForValue().get(key);
    }

    /**
     * string
     * valud 自增1
     */
    public Long incr(String key) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        return stringRedisTemplate.opsForValue().increment(key, 1);
    }

    /**
     * hash
     * 设置缓存
     */
    public void hSet(String key, String field, String val) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        stringRedisTemplate.opsForHash().put(key, field, val);
    }

    /**
     * hash
     * 获取缓存
     */
    public String hGet(String key, String field) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        return (String) stringRedisTemplate.opsForHash().get(key, field);
    }

    /**
     * hash
     * 检查field是否存在
     */
    public Boolean hExists(String key, String field) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        return stringRedisTemplate.opsForHash().hasKey(key, field);
    }

    /**
     * hash
     * 删除field
     */
    public Long hDelete(String key, String field) {
        return stringRedisTemplate.opsForHash().delete(key, field);
    }

    /**
     * set
     * 添加元素
     */
    public void sAdd(String key, String value) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        stringRedisTemplate.opsForSet().add(key, value);
    }

    /**
     * set
     * 元素是否存在
     */
    public Boolean sIsmember(String key, String value) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        return stringRedisTemplate.opsForSet().isMember(key, value);
    }

    /**
     * 执行脚本
     */
    public <V> V eval(String luaScript, List<String> keys, Class<V> resultClass, Object... args) {
        DefaultRedisScript<V> redisScript = new DefaultRedisScript<>(luaScript, resultClass);
        return stringRedisTemplate.execute(redisScript, keys, args);
    }
}
