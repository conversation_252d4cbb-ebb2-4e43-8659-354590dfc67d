package com.mira.openapi.config.oauth2;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.web.firewall.RequestRejectedException;
import org.springframework.security.web.firewall.RequestRejectedHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * custom request reject handler
 *
 * <AUTHOR>
 */
@Slf4j
public class CustomRequestRejectHandler implements RequestRejectedHandler {
    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                       RequestRejectedException requestRejectedException) throws IOException, ServletException {
        log.error("request rejected, url:{}", request.getRequestURI(), requestRejectedException);
        response.sendError(HttpServletResponse.SC_BAD_REQUEST);
    }
}
