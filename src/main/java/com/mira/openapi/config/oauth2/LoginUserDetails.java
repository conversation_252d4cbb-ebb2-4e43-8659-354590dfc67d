package com.mira.openapi.config.oauth2;

import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.User;

/**
 * OAuth User 扩展类
 *
 * <AUTHOR>
 */
public class LoginUserDetails extends User {
    /**
     * id
     */
    private final Long id;

    /**
     * 密码盐
     */
    private final String salt;

    public LoginUserDetails(Long id, String username, String password, String salt) {
        super(username, password, AuthorityUtils.createAuthorityList("*:*:*"));
        this.id = id;
        this.salt = salt;
    }

    public String getSalt() {
        return this.salt;
    }

    public Long getId() {
        return this.id;
    }
}
