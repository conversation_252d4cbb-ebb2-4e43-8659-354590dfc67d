package com.mira.openapi.config.oauth2;

import com.google.common.collect.Lists;
import com.mira.openapi.filter.OAuthClientTokenEndpointFilter;
import com.mira.openapi.handler.oauth2.EntryPointAuthHandler;
import com.mira.openapi.handler.oauth2.OAuthWebRespExceptionTranslator;
import com.mira.openapi.interceptor.ClearInterceptor;
import com.mira.openapi.properties.OAuth2Properties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.web.firewall.RequestRejectedHandler;

import javax.annotation.Resource;

/**
 * 授权服务配置
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Configuration
@EnableAuthorizationServer
public class OAuth2Configuration extends AuthorizationServerConfigurerAdapter {
    /**
     * redis 连接
     */
    @Resource
    private RedisConnectionFactory redisConnectionFactory;

    /**
     * authentication configuration
     */
    @Resource
    private AuthenticationConfiguration authenticationConfiguration;

    /**
     * 用户信息服务
     */
    @Resource(name = "openUserDetailsService")
    private UserDetailsService userDetailsService;

    /**
     * 客户端信息服务
     */
    @Resource(name = "openClientDetailsService")
    private ClientDetailsService clientDetailsService;

    /**
     * 授权码获取服务
     */
    @Resource(name = "openAuthorizationCodeService")
    private AuthorizationCodeServices authorizationCodeServices;

    @Override
    public void configure(AuthorizationServerSecurityConfigurer security) throws Exception {
        // 客户端认证过滤器
        OAuthClientTokenEndpointFilter clientTokenEndpointFilter = new OAuthClientTokenEndpointFilter(security);
        clientTokenEndpointFilter.setAuthenticationEntryPoint(entryPointAuthHandler());
        clientTokenEndpointFilter.afterPropertiesSet();
        security.addTokenEndpointAuthenticationFilter(clientTokenEndpointFilter);
        security.allowFormAuthenticationForClients()
                // 访问公钥端点（/auth/token_key）需要认证
                .tokenKeyAccess("isAuthenticated()")
                // 访问令牌解析端点（/auth/check_token）需要认证
                .checkTokenAccess("isAuthenticated()");
    }

    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        // 配置客户端信息服务
        clients.withClientDetails(clientDetailsService);
    }

    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) throws Exception {
        endpoints // 自定义 token path
                .pathMapping("/oauth/token", oAuth2Properties().getTokenUrl())
                // 自定义授权码 path
                .pathMapping("/oauth/authorize", oAuth2Properties().getAuthorizeUrl())
                // 用户信息服务
                .userDetailsService(userDetailsService)
                // 授权码服务
                .authorizationCodeServices(authorizationCodeServices)
                // 密码模式支持
                .authenticationManager(authenticationManager(authenticationConfiguration))
                // token 存储
                .tokenStore(redisTokenStore())
                // token 格式
                .accessTokenConverter(jwtAccessTokenConverter())
                // 是否重用 refresh token
                .reuseRefreshTokens(false)
                // 添加拦截器
                .addInterceptor(new ClearInterceptor())
                // 自定义异常翻译
                .exceptionTranslator(oAuthWebRespExceptionTranslator());
    }

    /**
     * properties
     */
    @Bean
    @ConditionalOnMissingBean
    public OAuth2Properties oAuth2Properties() {
        return new OAuth2Properties();
    }

    /**
     * 根据签名生成 token
     */
    @Bean
    @ConditionalOnMissingBean
    public JwtAccessTokenConverter jwtAccessTokenConverter() {
        JwtAccessTokenConverter jwtAccessTokenConverter = new JwtAccessTokenConverter();
        jwtAccessTokenConverter.setSigningKey(oAuth2Properties().getSignKey());
        return jwtAccessTokenConverter;
    }

    /**
     * 将 token 存储到 redis
     */
    @Bean
    @ConditionalOnMissingBean
    public CustomRedisTokenStore redisTokenStore() {
        return new CustomRedisTokenStore(redisConnectionFactory);
    }

    /**
     * 认证管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration auth) {
        return new ProviderManager(Lists.newArrayList(daoAuthenticationProvider()));
    }

    /**
     * 认证处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public EntryPointAuthHandler entryPointAuthHandler() {
        return new EntryPointAuthHandler();
    }

    /**
     * OAuth2 授权相关模式异常翻译类
     */
    @Bean
    @ConditionalOnMissingBean
    public OAuthWebRespExceptionTranslator oAuthWebRespExceptionTranslator() {
        return new OAuthWebRespExceptionTranslator();
    }

    /**
     * token 校验
     */
    @Bean
    @Primary
    public DefaultTokenServices defaultTokenServices() {
        DefaultTokenServices tokenServices = new DefaultTokenServices();
        tokenServices.setTokenStore(redisTokenStore());
        tokenServices.setSupportRefreshToken(true);
        tokenServices.setAuthenticationManager(authenticationManager(authenticationConfiguration));
        return tokenServices;
    }

    /**
     * 自定义密码解析
     */
    @Bean
    @ConditionalOnMissingBean
    public CustomPasswordEncoder passwordEncoder() {
        return new CustomPasswordEncoder();
    }

    /**
     * 原始用户名密码处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider daoAuthenticationProvider = new DaoAuthenticationProvider();
        daoAuthenticationProvider.setUserDetailsService(userDetailsService);
        daoAuthenticationProvider.setPasswordEncoder(passwordEncoder());
        return daoAuthenticationProvider;
    }

    /**
     * 自定义Request Reject Handler
     */
    @Bean
    @ConditionalOnMissingBean
    public RequestRejectedHandler requestRejectedHandler() {
        return new CustomRequestRejectHandler();
    }
}
