package com.mira.openapi.config.oauth2;

import com.mira.openapi.handler.oauth2.CustomAuthenticationFailureHandler;
import com.mira.openapi.handler.oauth2.CustomAuthenticationSuccessHandler;
import com.mira.openapi.properties.OAuth2Properties;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;

import javax.annotation.Resource;

/**
 * security 配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
public class SecurityConfiguration extends WebSecurityConfigurerAdapter {
    @Resource
    private OAuth2Properties oAuth2Properties;
    @Resource
    private CustomAuthenticationSuccessHandler customAuthenticationSuccessHandler;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        String[] apiWhite = oAuth2Properties.getApiWhite().split(",");
        String[] resourceWhite = oAuth2Properties.getResourcesWhite().split(",");
        http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED);
        http.formLogin()
                // 登陆页面
                .loginPage("/oauth/login")
                // 处理登陆，org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
                .loginProcessingUrl("/login")
                // 登陆成功处理器
                .successHandler(customAuthenticationSuccessHandler)
                // 登陆失败处理器
                .failureHandler(new CustomAuthenticationFailureHandler())
                .and()
                // 鉴权
                .authorizeRequests()
                // 放行白名单和资源路径
                .antMatchers(ArrayUtils.addAll(apiWhite, resourceWhite)).permitAll()
                // 所有请求都鉴权
                .anyRequest().authenticated()
                .and()
                .csrf().disable();
    }
}