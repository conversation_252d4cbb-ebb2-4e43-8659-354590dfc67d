package com.mira.openapi.controller.v1.clinic;

import com.mira.core.annotation.Idempotent;
import com.mira.openapi.model.dto.DoctorCreateDTO;
import com.mira.openapi.model.dto.DoctorDeleteDTO;
import com.mira.openapi.model.dto.DoctorUpdateDTO;
import com.mira.openapi.model.vo.DoctorInfoVO;
import com.mira.openapi.model.vo.ResultWrapper;
import com.mira.openapi.service.clinic.IDoctorService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * doctor controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/doctor")
public class DoctorController {
    @Resource
    private IDoctorService doctorService;

    /**
     * 添加诊所医生
     */
    @Idempotent(expire = 2L)
    @PostMapping("/{clinic_id}")
    public ResultWrapper<Void> create(@PathVariable("clinic_id") String clinic_id,
                                      @Valid @RequestBody DoctorCreateDTO doctorCreateDTO) {
        doctorService.create(clinic_id, doctorCreateDTO);
        return ResultWrapper.wrap();
    }

    /**
     * 更新诊所下的医生
     */
    @PatchMapping("/{clinic_id}")
    public ResultWrapper<Void> update(@PathVariable("clinic_id") String clinic_id,
                                      @Valid @RequestBody DoctorUpdateDTO doctorUpdateDTO) {
        doctorService.update(clinic_id, doctorUpdateDTO);
        return ResultWrapper.wrap();
    }

    /**
     * 从诊所移除医生
     */
    @DeleteMapping("/{clinic_id}")
    public ResultWrapper<String> delete(@PathVariable("clinic_id") String clinic_id,
                                        @Valid @RequestBody DoctorDeleteDTO doctorDeleteDTO) {
        doctorService.delete(clinic_id, doctorDeleteDTO);
        return ResultWrapper.wrap();
    }

    /**
     * 医生信息
     */
    @GetMapping("/{clinic_id}")
    public ResultWrapper<List<DoctorInfoVO>> get(@PathVariable("clinic_id") String clinic_id) {
        return ResultWrapper.wrap(doctorService.get(clinic_id));
    }
}
