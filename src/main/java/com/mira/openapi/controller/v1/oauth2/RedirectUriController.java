package com.mira.openapi.controller.v1.oauth2;

import com.mira.openapi.model.dto.RedirectUriDTO;
import com.mira.openapi.model.vo.RedirectUriVO;
import com.mira.openapi.model.vo.ResultWrapper;
import com.mira.openapi.service.oauth2.IRedirectUriService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * OAuth2 redirect uri controller
 *
 * <AUTHOR>
 */
@RestController
public class RedirectUriController {
    @Resource
    private IRedirectUriService redirectUriService;

    /**
     * 更新回调地址
     */
    @PatchMapping("/oauth/redirect_uri")
    public ResultWrapper<Void> update(@Valid @RequestBody RedirectUriDTO redirectUriDTO) {
        redirectUriService.update(redirectUriDTO);
        return ResultWrapper.wrap();
    }

    /**
     * 回调地址列表
     */
    @GetMapping("/oauth/redirect_uri/{client_id}")
    public ResultWrapper<RedirectUriVO> get(@PathVariable("client_id") String client_id) {
        return ResultWrapper.wrap(redirectUriService.get(client_id));
    }
}
