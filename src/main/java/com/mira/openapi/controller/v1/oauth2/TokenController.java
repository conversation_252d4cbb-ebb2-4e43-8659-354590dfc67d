package com.mira.openapi.controller.v1.oauth2;

import com.mira.openapi.service.oauth2.ITokenService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.security.Principal;
import java.util.Map;

/**
 * token endpoint controller
 *
 * <AUTHOR>
 */
@RestController
public class TokenController {
    @Resource
    private ITokenService tokenService;

    @PostMapping("/oauth/token")
    public ResponseEntity<OAuth2AccessToken> generateToken(Principal principal,
                                                           @RequestParam Map<String, String> parameters) throws HttpRequestMethodNotSupportedException {
        return tokenService.generateToken(principal, parameters);
    }
}
