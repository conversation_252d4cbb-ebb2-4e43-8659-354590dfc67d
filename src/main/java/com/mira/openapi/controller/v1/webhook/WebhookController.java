package com.mira.openapi.controller.v1.webhook;

import com.mira.openapi.model.dto.webhook.WebhookSubscriptionDTO;
import com.mira.openapi.model.vo.ResultWrapper;
import com.mira.openapi.model.vo.webhook.WebhookRegisterVO;
import com.mira.openapi.service.webhook.IWebhookService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * webhook controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1")
public class WebhookController {
    @Resource
    private IWebhookService webhookService;

    /**
     * 获取当前注册事件
     */
    @GetMapping("/webhook/{client_id}")
    public ResultWrapper<WebhookRegisterVO> webhookList(@PathVariable("client_id") String client_id) {
        return ResultWrapper.wrap(webhookService.list(client_id));
    }

    /**
     * webhook 注册
     */
    @PostMapping("/webhook")
    public ResultWrapper<Void> webhookRegister(@Valid @RequestBody WebhookSubscriptionDTO webhookSubscriptionDTO) {
        webhookService.register(webhookSubscriptionDTO);
        return ResultWrapper.wrap();
    }
}
