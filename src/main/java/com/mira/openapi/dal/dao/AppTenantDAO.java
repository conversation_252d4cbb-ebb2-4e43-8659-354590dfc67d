package com.mira.openapi.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.openapi.dal.entity.AppTenantEntity;
import com.mira.openapi.dal.mapper.AppTenantMapper;
import org.springframework.stereotype.Repository;

@Repository
public class AppTenantDAO extends ServiceImpl<AppTenantMapper, AppTenantEntity> {
    public AppTenantEntity getByTenantCode(String tenantCode) {
        return getOne(Wrappers.<AppTenantEntity>lambdaQuery()
                .eq(AppTenantEntity::getCode, tenantCode)
                .last("limit 1"));
    }

    public void removeClinic(Long id) {
        getBaseMapper().removeClinic(id);
    }
}
