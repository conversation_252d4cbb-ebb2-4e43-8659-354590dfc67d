package com.mira.openapi.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.openapi.dal.entity.AppUserInfoEntity;
import com.mira.openapi.dal.mapper.AppUserInfoMapper;
import org.springframework.stereotype.Repository;

/**
 * app_user_info DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserInfoDAO extends ServiceImpl<AppUserInfoMapper, AppUserInfoEntity> {
    public AppUserInfoEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppUserInfoEntity>lambdaQuery()
                .eq(AppUserInfoEntity::getUserId, userId));
    }
}
