package com.mira.openapi.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.openapi.dal.entity.OauthUserConnectionsEntity;
import com.mira.openapi.dal.mapper.OauthUserConnectionsMapper;
import org.springframework.stereotype.Repository;

@Repository
public class OauthUserConnectionsDAO extends ServiceImpl<OauthUserConnectionsMapper, OauthUserConnectionsEntity> {
    public OauthUserConnectionsEntity getByClientIdAndEmail(String clientId, String email) {
        return getOne(Wrappers.<OauthUserConnectionsEntity>lambdaQuery()
                .eq(OauthUserConnectionsEntity::getClientId, clientId)
                .eq(OauthUserConnectionsEntity::getEmail, email));
    }
}
