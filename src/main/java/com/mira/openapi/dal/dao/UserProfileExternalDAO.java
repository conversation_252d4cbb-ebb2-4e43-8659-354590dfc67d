package com.mira.openapi.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.openapi.dal.entity.UserProfileExternalEntity;
import com.mira.openapi.dal.mapper.UserProfileExternalMapper;
import org.springframework.stereotype.Repository;

@Repository
public class UserProfileExternalDAO extends ServiceImpl<UserProfileExternalMapper, UserProfileExternalEntity> {
    public UserProfileExternalEntity getByUserId(Long userId) {
        return getOne(Wrappers.<UserProfileExternalEntity>lambdaQuery()
                .eq(UserProfileExternalEntity::getUserId, userId));
    }

    public UserProfileExternalEntity getByExternalId(String externalId) {
        return getOne(Wrappers.<UserProfileExternalEntity>lambdaQuery()
                .eq(UserProfileExternalEntity::getExternalId, externalId));
    }
}
