package com.mira.openapi.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("app_tenant")
public class AppTenantEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户code
     */
    private String code;

    /**
     * 租户名称
     */
    private String name;

    /**
     * 租户图标
     */
    private String icon;

    /**
     * 通知图标
     */
    private String notificationIcon;

    /**
     * 初始密码
     */
    private String initPassword;

    /**
     * 租户描述
     */
    private String description;
}