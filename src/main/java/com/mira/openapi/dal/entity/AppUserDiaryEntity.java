package com.mira.openapi.dal.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 用户日记记录表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_diary")
public class AppUserDiaryEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 日记时间戳,年月日（不带时分秒）
     */
    private Long diaryDay;

    /**
     * 日记时间（年月日）
     */
    private String diaryDayStr;

    /**
     * 日记
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String notes;

    /**
     * 温度
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private BigDecimal tempC;

    /**
     * 温度
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private BigDecimal tempF;

    /**
     * 温度测试时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String tempTime;

    /**
     * 体重
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private BigDecimal weightK;

    /**
     * 体重
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private BigDecimal weightL;

    /**
     * 心情
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String mood;

    /**
     * 是否同房
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String sex;

    /**
     * Cervical Mucus -- 白带状态(粘液状态)
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String mucusType;

    /**
     * Cervical Mucus -- 白带流量
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String mucusFlow;

    /**
     * 验孕记录
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Boolean pregnant;

    /**
     * 排卵测试记录
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Boolean opk;

    /**
     * 出血状态
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String flowAndSpotting;

    /**
     * 子宫位置
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String cervicalPosition;

    /**
     * Firmness
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String cervicalFirmness;

    /**
     * Openness
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String cervicalOpenness;

    /**
     * 0:未测试，1:已测试
     *
     * @deprecated
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer hormone;

    /**
     * 症状 (数组)
     *
     * @deprecated
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String symptoms;
}
