package com.mira.openapi.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("oauth_user_connections")
public class OauthUserConnectionsEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * client id
     */
    private String clientId;

    /**
     * user's email
     */
    private String email;

    /**
     * access token
     */
    private String accessToken;

    /**
     * refresh_token
     */
    private String refreshToken;

    /**
     * token_expires_at
     */
    private Double tokenExpiresAt;

    /**
     * status: active, revoked, expired
     */
    private String status;

    /**
     * created_at
     */
    private Long createdAt;

    /**
     * updated_at
     */
    private Long updatedAt;
}
