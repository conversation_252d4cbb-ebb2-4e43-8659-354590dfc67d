package com.mira.openapi.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.openapi.dal.entity.AppTenantDoctorEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AppTenantDoctorMapper extends BaseMapper<AppTenantDoctorEntity> {
    /**
     * 删除医生
     *
     * @param id id
     */
    void removeDoctor(@Param("id") Long id);
}
