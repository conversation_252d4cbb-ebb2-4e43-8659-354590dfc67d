package com.mira.openapi.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.openapi.dal.entity.AppTenantEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AppTenantMapper extends BaseMapper<AppTenantEntity> {
    /**
     * 删除诊所
     *
     * @param id id
     */
    void removeClinic(@Param("id") Long id);
}
