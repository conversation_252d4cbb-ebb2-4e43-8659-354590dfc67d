package com.mira.openapi.enums;

import lombok.Getter;

/**
 * 诊所用户角色枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ClinicRoleEnum {
    CLINIC_ADMIN(1, "clinic管理员"),
    DOCTOR(2, "医生"),
    NURSE(3, "护士");

    private final Integer code;
    private final String desc;

    ClinicRoleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ClinicRoleEnum getByCode(Integer code) {
        for (ClinicRoleEnum clinicRoleEnum : ClinicRoleEnum.values()) {
            if (clinicRoleEnum.getCode().equals(code)) {
                return clinicRoleEnum;
            }
        }
        return null;
    }
}
