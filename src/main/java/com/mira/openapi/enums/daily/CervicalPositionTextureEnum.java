package com.mira.openapi.enums.daily;

import lombok.Getter;

/**
 * 子宫纹理
 */
@Getter
public enum CervicalPositionTextureEnum {
    S("s", "Soft"),
    M("m", "Medium"),
    F("f", "Firm");

    private final String value;
    private final String description;

    CervicalPositionTextureEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static CervicalPositionTextureEnum get(String value) {
        for (CervicalPositionTextureEnum textureEnum : values()) {
            if (textureEnum.getValue().equals(value)) {
                return textureEnum;
            }
        }
        return null;
    }
}
