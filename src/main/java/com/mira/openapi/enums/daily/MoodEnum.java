package com.mira.openapi.enums.daily;

import lombok.Getter;

/**
 * 心情记录
 */
@Getter
public enum MoodEnum {
    /**
     * 大笑
     */
    E("e", "Excited"),
    /**
     * 微笑
     */
    H("h", "Happy"),
    /**
     * 不高兴
     */
    S("s", "Sad"),
    /**
     * 疼痛
     */
    T("t", "Tired"),
    /**
     * 烦躁
     */
    I("i", "Irritable");

    private final String value;
    private final String description;

    MoodEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static MoodEnum get(String value) {
        for (MoodEnum moodEnum : values()) {
            if (moodEnum.getValue().equals(value)) {
                return moodEnum;
            }
        }
        return null;
    }
}
