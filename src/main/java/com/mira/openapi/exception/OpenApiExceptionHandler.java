package com.mira.openapi.exception;

import com.mira.openapi.model.vo.ResultWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.common.exceptions.RedirectMismatchException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

/**
 * 开放服务异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@ResponseBody
@RestControllerAdvice
public class OpenApiExceptionHandler implements Ordered {
    @ExceptionHandler(OpenApiException.class)
    public ResultWrapper<String> openApiException(HttpServletResponse response, OpenApiException ex) {
        log.info("code:{}, msg:{}", ex.getCode(), ex.getMsg());
        response.setStatus(ex.getCode());
        return ResultWrapper.wrap(ex.getMsg());
    }

    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResultWrapper<String> httpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        log.error("HttpRequestMethodNotSupportedException: {}", ex.getMessage());
        return ResultWrapper.wrap(HttpStatus.METHOD_NOT_ALLOWED.getReasonPhrase());
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(ConstraintViolationException.class)
    public ResultWrapper<String> violationException(ConstraintViolationException ex) {
        log.error("ConstraintViolationException: {}", ex.getMessage());
        Set<ConstraintViolation<?>> constraintViolations = ex.getConstraintViolations();
        for (ConstraintViolation o : constraintViolations) {
            return ResultWrapper.wrap(o.getMessage());
        }
        return ResultWrapper.wrap(HttpStatus.BAD_REQUEST.getReasonPhrase().concat(", ").concat("incomplete request parameters"));
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultWrapper<String> methodArgumentNotValidException(MethodArgumentNotValidException ex) {
        String errorMsg = ex.getBindingResult().getAllErrors().get(0).getDefaultMessage();
        log.error("MethodArgumentNotValidException: {}", errorMsg);
        return ResultWrapper.wrap(errorMsg);
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResultWrapper<String> missingServletRequestParameterException(MissingServletRequestParameterException ex) {
        log.error("MissingServletRequestParameterException: {}", ex.getMessage());
        return ResultWrapper.wrap(HttpStatus.BAD_REQUEST.getReasonPhrase().concat(", ").concat("incomplete request parameters"));
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(BindException.class)
    public ResultWrapper<String> bindException(BindException ex) {
        log.error("BindException: {}", ex.getMessage());
        return ResultWrapper.wrap(HttpStatus.BAD_REQUEST.getReasonPhrase().concat(", ").concat("incomplete request parameters"));
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(InvalidGrantException.class)
    public ResultWrapper<String> invalidGrantException(InvalidGrantException ex) {
        log.error("InvalidGrantException: {}", ex.getMessage());
        return ResultWrapper.wrap(ex.getMessage());
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(RedirectMismatchException.class)
    public ResultWrapper<String> redirectMismatchException(RedirectMismatchException ex) {
        log.error("RedirectMismatchException: {}", ex.getMessage());
        return ResultWrapper.wrap(ex.getMessage());
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(BadCredentialsException.class)
    public ResultWrapper<String> badCredentialsException(BadCredentialsException ex) {
        log.error("BadCredentialsException: {}", ex.getMessage());
        return ResultWrapper.wrap(ex.getMessage());
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE + 8;
    }
}
