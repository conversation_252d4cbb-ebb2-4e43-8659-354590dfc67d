package com.mira.openapi.handler.hormone;

import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.openapi.model.dto.HormoneDTO;
import com.mira.openapi.model.vo.HormoneVO;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * HCG_Qualitative
 *
 * <AUTHOR>
 */
@Component
public class HcgQualitativeHormoneHandler implements IHormoneHandler<HormoneDTO, HormoneVO> {
    @PostConstruct
    public void init() {
        HormoneHandler.set(WandTypeEnum.HCG_QUALITATIVE.getInteger(), this);
    }

    @Override
    public void handle(HormoneDTO hormoneDTO, HormoneVO hormoneVO) {
        Float value1 = hormoneDTO.getTest_results().getValue1();
        Float valueResult1 = 1F;
        if (value1 < 10) {
            valueResult1 = 0F;
        }
        hormoneVO.setHcg(String.valueOf(valueResult1));
    }
}
