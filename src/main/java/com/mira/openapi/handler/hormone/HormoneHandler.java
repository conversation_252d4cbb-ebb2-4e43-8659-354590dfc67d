package com.mira.openapi.handler.hormone;

import java.util.HashMap;
import java.util.Map;

/**
 * 激素数据处理器
 *
 * <AUTHOR>
 */
public class HormoneHandler {
    private final static Map<String, IHormoneHandler> HANDLER_MAP = new HashMap<>();

    public static void set(Integer wangType, IHormoneHandler hormoneHandler) {
        HANDLER_MAP.put(String.valueOf(wangType), hormoneHandler);
    }

    public static IHormoneHandler get(Integer wandType) {
        if (wandType == null) {
            return null;
        }
        return HANDLER_MAP.get(String.valueOf(wandType));
    }
}
