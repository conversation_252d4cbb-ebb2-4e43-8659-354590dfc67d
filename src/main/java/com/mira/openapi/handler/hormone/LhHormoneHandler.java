package com.mira.openapi.handler.hormone;

import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.NumberFormatUtil;
import com.mira.openapi.model.dto.HormoneDTO;
import com.mira.openapi.model.vo.HormoneVO;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * LH
 *
 * <AUTHOR>
 */
@Component
public class LhHormoneHandler implements IHormoneHandler<HormoneDTO, HormoneVO> {
    @PostConstruct
    public void init() {
        HormoneHandler.set(WandTypeEnum.LH.getInteger(), this);
    }

    @Override
    public void handle(HormoneDTO hormoneDTO, HormoneVO hormoneVO) {
        hormoneVO.setLh(String.valueOf(NumberFormatUtil.format(hormoneDTO.getTest_results().getValue1())));
    }
}
