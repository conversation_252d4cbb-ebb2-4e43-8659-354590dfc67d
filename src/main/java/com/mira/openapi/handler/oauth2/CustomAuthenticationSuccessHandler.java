package com.mira.openapi.handler.oauth2;

import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.openapi.config.oauth2.LoginUserDetails;
import com.mira.openapi.model.vo.ResultWrapper;
import com.mira.openapi.dal.dao.OauthAuthorizeLogDAO;
import com.mira.openapi.dal.entity.OauthAuthorizeLogEntity;
import com.mira.openapi.utils.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 自定义认证成功处理器
 *
 * <AUTHOR>
 * @see org.springframework.security.web.savedrequest.DefaultSavedRequest toString()
 * @see org.springframework.security.web.savedrequest.HttpSessionRequestCache SAVED_REQUEST
 */
@Slf4j
@Component
public class CustomAuthenticationSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {
    @Resource
    private OauthAuthorizeLogDAO oauthAuthorizeLogDAO;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        if (request.getSession().getAttribute("SPRING_SECURITY_SAVED_REQUEST") != null) {
            String savedRequest = request.getSession().getAttribute("SPRING_SECURITY_SAVED_REQUEST").toString();
            String redirectUrl = savedRequest.substring(savedRequest.indexOf("http"), savedRequest.length() - 1);
            // save authorize log
            saveAuthorizeLog(redirectUrl, authentication);
            response.setContentType("application/json");
            Map<String, String> data = new HashMap<>();
            data.put("redirectUrl", redirectUrl);
            ResponseUtil.responseWrite(response, ResultWrapper.wrap(data));
        } else {
            super.onAuthenticationSuccess(request, response, authentication);
        }
    }

    private void saveAuthorizeLog(String redirectUrl, Authentication authentication) {
        CompletableFuture.runAsync(() -> {
            String clinicId = "";
            String[] pairs = redirectUrl.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if ("client_id".equals(keyValue[0])) {
                    clinicId = keyValue[1];
                    break;
                }
            }
            LoginUserDetails principal = (LoginUserDetails) authentication.getPrincipal();
            String email = principal.getUsername();

            // save authorize
            String timeZone = "America/Chicago";
            OauthAuthorizeLogEntity oauthAuthorizeLogEntity = new OauthAuthorizeLogEntity();
            oauthAuthorizeLogEntity.setClinicId(clinicId);
            oauthAuthorizeLogEntity.setEmail(email);
            oauthAuthorizeLogEntity.setAuthorizeTime(ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN));
            oauthAuthorizeLogEntity.setTimeZone(timeZone);
            oauthAuthorizeLogEntity.setNote(redirectUrl);
            oauthAuthorizeLogDAO.save(oauthAuthorizeLogEntity);
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("save app_tenant_patient_authorize error", ex);
            return null;
        });
    }
}
