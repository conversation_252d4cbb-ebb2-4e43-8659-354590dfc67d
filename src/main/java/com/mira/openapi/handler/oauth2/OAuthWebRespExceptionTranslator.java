package com.mira.openapi.handler.oauth2;

import com.mira.openapi.model.vo.ResultWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.exceptions.*;
import org.springframework.security.oauth2.provider.error.WebResponseExceptionTranslator;
import org.springframework.web.HttpRequestMethodNotSupportedException;

import java.util.HashMap;
import java.util.Map;

/**
 * 自定义异常翻译类，涉及到授权的模式都走翻译类。
 * <p>作用：将 OAuth 抛出的异常按照我们自定义的数据格式返回（ControllerAdvice 捕获不了）
 *
 * <AUTHOR>
 */
public class OAuthWebRespExceptionTranslator implements WebResponseExceptionTranslator {
    @Override
    public ResponseEntity translate(Exception e) throws Exception {
        ResponseEntity.BodyBuilder status = ResponseEntity.status(HttpStatus.UNAUTHORIZED);
        // 认证失败
        String message = "Authentication failure";

        // 请求方式错误
        if (e instanceof HttpRequestMethodNotSupportedException) {
            message = e.getMessage();
        }
        // 不支持的认证类型
        if (e instanceof UnsupportedGrantTypeException) {
            message = "Unsupported authentication types";
            return status.body(buildResult(message));
        }
        // Redirect Uri 不匹配
        if (e instanceof RedirectMismatchException) {
            message = "Invalid redirect URI. Please check your configuration.";
            return status.body(buildResult(message));
        }
        // Response Type 不匹配
        if (e instanceof UnsupportedResponseTypeException) {
            message = "Unsupported response type";
            return status.body(buildResult(message));
        }
        // 令牌已过期
        if (e instanceof InvalidTokenException
                && StringUtils.containsIgnoreCase(e.getMessage(), "Invalid refresh token (expired)")) {
            message = "Token has expired";
            return status.body(buildResult(message));
        }
        // 不是有效的scope值
        if (e instanceof InvalidScopeException) {
            message = "Not a valid scope value";
            return status.body(buildResult(message));
        }

        if (e instanceof InvalidGrantException) {
            // 无效的刷新令牌
            if (StringUtils.containsIgnoreCase(e.getMessage(), "Invalid refresh token")) {
                message = "Invalid refresh token";
                return status.body(buildResult(message));
            }
            // 无效的授权码
            if (StringUtils.containsIgnoreCase(e.getMessage(), "Invalid authorization code")) {
                message = "Invalid authorization code";
                return status.body(buildResult(message));
            }
            // 用户已被锁定
            if (StringUtils.containsIgnoreCase(e.getMessage(), "locked")) {
                message = "User has been locked out";
                return status.body(buildResult(message));
            }
        }

        return status.body(message);
    }

    private String buildResult(String msg) {
        return msg;
    }

//    private ResultWrapper<?> buildResult(String msg) {
//        return ResultWrapper.wrap(msg);
//    }
}
