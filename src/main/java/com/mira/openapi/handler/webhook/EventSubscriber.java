package com.mira.openapi.handler.webhook;

import com.mira.core.util.JsonUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.openapi.config.RedisComponent;
import com.mira.openapi.dal.dao.AppUserDAO;
import com.mira.openapi.dal.dao.OauthUserConnectionsDAO;
import com.mira.openapi.dal.dao.OpenWebhookDAO;
import com.mira.openapi.dal.entity.AppUserEntity;
import com.mira.openapi.dal.entity.OauthUserConnectionsEntity;
import com.mira.openapi.dal.entity.OpenWebhookEntity;
import com.mira.openapi.model.vo.webhook.WebhookResultVO;
import com.mira.openapi.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * redis event subscriber
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class EventSubscriber {
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private AppUserDAO appUserDAO;
    @Resource
    private OpenWebhookDAO openWebhookDAO;
    @Resource
    private OauthUserConnectionsDAO oauthUserConnectionsDAO;

    public void onMessage(String message) {
        // convert
        String json = JsonUtil.toObject(message, String.class);
        Map<String, String> eventMap = JsonUtil.toObject(json, Map.class);

        // params
        Long userId = Long.valueOf(eventMap.get("user_id"));
        String eventId = eventMap.get("event_id");
        String event = eventMap.get("event");

        // lock
        String lockKey = "event_lock:" + userId + ":" + eventId;
        Boolean locked = redisComponent.setIfAbsent(lockKey, "1", 30, TimeUnit.SECONDS);
        if (!locked) {
            return;
        }

        try {
            processEvent(userId, event);
        } catch (Exception e) {
            log.error("process event error, message:{}", message, e);
        }
    }

    private void processEvent(Long userId, String event) {
        EventEnum eventEnum = EventEnum.get(event);
        if (eventEnum == null) {
            log.error("process event not found, user_id:{}, event:{}", userId, event);
            return;
        }

        // app user entity
        AppUserEntity appUserEntity = appUserDAO.getById(userId);
        String email = appUserEntity.getEmail();

        // vo
        WebhookResultVO webhookResult = new WebhookResultVO();
        webhookResult.setEvent(event);
        webhookResult.setEmail(email);

        // push
        push(event, webhookResult);
    }

    private void push(String event, WebhookResultVO webhookResult) {
        List<OpenWebhookEntity> openWebhookEntities = openWebhookDAO.listByEventType(event);
        if (CollectionUtils.isEmpty(openWebhookEntities)) {
            return;
        }

        for (OpenWebhookEntity openWebhookEntity : openWebhookEntities) {
            String pushUrl = openWebhookEntity.getPushUrl();

//            CompletableFuture.runAsync(() -> {
                // check user active
                String clientId = openWebhookEntity.getClientId();
                if (!checkUserActive(clientId, webhookResult.getEmail())) {
                    return;
                }

                try {
                    HttpUtil.post(pushUrl, JsonUtil.toJson(webhookResult));
                } catch (IOException e) {
                    log.error("push webhook error", e);
                }

//            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
//                log.error("push webhook error, client_id:{}, event_type:{}, push_url:{} message:{}",
//                        openWebhookEntity.getClientId(), event, pushUrl, ex.getMessage());
//                return null;
//            });
        }
    }

    private boolean checkUserActive(String clientId, String email) {
        OauthUserConnectionsEntity oauthUserConnectionsEntity = oauthUserConnectionsDAO.getByClientIdAndEmail(clientId, email);
        if (oauthUserConnectionsEntity == null) {
            return false;
        }
        if (!clientId.equals(oauthUserConnectionsEntity.getClientId())) {
            log.info("check user active, client_id not match, open_webhook client_id:{}, oauth_user_connections client_id:{}, email:{}",
                    clientId, oauthUserConnectionsEntity.getClientId(), email);
            return false;
        }
        if (!"active".equals(oauthUserConnectionsEntity.getStatus())) {
            log.info("check user active, status not active, status:{}, email:{}", oauthUserConnectionsEntity.getStatus(), email);
            return false;
        }
        Double tokenExpiresAt = oauthUserConnectionsEntity.getTokenExpiresAt();
        if (tokenExpiresAt - System.currentTimeMillis() < 0) {
            log.info("check user active, token expired, expire:{}, email:{}", tokenExpiresAt, email);
            // update oauth_user_connections status to inactive
            oauthUserConnectionsEntity.setStatus("inactive");
            oauthUserConnectionsEntity.setUpdatedAt(System.currentTimeMillis());
            oauthUserConnectionsDAO.updateById(oauthUserConnectionsEntity);
            return false;
        }

        return true;
    }
}
