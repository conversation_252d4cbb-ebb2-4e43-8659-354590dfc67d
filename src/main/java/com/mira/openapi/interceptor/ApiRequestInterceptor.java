package com.mira.openapi.interceptor;

import com.google.common.collect.Lists;
import com.mira.core.util.IpUtils;
import com.mira.openapi.config.StringRedisComponent;
import com.mira.openapi.config.annotation.RequestRateLimit;
import com.mira.openapi.consts.ErrorMsgConst;
import com.mira.openapi.consts.LuaScript;
import com.mira.openapi.model.vo.ResultWrapper;
import com.mira.openapi.utils.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * API 请求拦截器
 *
 * <AUTHOR>
 */
@Slf4j
public class ApiRequestInterceptor implements HandlerInterceptor {
    @Resource
    private StringRedisComponent stringRedisComponent;

    /**
     * 持续时间，单位秒
     */
    @Value("${api.limit.duration}")
    private int duration;

    /**
     * 限制次数
     */
    @Value("${api.limit.count}")
    private int limitCount;

    /**
     * redis key 前缀
     */
    private final static String KEY_PREFIX = "API_REQUEST_LIMIT:";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 全局限流按照默认值，如果有注解，按照注解值限流
        long currentRequestCount;
        String ip = IpUtils.getIp(request);
        try {
            if (!handler.getClass().isAssignableFrom(HandlerMethod.class)) {
                return true;
            }
            Method method = ((HandlerMethod) handler).getMethod();

            if (method.isAnnotationPresent(RequestRateLimit.class)) {
                RequestRateLimit ipRequestLimit = method.getAnnotation(RequestRateLimit.class);
                currentRequestCount = stringRedisComponent.eval(LuaScript.IP_REQUEST_LIMIT,
                        Lists.newArrayList(KEY_PREFIX + ipRequestLimit.path() + ":" + ip.replaceAll(":", "-")),
                        Long.class, String.valueOf(ipRequestLimit.duration()), String.valueOf(ipRequestLimit.count()));
            } else {
                currentRequestCount = stringRedisComponent.eval(LuaScript.IP_REQUEST_LIMIT,
                        Lists.newArrayList(KEY_PREFIX + request.getRequestURI() + ":" + ip.replaceAll(":", "-")),
                        Long.class, String.valueOf(duration), String.valueOf(limitCount));
            }
        } catch (Exception e) {
            log.error("ApiRequestInterceptor Error:{}", e.getMessage());
            return true;
        }

        if (currentRequestCount == 0) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            ResponseUtil.responseWrite(response, ResultWrapper.wrap(ErrorMsgConst.TOO_MANY_REQUESTS));
            return false;
        }

        return true;
    }
}
