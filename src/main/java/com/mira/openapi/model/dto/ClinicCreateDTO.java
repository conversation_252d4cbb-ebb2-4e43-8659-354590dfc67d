package com.mira.openapi.model.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * add clinic dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ClinicCreateDTO {
    /**
     * 诊所唯一标识
     */
    @NotBlank(message = "Unique ID can not be empty.")
    @Size(max = 50, message = "Unique ID maximum length of 50.")
    private String id;

    /**
     * 诊所名
     */
    @NotBlank(message = "Clinic name can not be empty.")
    @Size(max = 120, message = "Clinic name maximum length of 120.")
    private String name;

    /**
     * 诊所logo，图片链接地址
     */
    @Size(max = 256, message = "Logo link maximum length of 256.")
    private String logo;
}
