package com.mira.openapi.model.dto;

import com.mira.openapi.enums.CycleStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 周期数据
 */
@Getter
@Setter
public class CycleDataDTO {
    /**
     * 主键
     */
    private Integer cycle_index;

    /**
     * 周期长度
     */
    private Integer len_cycle;

    /**
     * 周期开始日
     */
    private String date_period_start;

    /**
     * 经期结束日（经期不包含这天）
     */
    private String date_period_end;

    /**
     * 易孕期开始日 (由 fertility soccer 》=6 计算)
     */
    private String date_FW_start;

    /**
     * 易孕期结束日 （易孕期不包含这一天）
     */
    private String date_FW_end;

    /**
     * 排卵日 （预测 or LH 峰值日
     */
    private String date_ovulation;

    /**
     * 预留，可为 null （实际测量的最高值时间）
     */
    private String date_LH_surge;

    /**
     * float, 实际峰值日 中 LH的对应最大值，可为 null
     */
    private Float value_LH_surge;

    /**
     * 编辑经期新增的字段
     */
    private List<String> date_PDG_rise;

    /**
     * float, LH 阈值,可为 null
     */
    private Float threshold_LH;

    /**
     * float, E3G 阈值,可为 null
     */
    private Float threshold_E3G;

    /**
     * @see CycleStatusEnum
     */
    private Integer cycle_status;

    /**
     * 怀孕模式下，怀孕周期会分为3段，这个字段表示每段的长度
     */
    private List<Integer> len_phase;

    /**
     * 实周期和预测周期为“cd1”类似,怀孕周期为“Week 1,Day1”类似
     */
    private List<String> cycle_cd_index;

    /**
     * 易孕指数，len_cycle个元素 float ⼩数点后⼀位,每个元素可为null
     */
    private List<Float> fertility_score_list;
}
