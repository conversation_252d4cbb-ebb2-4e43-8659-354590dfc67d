package com.mira.openapi.model.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * add doctor dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class DoctorCreateDTO {
    /**
     * 医生名
     */
    @NotBlank(message = "Doctor name can not be empty.")
    @Size(max = 50, message = "Doctor name maximum length of 50.")
    private String name;

    /**
     * 医生邮箱
     */
    @NotBlank(message = "Doctor email can not be empty.")
    @Pattern(regexp = "[\\w!#$%&'*+/=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+[\\w](?:[\\w-]*[\\w])?",
            message = "That email doesn't look right. Please enter a valid email address.")
    @Size(max = 100, message = "Doctor email address is too lengthy. maximum length of 100.")
    private String email;
}
