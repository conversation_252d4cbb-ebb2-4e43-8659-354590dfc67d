package com.mira.openapi.model.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * delete doctor dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class DoctorDeleteDTO {
    /**
     * 医生邮箱
     */
    @NotBlank(message = "Doctor email can not be empty.")
    @Size(max = 100, message = "Doctor email address is too lengthy. maximum length of 100.")
    private String email;
}
