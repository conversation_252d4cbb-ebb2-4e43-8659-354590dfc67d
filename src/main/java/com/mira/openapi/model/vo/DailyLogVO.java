package com.mira.openapi.model.vo;

import com.mira.openapi.model.vo.daily.CervicalMucous;
import com.mira.openapi.model.vo.daily.CervicalPosition;
import com.mira.openapi.model.vo.daily.Symptoms;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * patient daily log vo
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class DailyLogVO {
    /**
     * Symptoms
     */
    private List<Symptoms> symptoms;

    /**
     * Mood
     */
    private String mood;

    /**
     * Sex / Insemination
     */
    private String sex;

    /**
     * Cervical Mucous
     */
    private CervicalMucous cervical_mucous;

    /**
     * Cervical Position
     */
    private CervicalPosition cervical_position;

    /**
     * Flow & Spotting
     */
    private String flow_spotting;
}
