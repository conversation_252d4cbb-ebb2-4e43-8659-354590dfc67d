package com.mira.openapi.service.clinic;

import com.mira.openapi.model.dto.DoctorCreateDTO;
import com.mira.openapi.model.dto.DoctorDeleteDTO;
import com.mira.openapi.model.dto.DoctorUpdateDTO;
import com.mira.openapi.model.vo.DoctorInfoVO;

import java.util.List;

/**
 * doctor service
 *
 * <AUTHOR>
 */
public interface IDoctorService {
    /**
     * 添加诊所医生
     *
     * @param clinic_id       诊所标识
     * @param doctorCreateDTO 参数
     */
    void create(String clinic_id, DoctorCreateDTO doctorCreateDTO);

    /**
     * 更新诊所下的医生
     *
     * @param clinic_id       诊所标识
     * @param doctorUpdateDTO 参数
     */
    void update(String clinic_id, DoctorUpdateDTO doctorUpdateDTO);

    /**
     * 从诊所移除医生
     *
     * @param clinic_id       诊所标记
     * @param doctorDeleteDTO 参数
     */
    void delete(String clinic_id, DoctorDeleteDTO doctorDeleteDTO);

    /**
     * 医生信息
     *
     * @param clinic_id 诊所标记
     * @return List<DoctorInfoVO>
     */
    List<DoctorInfoVO> get(String clinic_id);
}
