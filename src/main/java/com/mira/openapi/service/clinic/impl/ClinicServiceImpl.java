package com.mira.openapi.service.clinic.impl;

import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.openapi.consts.ErrorMsgConst;
import com.mira.openapi.dal.dao.AppTenantDAO;
import com.mira.openapi.dal.dao.AppTenantDoctorDAO;
import com.mira.openapi.dal.dao.AppTenantPatientDAO;
import com.mira.openapi.dal.entity.AppTenantEntity;
import com.mira.openapi.exception.OpenApiException;
import com.mira.openapi.model.dto.ClinicCreateDTO;
import com.mira.openapi.model.dto.ClinicUpdateDTO;
import com.mira.openapi.model.vo.ClinicInfoVO;
import com.mira.openapi.properties.InitProperties;
import com.mira.openapi.service.clinic.IClinicService;
import com.mira.openapi.utils.ClinicUtil;
import com.mira.openapi.utils.TimeZoneUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * clinic service impl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ClinicServiceImpl implements IClinicService {
    @Resource
    private AppTenantDAO appTenantDAO;
    @Resource
    private AppTenantDoctorDAO appTenantDoctorDAO;
    @Resource
    private AppTenantPatientDAO appTenantPatientDAO;
    @Resource
    private InitProperties initProperties;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(ClinicCreateDTO clinicCreateDTO) {
        // time zone
        String timeZone = TimeZoneUtil.timeZone();
        // tenant code
        String tenantCode = ClinicUtil.getTenantCode(clinicCreateDTO.getId());
        AppTenantEntity existClinic = appTenantDAO.getByTenantCode(tenantCode);
        if (existClinic != null) {
            return;
        }

        AppTenantEntity newAppTenant = new AppTenantEntity();
        newAppTenant.setCode(tenantCode);
        // 诊所名
        newAppTenant.setName(clinicCreateDTO.getName());
        // 诊所logo
        String icon = StringUtils.isNotBlank(clinicCreateDTO.getLogo()) ? clinicCreateDTO.getLogo()
                : initProperties.getIcon();
        newAppTenant.setIcon(icon);

        // save
        try {
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, newAppTenant);
        } catch (Exception e) {
            log.error("Clinic Create -> TenantCode:{}, uniqueID:{}, timeZone:{}, the timezone format is wrong.",
                    tenantCode, clinicCreateDTO.getId(), timeZone);
        }
        newAppTenant.setTimeZone(timeZone);
        appTenantDAO.save(newAppTenant);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(String id, ClinicUpdateDTO clinicUpdateDTO) {
        // time zone
        String timeZone = TimeZoneUtil.timeZone();
        // tenant code
        String tenantCode = ClinicUtil.getTenantCode(id);
        AppTenantEntity existClinic = appTenantDAO.getByTenantCode(tenantCode);
        if (existClinic == null) {
            throw new OpenApiException(HttpStatus.BAD_REQUEST.value(), ErrorMsgConst.CLINIC_NOT_EXIST);
        }

        // set
        if (StringUtils.isNotBlank(clinicUpdateDTO.getName())) {
            existClinic.setName(clinicUpdateDTO.getName());
        }
        if (StringUtils.isNotBlank(clinicUpdateDTO.getLogo())) {
            existClinic.setIcon(clinicUpdateDTO.getLogo());
        }
        existClinic.setTimeZone(timeZone);

        // update
        try {
            UpdateEntityTimeUtil.updateBaseEntityTime(existClinic.getTimeZone(), existClinic);
        } catch (Exception e) {
            log.error("Clinic Update -> TenantCode:{}, uniqueID:{}, timeZone:{}, the timezone format is wrong.",
                    tenantCode, id, existClinic.getTimeZone());
        }
        appTenantDAO.updateById(existClinic);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(String id) {
        String tenantCode = ClinicUtil.getTenantCode(id);
        AppTenantEntity existClinic = appTenantDAO.getByTenantCode(tenantCode);
        if (existClinic == null) {
            throw new OpenApiException(HttpStatus.BAD_REQUEST.value(), ErrorMsgConst.CLINIC_NOT_EXIST);
        }

        // delete
        appTenantDAO.removeClinic(existClinic.getId());
    }

    @Override
    public ClinicInfoVO get(String id) {
        String tenantCode = ClinicUtil.getTenantCode(id);
        AppTenantEntity existClinic = appTenantDAO.getByTenantCode(tenantCode);
        if (existClinic == null) {
            throw new OpenApiException(HttpStatus.BAD_REQUEST.value(), ErrorMsgConst.CLINIC_NOT_EXIST);
        }

        ClinicInfoVO clinicInfoVO = new ClinicInfoVO();
        clinicInfoVO.setName(existClinic.getName());
        clinicInfoVO.setLogo(existClinic.getIcon());
        clinicInfoVO.setDoctor_count(appTenantDoctorDAO.countByTenantCode(tenantCode));
        clinicInfoVO.setPatient_count(appTenantPatientDAO.countByTenantCode(tenantCode));

        return clinicInfoVO;
    }
}
