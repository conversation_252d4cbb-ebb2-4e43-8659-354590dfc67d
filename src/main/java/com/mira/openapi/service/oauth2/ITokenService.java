package com.mira.openapi.service.oauth2;

import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.web.HttpRequestMethodNotSupportedException;

import java.security.Principal;
import java.util.Map;

/**
 * token endpoint service
 *
 * <AUTHOR>
 */
public interface ITokenService {
    ResponseEntity<OAuth2AccessToken> generateToken(Principal principal, Map<String, String> parameters) throws HttpRequestMethodNotSupportedException;
}
