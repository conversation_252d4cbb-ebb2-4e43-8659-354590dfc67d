package com.mira.openapi.service.oauth2.impl;

import com.mira.core.holder.ContextHolder;
import com.mira.openapi.config.RedisComponent;
import com.mira.openapi.config.oauth2.LoginUserDetails;
import com.mira.openapi.consts.ErrorMsgConst;
import com.mira.openapi.consts.UserStatusConst;
import com.mira.openapi.dal.dao.AppUserDAO;
import com.mira.openapi.dal.entity.AppUserEntity;
import com.mira.openapi.exception.OpenApiException;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 用户信息获取服务
 *
 * <AUTHOR>
 */
@Service("openUserDetailsService")
public class OpenUserDetailsServiceImpl implements UserDetailsService {
    @Resource
    private AppUserDAO appUserDAO;
    @Resource
    private RedisComponent redisComponent;

    private final static String USER_LOGIN_LOCK = "AUTHORIZATION_LOGIN_LOCK:";

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        AppUserEntity appUser = appUserDAO.getByEmail(username);
        Optional.ofNullable(appUser).<OpenApiException>orElseThrow(() -> {
            throw new UsernameNotFoundException(username);
        });
        checkStatus(appUser);
        checkLoginLock(appUser);
        ContextHolder.put("salt", appUser.getSalt());
        ContextHolder.put("user_id", String.valueOf(appUser.getId()));

        return new LoginUserDetails(appUser.getId(), appUser.getEmail(), appUser.getPassword(), appUser.getSalt());
    }

    private void checkStatus(AppUserEntity appUser) {
        if (UserStatusConst.DISABLE == appUser.getStatus()) {
            throw new OpenApiException(HttpStatus.UNAUTHORIZED.value(), ErrorMsgConst.USER_LOCK);
        }
        if (UserStatusConst.INACTIVE == appUser.getStatus()) {
            throw new OpenApiException(HttpStatus.UNAUTHORIZED.value(), ErrorMsgConst.USER_NOT_ACTIVE);
        }
    }

    private void checkLoginLock(AppUserEntity appUser) {
        String key = USER_LOGIN_LOCK + appUser.getId();
        if (redisComponent.exists(key)) {
            throw new OpenApiException(HttpStatus.UNAUTHORIZED.value(), ErrorMsgConst.USER_LOGIN_LOCK);
        }
    }
}
