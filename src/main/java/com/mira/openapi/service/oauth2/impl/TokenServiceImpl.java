package com.mira.openapi.service.oauth2.impl;

import com.mira.core.util.JwtUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.openapi.dal.dao.OauthUserConnectionsDAO;
import com.mira.openapi.dal.entity.OauthUserConnectionsEntity;
import com.mira.openapi.properties.OAuth2Properties;
import com.mira.openapi.service.oauth2.ITokenService;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.provider.endpoint.TokenEndpoint;
import org.springframework.stereotype.Service;
import org.springframework.web.HttpRequestMethodNotSupportedException;

import javax.annotation.Resource;
import java.security.Principal;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * token endpoint service impl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TokenServiceImpl implements ITokenService {
    @Resource
    private TokenEndpoint tokenEndpoint;
    @Resource
    private OauthUserConnectionsDAO oauthUserConnectionsDAO;
    @Resource
    private OAuth2Properties oAuth2Properties;

    @Override
    public ResponseEntity<OAuth2AccessToken> generateToken(Principal principal, Map<String, String> parameters) throws HttpRequestMethodNotSupportedException {
        // generate token
        ResponseEntity<OAuth2AccessToken> oAuth2AccessTokenResponseEntity = tokenEndpoint.postAccessToken(principal, parameters);
        // save/update oauth_user_connections
        oauthUserConnections(parameters, oAuth2AccessTokenResponseEntity);

        return oAuth2AccessTokenResponseEntity;
    }

    private void oauthUserConnections(Map<String, String> parameters,
                                      ResponseEntity<OAuth2AccessToken> oAuth2AccessTokenResponseEntity) {
//        CompletableFuture.runAsync(() -> {
            // client id
            String clientId = parameters.get("client_id");
            // grant type
            String grantType = parameters.get("grant_type");

            if ("authorization_code".equals(grantType) || "refresh_token".equals(grantType)) {
                String token = oAuth2AccessTokenResponseEntity.getBody().getValue();
                OAuth2RefreshToken oAuth2RefreshToken = oAuth2AccessTokenResponseEntity.getBody().getRefreshToken();
                String refreshToken = Objects.isNull(oAuth2RefreshToken) ? null : oAuth2RefreshToken.getValue();
                saveOauthUserConnections(clientId, token, refreshToken);
                return;
            }
            if ("client_credentials".equals(grantType)) {
                String token = oAuth2AccessTokenResponseEntity.getBody().getValue();
                saveOauthUserConnections(clientId, token, null);
            }
//
//        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
//            log.error("save oauth_user_connections error", ex);
//            return null;
//        });
    }

    private void saveOauthUserConnections(String clientId, String token, String refreshToken) {
        Claims claims = JwtUtil.parseClaims(token, oAuth2Properties.getSignKey());
        String username = (String) claims.get("user_name");
        Double exp = (Double) claims.get("exp");
        // query record
        OauthUserConnectionsEntity oauthUserConnectionsEntity = oauthUserConnectionsDAO.getByClientIdAndEmail(clientId, username);
        // insert
        if (oauthUserConnectionsEntity == null) {
            oauthUserConnectionsEntity = buildInsert(clientId, username, exp);
            oauthUserConnectionsEntity.setAccessToken(token);
            oauthUserConnectionsEntity.setRefreshToken(refreshToken);
            oauthUserConnectionsDAO.save(oauthUserConnectionsEntity);
            return;
        }
        // update
        buildUpdate(oauthUserConnectionsEntity, exp);
        oauthUserConnectionsEntity.setAccessToken(token);
        oauthUserConnectionsEntity.setRefreshToken(refreshToken);
        oauthUserConnectionsDAO.updateById(oauthUserConnectionsEntity);
    }

    private OauthUserConnectionsEntity buildInsert(String clientId, String username, Double exp) {
        long now = System.currentTimeMillis();
        OauthUserConnectionsEntity oauthUserConnectionsEntity = new OauthUserConnectionsEntity();
        oauthUserConnectionsEntity.setClientId(clientId);
        oauthUserConnectionsEntity.setEmail(username);
        oauthUserConnectionsEntity.setTokenExpiresAt(exp);
        oauthUserConnectionsEntity.setStatus("active");
        oauthUserConnectionsEntity.setCreatedAt(now);
        oauthUserConnectionsEntity.setUpdatedAt(now);
        return oauthUserConnectionsEntity;
    }

    private void buildUpdate(OauthUserConnectionsEntity oauthUserConnectionsEntity, Double exp) {
        oauthUserConnectionsEntity.setTokenExpiresAt(exp);
        oauthUserConnectionsEntity.setStatus("active");
        oauthUserConnectionsEntity.setUpdatedAt(System.currentTimeMillis());
        oauthUserConnectionsDAO.updateById(oauthUserConnectionsEntity);
    }
}
