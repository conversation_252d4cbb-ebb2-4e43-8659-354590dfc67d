package com.mira.openapi.service.user;

import com.mira.openapi.model.vo.*;

import java.util.List;

/**
 * user service
 *
 * <AUTHOR>
 */
public interface IUserService {
    /**
     * 用户信息
     *
     * @return PatientInfoVO
     */
    UserInfoVO profile();

    /**
     * 用户当前周期信息
     *
     * @return CycleVO
     */
    CycleVO cycle();

    /**
     * 用户所有周期信息
     *
     * @return CycleVO List
     */
    List<CycleVO> cycles();

    /**
     * 用户测试数据，指定日期
     *
     * @param date  日期
     * @return HormoneVO List
     */
    List<HormoneVO> hormone(String date);

    /**
     * 用户测试数据，日期范围
     *
     * @param start_date 开始日期
     * @param end_date   截止日期
     * @return HormoneVO List
     */
    List<HormoneVO> hormones(String start_date, String end_date);

    /**
     * 用户所有测试数据
     *
     * @return HormoneVO List
     */
    List<HormoneVO> hormones();

    /**
     * 用户每日记录，指定日期
     *
     * @param date  日期
     * @return DailyLogVO
     */
    DailyLogVO dailyLog(String date);

    /**
     * 用户每日记录，日期范围
     *
     * @param start_date 开始日期
     * @param end_date   截止日期
     * @return DailyLogVO
     */
    List<DailyLogsVO> dailyLogs(String start_date, String end_date);

    /**
     * 用户所有的每日记录
     *
     * @return DailyLogVO List
     */
    List<DailyLogsVO> dailyLogs();
}
