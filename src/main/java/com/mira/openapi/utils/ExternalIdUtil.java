package com.mira.openapi.utils;

import com.mira.openapi.consts.ErrorMsgConst;
import com.mira.openapi.exception.OpenApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.util.UUID;

/**
 * External ID Util
 *
 * <AUTHOR>
 */
@Slf4j
public class ExternalIdUtil {
    // Base62 字符集：包含数字、大小写字母
    private static final String CHARSET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final BigInteger BASE = BigInteger.valueOf(62);

    /**
     * 生成 external id
     */
    public static String generateExternalID() {
        UUID uuid = UUID.randomUUID();
        return "U" + uuidToBase62(uuid);
    }

    /**
     * uuid -> base62
     */
    public static String uuidToBase62(UUID uuid) {
        ByteBuffer bb = ByteBuffer.wrap(new byte[16]);
        bb.putLong(uuid.getMostSignificantBits());
        bb.putLong(uuid.getLeastSignificantBits());

        return toBase62(new BigInteger(1, bb.array()));
    }

    /**
     * base62 -> uuid
     */
    public static UUID base62ToUuid(String base62) {
        byte[] bytes = fromBase62(base62).toByteArray();

        // 确保字节数组长度为16
        byte[] uuidBytes = new byte[16];
        int start = Math.max(0, bytes.length - 16);
        int length = Math.min(bytes.length, 16);

        System.arraycopy(bytes, start, uuidBytes, 16 - length, length);

        ByteBuffer bb = ByteBuffer.wrap(uuidBytes);
        long mostSigBits = bb.getLong();
        long leastSigBits = bb.getLong();

        return new UUID(mostSigBits, leastSigBits);
    }

    /**
     * bigInteger -> base62
     */
    private static String toBase62(BigInteger number) {
        if (number.compareTo(BigInteger.ZERO) < 0) {
            throw new OpenApiException(HttpStatus.INTERNAL_SERVER_ERROR.value(), ErrorMsgConst.INTERNAL_SERVER_ERROR);
        }
        if (number.equals(BigInteger.ZERO)) {
            return String.valueOf(CHARSET.charAt(0));
        }

        StringBuilder sb = new StringBuilder();
        while (number.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] divMod = number.divideAndRemainder(BASE);
            number = divMod[0];
            int digit = divMod[1].intValue();
            sb.insert(0, CHARSET.charAt(digit));
        }

        return sb.toString();
    }

    /**
     * base62 -> bigInteger
     */
    private static BigInteger fromBase62(String base62) {
        BigInteger result = BigInteger.ZERO;

        for (int i = 0; i < base62.length(); i++) {
            char c = base62.charAt(i);
            int digit = CHARSET.indexOf(c);

            if (digit == -1) {
                log.error("Invalid character in Base62 string: {}", c);
                throw new OpenApiException(HttpStatus.INTERNAL_SERVER_ERROR.value(), ErrorMsgConst.INTERNAL_SERVER_ERROR);
            }

            result = result.multiply(BASE).add(BigInteger.valueOf(digit));
        }

        return result;
    }
}
