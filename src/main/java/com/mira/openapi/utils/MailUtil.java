package com.mira.openapi.utils;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClientBuilder;
import com.amazonaws.services.simpleemail.model.*;
import com.mira.openapi.properties.SendMailProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

import java.util.Arrays;
import java.util.Map;

/**
 * email util
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MailUtil {
    private final SendMailProperties sendMailProperties;

    public MailUtil(SendMailProperties sendMailProperties) {
        this.sendMailProperties = sendMailProperties;
    }

    public void send(String toMail, String templateName, String subject, Map<String, String> emailVariable) {
        // 创建邮件模版
        String templateEngine = createMailTemplate(emailVariable, templateName);
        // 发送邮件
        sendHtmlMail(new String[]{toMail}, subject, templateEngine);
    }

    private String createMailTemplate(Map<String, String> variableMap, String templateName) {
        Context context = new Context();
        for (Map.Entry<String, String> entry : variableMap.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }

        TemplateEngine templateEngine = new TemplateEngine();
        ClassLoaderTemplateResolver templateResolver = new ClassLoaderTemplateResolver();
        templateResolver.setPrefix("templates/");
        templateResolver.setSuffix(".html");
        templateResolver.setTemplateMode(TemplateMode.HTML);
        templateResolver.setCharacterEncoding("UTF-8");
        templateResolver.setOrder(0);
        templateEngine.setTemplateResolver(templateResolver);
        return templateEngine.process(templateName, context);
    }

    private boolean sendHtmlMail(String[] to, String subject, String templateEngine) {
        boolean sendStatus = true;
        try {
            AWSCredentials credentials = new BasicAWSCredentials(sendMailProperties.getAccessKey(), sendMailProperties.getSecretKey());
            AWSCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);
            ClientConfiguration clientConfiguration = new ClientConfiguration();
            clientConfiguration.setMaxErrorRetry(sendMailProperties.getMaxErrorRetry());

            AmazonSimpleEmailService client = AmazonSimpleEmailServiceClientBuilder.standard()
                    .withClientConfiguration(clientConfiguration)
                    .withCredentials(credentialsProvider)
                    .withRegion(sendMailProperties.getRegion()).build();
            SendEmailRequest request = new SendEmailRequest()
                    .withDestination(new Destination().withToAddresses(to))
                    .withMessage(new Message()
                            .withBody(new Body().withHtml(new Content().withCharset("UTF-8").withData(templateEngine)))
                            .withSubject(new Content().withCharset("UTF-8").withData(subject)))
                    .withSource(sendMailProperties.getFromNickname() + "<" + sendMailProperties.getFromEmail() + ">");
            client.sendEmail(request);


            log.info("send html mail success! email:{}, subject:{}", to, subject);
        } catch (Exception e) {
            log.error("send html mail occurred unknown error! email:" + Arrays.toString(to) + ", subject:" + subject, e);
            sendStatus = false;
        }

        return sendStatus;
    }
}
