package com.mira.openapi.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.TreeMap;

/**
 * Request Util
 *
 * <AUTHOR>
 */
@Slf4j
public class RequestUtil {
    /**
     * 获取请求参数
     */
    public static String getParameters(HttpServletRequest request) {
        String method = request.getMethod();
        if ("GET".equals(method)) {
            return request.getQueryString();
        }
        if ("POST".equals(method)) {
            try {
                return IOUtils.toString(request.getInputStream(), request.getCharacterEncoding());
            } catch (IOException e) {
                return "get request body param error";
            }
        }

        return null;
    }

    /**
     * 获取请求参数
     */
    public static TreeMap<String, Object> getParameters(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();

        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        LocalVariableTableParameterNameDiscoverer parameterNameDiscoverer = new LocalVariableTableParameterNameDiscoverer();
        String[] paramNames = parameterNameDiscoverer.getParameterNames(method);
        TreeMap<String, Object> parameterMap = new TreeMap<>();
        if (args != null && paramNames != null) {
            for (int i = 0; i < args.length; ++i) {
                Object value = args[i];
                if (value instanceof MultipartFile) {
                    MultipartFile file = (MultipartFile) value;
                    value = file.getOriginalFilename();
                }

                if (!(value instanceof ServletRequest) && !(value instanceof ServletResponse)) {
                    parameterMap.put(paramNames[i], value);
                }
            }
        }

        return parameterMap;
    }

    /**
     * 获取对应参数的索引值
     *
     * @param paramName 参数名
     */
    public static int getParameterIndex(ProceedingJoinPoint joinPoint, String paramName) {
        Signature signature = joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();

        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        LocalVariableTableParameterNameDiscoverer parameterNameDiscoverer = new LocalVariableTableParameterNameDiscoverer();
        String[] paramNames = parameterNameDiscoverer.getParameterNames(method);
        if (args != null && paramNames != null) {
            for (int i = 0; i < args.length; ++i) {
                if (paramNames[i].equals(paramName)) {
                    return i;
                }
            }
        }

        return -1;
    }

    /**
     * 获取 request 对象
     *
     * @return HttpServletRequest
     */
    public static HttpServletRequest getRequest() {
        return ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
    }
}
