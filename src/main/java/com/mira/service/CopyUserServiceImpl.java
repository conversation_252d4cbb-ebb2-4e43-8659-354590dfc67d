package com.mira.service;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.mira.dal.dao.*;
import com.mira.dal.entity.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class CopyUserServiceImpl implements ICopyUserService {
    private final AppUserDAO appUserDAO;
    private final AppUserInfoDAO appUserInfoDAO;
    private final AppDataManualDAO appDataManualDAO;
    private final AppDataTemperatureDAO appDataTemperatureDAO;
    private final AppDataUploadDAO appDataUploadDAO;
    private final AppOvulationManualDAO appOvulationManualDAO;
    private final AppOvulationManualNoteDAO appOvulationManualNoteDAO;
    private final AppPregnantModeInfoV2DAO appPregnantModeInfoV2DAO;
    private final AppUserAlgorithmResultDAO appUserAlgorithmResultDAO;
    private final AppUserBbtBindDAO appUserBbtBindDAO;
    private final AppUserBindDAO appUserBindDAO;
    private final AppUserDiaryDAO appUserDiaryDAO;
    private final AppUserDiaryMedicationsDAO appUserDiaryMedicationsDAO;
    private final AppUserDiaryMoodsDAO appUserDiaryMoodsDAO;
    private final AppUserDiarySymptomsDAO appUserDiarySymptomsDAO;
    private final AppUserMenopauseResultDAO appUserMenopauseResultDAO;
    private final AppUserMenopauseSurveyDAO appUserMenopauseSurveyDAO;
    private final AppUserOnboardingPageViewDAO appUserOnboardingPageViewDAO;
    private final AppUserPeriodDAO appUserPeriodDAO;
    private final AppUserReminderDAO appUserReminderDAO;
    private final AppUserSexConfigDAO appUserSexConfigDAO;
    private final AppUserTemperatureDAO appUserTemperatureDAO;
    private final AppUserTestingScheduleDAO appUserTestingScheduleDAO;
    private final AppUserWandCountDAO appUserWandCountDAO;
    private final SysNotificationRecordDAO sysNotificationRecordDAO;
    private final SysNotificationTestingStatisticsDAO sysNotificationTestingStatisticsDAO;
    private final SysNotificationThresholdStatisticsDAO sysNotificationThresholdStatisticsDAO;
    private final UserPaywallDAO userPaywallDAO;
    private final UserPeriodEditNoteDAO userPeriodEditNoteDAO;
    private final OnboardingConditionDAO onboardingConditionDAO;

    @Override
    public Long copyUser(Long originUserId) {
        // prod env
        DynamicDataSourceContextHolder.push("prod");

        AppUserEntity appUserEntity = appUserDAO.getById(originUserId);
        if (appUserEntity == null) {
            throw new RuntimeException("用户不存在");
        }
        appUserEntity.setId(null);

        try {
            AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(originUserId);
            appUserInfoEntity.setId(null);

            List<AppDataManualEntity> appDataManualEntities = appDataManualDAO.listByUserId(originUserId);
            List<AppDataTemperatureEntity> appDataTemperatureEntities = appDataTemperatureDAO.listByUserId(originUserId);
            List<AppDataUploadEntity> appDataUploadEntities = appDataUploadDAO.listByUserId(originUserId);
            List<AppOvulationManualEntity> appOvulationManualEntities = appOvulationManualDAO.listByUserId(originUserId);
            List<AppOvulationManualNoteEntity> appOvulationManualNoteEntities = appOvulationManualNoteDAO.listByUserId(originUserId);
            List<AppPregnantModeInfoV2Entity> appPregnantModeInfoV2Entities = appPregnantModeInfoV2DAO.listByUserId(originUserId);
            List<AppUserAlgorithmResultEntity> appUserAlgorithmResultEntities = appUserAlgorithmResultDAO.listByUserId(originUserId);
            List<AppUserBbtBindEntity> appUserBbtBindEntities = appUserBbtBindDAO.listByUserId(originUserId);
            List<AppUserBindEntity> appUserBindEntities = appUserBindDAO.listByUserId(originUserId);
            List<AppUserDiaryEntity> appUserDiaryEntities = appUserDiaryDAO.listByUserId(originUserId);
            List<AppUserDiaryMedicationsEntity> appUserDiaryMedicationsEntities = appUserDiaryMedicationsDAO.listByUserId(originUserId);
            List<AppUserDiaryMoodsEntity> appUserDiaryMoodsEntities = appUserDiaryMoodsDAO.listByUserId(originUserId);
            List<AppUserDiarySymptomsEntity> appUserDiarySymptomsEntities = appUserDiarySymptomsDAO.listByUserId(originUserId);
            List<AppUserMenopauseResultEntity> appUserMenopauseResultEntities = appUserMenopauseResultDAO.listByUserId(originUserId);
            List<AppUserMenopauseSurveyEntity> appUserMenopauseSurveyEntities = appUserMenopauseSurveyDAO.listByUserId(originUserId);
            List<AppUserOnboardingPageViewEntity> appUserOnboardingPageViewEntities = appUserOnboardingPageViewDAO.listByUserId(originUserId);
            List<AppUserPeriodEntity> appUserPeriodEntities = appUserPeriodDAO.listByUserId(originUserId);
            List<AppUserReminderEntity> appUserReminderEntities = appUserReminderDAO.listByUserId(originUserId);
            List<AppUserSexConfigEntity> appUserSexConfigEntities = appUserSexConfigDAO.listByUserId(originUserId);
            List<AppUserTemperatureEntity> appUserTemperatureEntities = appUserTemperatureDAO.listByUserId(originUserId);
            List<AppUserTestingScheduleEntity> appUserTestingScheduleEntities = appUserTestingScheduleDAO.listByUserId(originUserId);
            List<AppUserWandCountEntity> appUserWandCountEntities = appUserWandCountDAO.listByUserId(originUserId);
            List<SysNotificationRecordEntity> sysNotificationRecordEntities = sysNotificationRecordDAO.listByUserId(originUserId);
            List<SysNotificationTestingStatisticsEntity> sysNotificationTestingStatisticsEntities = sysNotificationTestingStatisticsDAO.listByUserId(originUserId);
            List<SysNotificationThresholdStatisticsEntity> sysNotificationThresholdStatisticsEntities = sysNotificationThresholdStatisticsDAO.listByUserId(originUserId);
            List<UserPaywallEntity> userPaywallEntities = userPaywallDAO.listByUserId(originUserId);
            List<UserPeriodEditNoteEntity> userPeriodEditNoteEntities = userPeriodEditNoteDAO.listByUserId(originUserId);
            List<OnboardingConditionEntity> onboardingConditionEntities = onboardingConditionDAO.listByUserId(originUserId);

            // test env
            DynamicDataSourceContextHolder.push("test");

            appUserDAO.save(appUserEntity);
            Long userId = appUserEntity.getId();

            appUserInfoEntity.setUserId(userId);

            for (AppDataManualEntity appDataManualEntity : appDataManualEntities) {
                appDataManualEntity.setId(null);
                appDataManualEntity.setUserId(userId);
            }
            appDataManualDAO.saveBatch(appDataManualEntities);

            for (AppDataTemperatureEntity appDataTemperatureEntity : appDataTemperatureEntities) {
                appDataTemperatureEntity.setId(null);
                appDataTemperatureEntity.setUserId(userId);
            }
            appDataTemperatureDAO.saveBatch(appDataTemperatureEntities);

            for (AppDataUploadEntity appDataUploadEntity : appDataUploadEntities) {
                appDataUploadEntity.setId(null);
                appDataUploadEntity.setUserId(userId);
            }
            appDataUploadDAO.saveBatch(appDataUploadEntities);

            for (AppOvulationManualEntity appOvulationManualEntity : appOvulationManualEntities) {
                appOvulationManualEntity.setId(null);
                appOvulationManualEntity.setUserId(userId);
            }
            appOvulationManualDAO.saveBatch(appOvulationManualEntities);

            for (AppOvulationManualNoteEntity appOvulationManualNoteEntity : appOvulationManualNoteEntities) {
                appOvulationManualNoteEntity.setId(null);
                appOvulationManualNoteEntity.setUserId(userId);
            }
            appOvulationManualNoteDAO.saveBatch(appOvulationManualNoteEntities);

            for (AppPregnantModeInfoV2Entity appPregnantModeInfoV2Entity : appPregnantModeInfoV2Entities) {
                appPregnantModeInfoV2Entity.setId(null);
                appPregnantModeInfoV2Entity.setUserId(userId);
            }
            appPregnantModeInfoV2DAO.saveBatch(appPregnantModeInfoV2Entities);

            for (AppUserAlgorithmResultEntity appUserAlgorithmResultEntity : appUserAlgorithmResultEntities) {
                appUserAlgorithmResultEntity.setId(null);
                appUserAlgorithmResultEntity.setUserId(userId);
            }
            appUserAlgorithmResultDAO.saveBatch(appUserAlgorithmResultEntities);

            for (AppUserBbtBindEntity appUserBbtBindEntity : appUserBbtBindEntities) {
                appUserBbtBindEntity.setId(null);
                appUserBbtBindEntity.setUserId(userId);
            }
            appUserBbtBindDAO.saveBatch(appUserBbtBindEntities);

            for (AppUserBindEntity appUserBindEntity : appUserBindEntities) {
                appUserBindEntity.setId(null);
                appUserBindEntity.setUserId(userId);
            }
            appUserBindDAO.saveBatch(appUserBindEntities);

            for (AppUserDiaryEntity appUserDiaryEntity : appUserDiaryEntities) {
                appUserDiaryEntity.setId(null);
                appUserDiaryEntity.setUserId(userId);
            }
            appUserDiaryDAO.saveBatch(appUserDiaryEntities);

            for (AppUserDiaryMedicationsEntity appUserDiaryMedicationsEntity : appUserDiaryMedicationsEntities) {
                appUserDiaryMedicationsEntity.setId(null);
                appUserDiaryMedicationsEntity.setUserId(userId);
            }
            appUserDiaryMedicationsDAO.saveBatch(appUserDiaryMedicationsEntities);

            for (AppUserDiaryMoodsEntity appUserDiaryMoodsEntity : appUserDiaryMoodsEntities) {
                appUserDiaryMoodsEntity.setId(null);
                appUserDiaryMoodsEntity.setUserId(userId);
            }
            appUserDiaryMoodsDAO.saveBatch(appUserDiaryMoodsEntities);

            for (AppUserDiarySymptomsEntity appUserDiarySymptomsEntity : appUserDiarySymptomsEntities) {
                appUserDiarySymptomsEntity.setId(null);
                appUserDiarySymptomsEntity.setUserId(userId);
            }
            appUserDiarySymptomsDAO.saveBatch(appUserDiarySymptomsEntities);

            for (AppUserMenopauseResultEntity appUserMenopauseResultEntity : appUserMenopauseResultEntities) {
                appUserMenopauseResultEntity.setId(null);
                appUserMenopauseResultEntity.setUserId(userId);
            }
            appUserMenopauseResultDAO.saveBatch(appUserMenopauseResultEntities);

            for (AppUserMenopauseSurveyEntity appUserMenopauseSurveyEntity : appUserMenopauseSurveyEntities) {
                appUserMenopauseSurveyEntity.setId(null);
                appUserMenopauseSurveyEntity.setUserId(userId);
            }
            appUserMenopauseSurveyDAO.saveBatch(appUserMenopauseSurveyEntities);

            for (AppUserOnboardingPageViewEntity appUserOnboardingPageViewEntity : appUserOnboardingPageViewEntities) {
                appUserOnboardingPageViewEntity.setId(null);
                appUserOnboardingPageViewEntity.setUserId(userId);
            }
            appUserOnboardingPageViewDAO.saveBatch(appUserOnboardingPageViewEntities);

            for (AppUserPeriodEntity appUserPeriodEntity : appUserPeriodEntities) {
                appUserPeriodEntity.setId(null);
                appUserPeriodEntity.setUserId(userId);
            }
            appUserPeriodDAO.saveBatch(appUserPeriodEntities);

            for (AppUserReminderEntity appUserReminderEntity : appUserReminderEntities) {
                appUserReminderEntity.setId(null);
                appUserReminderEntity.setUserId(userId);
            }
            appUserReminderDAO.saveBatch(appUserReminderEntities);

            for (AppUserSexConfigEntity appUserSexConfigEntity : appUserSexConfigEntities) {
                appUserSexConfigEntity.setId(null);
                appUserSexConfigEntity.setUserId(userId);
            }
            appUserSexConfigDAO.saveBatch(appUserSexConfigEntities);

            for (AppUserTemperatureEntity appUserTemperatureEntity : appUserTemperatureEntities) {
                appUserTemperatureEntity.setId(null);
                appUserTemperatureEntity.setUserId(userId);
            }
            appUserTemperatureDAO.saveBatch(appUserTemperatureEntities);

            for (AppUserTestingScheduleEntity appUserTestingScheduleEntity : appUserTestingScheduleEntities) {
                appUserTestingScheduleEntity.setId(null);
                appUserTestingScheduleEntity.setUserId(userId);
            }
            appUserTestingScheduleDAO.saveBatch(appUserTestingScheduleEntities);

            for (AppUserWandCountEntity appUserWandCountEntity : appUserWandCountEntities) {
                appUserWandCountEntity.setId(null);
                appUserWandCountEntity.setUserId(userId);
            }
            appUserWandCountDAO.saveBatch(appUserWandCountEntities);

            for (SysNotificationRecordEntity sysNotificationRecordEntity : sysNotificationRecordEntities) {
                sysNotificationRecordEntity.setId(null);
                sysNotificationRecordEntity.setUserId(userId);
            }
            sysNotificationRecordDAO.saveBatch(sysNotificationRecordEntities);

            for (SysNotificationTestingStatisticsEntity sysNotificationTestingStatisticsEntity : sysNotificationTestingStatisticsEntities) {
                sysNotificationTestingStatisticsEntity.setId(null);
                sysNotificationTestingStatisticsEntity.setUserId(userId);
            }
            sysNotificationTestingStatisticsDAO.saveBatch(sysNotificationTestingStatisticsEntities);

            for (SysNotificationThresholdStatisticsEntity sysNotificationThresholdStatisticsEntity : sysNotificationThresholdStatisticsEntities) {
                sysNotificationThresholdStatisticsEntity.setId(null);
                sysNotificationThresholdStatisticsEntity.setUserId(userId);
            }
            sysNotificationThresholdStatisticsDAO.saveBatch(sysNotificationThresholdStatisticsEntities);

            for (UserPaywallEntity userPaywallEntity : userPaywallEntities) {
                userPaywallEntity.setId(null);
                userPaywallEntity.setUserId(userId);
            }
            userPaywallDAO.saveBatch(userPaywallEntities);

            for (UserPeriodEditNoteEntity userPeriodEditNoteEntity : userPeriodEditNoteEntities) {
                userPeriodEditNoteEntity.setId(null);
                userPeriodEditNoteEntity.setUserId(userId);
            }
            userPeriodEditNoteDAO.saveBatch(userPeriodEditNoteEntities);

            for (OnboardingConditionEntity onboardingConditionEntity : onboardingConditionEntities) {
                onboardingConditionEntity.setId(null);
                onboardingConditionEntity.setUserId(userId);
            }
            onboardingConditionDAO.saveBatch(onboardingConditionEntities);

            return userId;

        } catch (Exception e) {
            throw new RuntimeException("内部错误");
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }
}
