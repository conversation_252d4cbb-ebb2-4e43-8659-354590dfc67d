# WebClient 配置
webclient:
  auth:
    # 连接超时时间（毫秒）
    connect-timeout: 10000
    # 响应超时时间
    response-timeout: PT30S
    # 读取超时时间（秒）
    read-timeout: 30
    # 写入超时时间（秒）
    write-timeout: 30
    pool:
      # 最大连接数
      max-connections: 100
      # 连接最大空闲时间
      max-idle-time: PT30S
      # 连接最大生命周期
      max-life-time: PT60S
      # 等待获取连接的超时时间
      pending-acquire-timeout: PT10S
      # 后台清理空闲连接的间隔
      evict-in-background: PT30S

# Spring Cloud Gateway 优化配置
spring:
  cloud:
    gateway:
      httpclient:
        # 网关请求超时时间（毫秒）
        connect-timeout: 10000
        # 网关响应超时时间
        response-timeout: 30s
        pool:
          # 连接池类型，使用固定大小连接池
          type: fixed
          # 最大连接数
          max-connections: 200
          # 连接最大空闲时间
          max-idle-time: 30s
          # 连接最大生命周期
          max-life-time: 60s
          # 等待获取连接的超时时间
          acquire-timeout: 10000
      # 全局过滤器配置
      global-filter:
        # 启用请求日志
        request-logging:
          enabled: true
        # 启用响应日志
        response-logging:
          enabled: true

# Reactor Netty 配置
reactor:
  netty:
    # 启用连接池指标
    pool:
      metrics: true
    # I/O 线程数，默认为CPU核心数
    ioWorkerCount: 4
    # 是否启用原生传输
    native: true

# 日志配置
logging:
  level:
    # WebClient 相关日志
    org.springframework.web.reactive.function.client: DEBUG
    # Reactor Netty 相关日志
    reactor.netty: INFO
    # 连接池相关日志
    reactor.netty.resources: DEBUG
    # Gateway 过滤器日志
    com.mira.gateway.filter: INFO
    # 根日志级别
    root: INFO

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    # 启用连接池指标
    web:
      client:
        request:
          autotime:
            enabled: true
