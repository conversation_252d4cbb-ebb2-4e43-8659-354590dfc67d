spring:
  cloud:
    nacos:
      # 配置
      config:
        server-addr: 172.31.15.200:8848,172.31.29.111:8848,172.31.26.127:8848 # 地址
        namespace: 948055f0-b0df-4fe1-b389-b014c9600d62 # 命名空间
        group: DEFAULT_GROUP # 组
        file-extension: yaml # 扩展文件格式
        # 通用配置
        shared-configs[0]:
          data_id: common.yaml
          group: DEFAULT_GROUP
          refresh: true
        # 字典表
        shared-configs[1]:
          data_id: sys_dict.yaml
          group: DEFAULT_GROUP
          refresh: true
        # 黑白名单
        shared-configs[2]:
          data_id: api_black_white.yaml
          group: DEFAULT_GROUP
          refresh: true
        username: nacos
        password: <EMAIL>
      # 服务注册发现
      discovery:
        server-addr: 172.31.15.200:8848,172.31.29.111:8848,172.31.26.127:8848
        namespace: 948055f0-b0df-4fe1-b389-b014c9600d62
        group: DEFAULT_GROUP
        username: nacos
        password: <EMAIL>

logging:
  config: classpath:logback/logback-${spring.profiles.active}.xml