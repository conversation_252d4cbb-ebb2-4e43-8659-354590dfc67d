spring:
  cloud:
    nacos:
      # 配置
      config:
        server-addr: *************:8848,*************:8848,*************:8848 # 地址
        namespace: 948055f0-b0df-4fe1-b389-b014c9600d62 # 命名空间
        group: DEFAULT_GROUP # 组
        file-extension: yaml # 扩展文件格式
        username: nacos
        password: <EMAIL>
      # 服务注册发现
      discovery:
        server-addr: *************:8848,*************:8848,*************:8848
        namespace: 948055f0-b0df-4fe1-b389-b014c9600d62
        group: DEFAULT_GROUP
        username: nacos
        password: <EMAIL>
  # zipkin
  zipkin:
    base-url: http://*************:9411
    sender:
      type: web
    discovery-client-enabled: false

host: https://open.quanovate.com