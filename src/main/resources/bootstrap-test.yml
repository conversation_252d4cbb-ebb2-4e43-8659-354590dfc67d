spring:
  cloud:
    nacos:
      # 配置
      config:
        server-addr: *************:8848 # 地址
        namespace: 12d924dc-f63f-4b30-b1c2-c660a810bfb0 # 命名空间
        group: DEFAULT_GROUP # 组
        file-extension: yaml # 扩展文件格式
        username: nacos
        password: <EMAIL>
      # 服务注册发现
      discovery:
        server-addr: *************:8848
        namespace: 12d924dc-f63f-4b30-b1c2-c660a810bfb0
        group: DEFAULT_GROUP
        username: nacos
        password: <EMAIL>
  zipkin:
    base-url: http://*************:9411
    sender:
      type: web
    discovery-client-enabled: false
logging:
  config: classpath:logback/logback-${spring.profiles.active}.xml