spring:
  application:
    name: mira-gateway
  profiles:
    active: dev
  cloud:
    gateway:
      discovery:
        # 开启自动路由，根据服务名转发请求
        locator:
          enabled: true
      httpclient:
        # 网关请求超时时间
        connect-timeout: 20000
        # 网关响应超时时间
        response-timeout: 60s
        pool:
          # 连接池类型，动态
          type: elastic
          # 最大连接数
          max-connections: 500
          # 连接最大空闲
          max-idle-time: 28s
          # 连接最大生命周期
          max-life-time: 180s
    loadbalancer:
      nacos:
        enabled: true
  sleuth:
    traceId128: true
    sampler:
      probability: 0.1

server:
  port: 8000
  shutdown: graceful