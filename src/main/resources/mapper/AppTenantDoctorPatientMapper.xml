<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.openapi.dal.mapper.AppTenantDoctorPatientMapper">
    <delete id="removeByDoctorId">
        delete from app_tenant_doctor_patient where doctor_id=#{doctorId}
    </delete>

    <delete id="removeByPatientId">
        delete from app_tenant_doctor_patient where patient_id=#{patientId}
    </delete>

    <delete id="removeByDoctorIdAndPatientId">
        delete from app_tenant_doctor_patient where doctor_id=#{doctorId} and patient_id=#{patientId}
    </delete>
</mapper>
