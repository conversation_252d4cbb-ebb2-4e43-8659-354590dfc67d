<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.openapi.dal.mapper.AppTenantPatientMapper">
    <resultMap id="userInfoVO" type="com.mira.openapi.model.vo.UserInfoVO">
        <result column="nickname" property="nickname"/>
        <result column="init_email" property="email"/>
        <result column="birth_year" property="birth_year"/>
        <result column="birth_month" property="birth_month"/>
        <result column="birth_of_day" property="birth_day"/>
        <result column="avatar" property="avatar"/>
        <result column="time_zone" property="time_zone"/>
    </resultMap>
    
    <delete id="removePatient">
        delete from app_tenant_patient where id=#{id}
    </delete>

    <select id="listByTenantCode" resultMap="userInfoVO">
        select patient.nickname,
               patient.init_email,
               info.birth_year,
               info.birth_month,
               info.birth_of_day,
               info.avatar,
               info.time_zone
        from app_tenant_patient patient
                 join app_user_info info
                      on patient.user_id = info.user_id
        where patient.tenant_code = #{tenantCode}
          and patient.deleted = 0
          and info.deleted = 0
    </select>

    <select id="listByTenantCodeAndDoctor" resultMap="userInfoVO">
        select patient.nickname,
               patient.init_email,
               info.birth_year,
               info.birth_month,
               info.birth_of_day,
               info.avatar,
               info.time_zone
        from app_tenant_patient patient
                 join app_tenant_doctor_patient releated
                      on patient.id = releated.patient_id
                 join app_user_info info
                      on patient.user_id = info.user_id
        where patient.tenant_code = #{tenantCode}
          and patient.deleted = 0
          and info.deleted = 0
          and releated.doctor_id = #{doctorId};
    </select>
</mapper>
