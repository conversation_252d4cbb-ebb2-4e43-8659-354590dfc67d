var baseUri = 'https://open.quanovate.com';
// var baseUri = 'https://open.miracare.dev';
// var baseUri = 'http://localhost:9100';

var pageType = getUrlParams('type')
pageType = parseInt(pageType) || 0
var userHash = getUrlParams('hash') || ''
var email = getUrlParams('email') || ''

var baseObj = {
  baseUri: baseUri,
  userHash: userHash,
  pageType: pageType,
  email: email,
  isPC: judgeIsPC(),
  clientType: getClientType(),
  showToast: showToast,
};

// console.log(baseObj)

// Judge PC or Mobile
function judgeIsPC()  {
  var userAgentInfo = navigator.userAgent;
  var Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod'];
  var flag = true;
  for (var i = 0; i < Agents.length; i++) {
    if (userAgentInfo.indexOf(Agents[i]) !== -1) {
      flag = false;
      break;
    }
  }
  return flag;
}

// Get the client type: ios or android
function getClientType() {
  let u = navigator.userAgent;
  let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android client  
  let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios client
  let ClientType = {
    isAndroid: isAndroid,
    isIOS: isIOS
  };
  return ClientType;
};

// Get param from url
function getUrlParams(variable) {
	var query = window.location.search.substring(1);
	var vars = query.split("&");
	for (var i = 0; i < vars.length; i++) {
		var pair = vars[i].split("=");
		if (pair[0] == variable) {
			return pair[1];
		}
	}
	return (false);
}

// Show toast
function showToast(toastText, time) {
  if (!document.querySelector('.toast_box')) {
    var toastStr = '<div class="toast_box"><div class="toast_content"><p>' + toastText + '</p></div></div>';
    $('body').append(toastStr);
  } else {
    $('.toast_box .toast_content p').text(toastText);
  }
  $('.toast_box').addClass('show');
  time = time || 2000;
  setTimeout(function () {
    $('.toast_box').removeClass('show');
  }, time);
}